<?xml version="1.0" encoding="utf-8"?>
<!--
  ~ Copyright (C) 2021 The Android Open Source Project
  ~
  ~ Licensed under the Apache License, Version 2.0 (the "License");
  ~ you may not use this file except in compliance with the License.
  ~ You may obtain a copy of the License at
  ~
  ~      http://www.apache.org/licenses/LICENSE-2.0
  ~
  ~ Unless required by applicable law or agreed to in writing, software
  ~ distributed under the License is distributed on an "AS IS" BASIS,
  ~ WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
  ~ See the License for the specific language governing permissions and
  ~ limitations under the License.
  -->
<vector xmlns:android="http://schemas.android.com/apk/res/android"
    android:width="66dp"
    android:height="256dp"
    android:viewportWidth="66"
    android:viewportHeight="256">
  <path
      android:pathData="m33,0h-5.143,-5.141 -5.142,-2.57 -0.644,-0.321c-0.108,0.001 -0.214,0.002 -0.321,0.003 -0.428,0.007 -0.859,0.025 -1.286,0.054 -0.852,0.06 -1.706,0.169 -2.545,0.34 -0.839,0.171 -1.665,0.405 -2.461,0.717 -0.797,0.312 -1.562,0.7 -2.272,1.173 -0.71,0.472 -1.362,1.028 -1.934,1.654 -0.573,0.626 -1.066,1.323 -1.473,2.064 -0.408,0.741 -0.732,1.523 -0.983,2.329 -0.502,1.612 -0.717,3.298 -0.757,4.981 -0.003,0.105 -0.004,0.21 -0.006,0.316 -0.001,0.105 -0.002,0.21 -0.002,0.315v0.632,1.263 0.632,0.632 1.262,5.053 10.105,20.209 80.84,80.292 7.607,10.104 5.053,1.263 0.632,0.631 0.632,0.631 0.632c0,0.211 0.002,0.421 0.007,0.632 0.02,0.843 0.084,1.682 0.205,2.516 0.122,0.835 0.3,1.659 0.552,2.465 0.252,0.807 0.575,1.588 0.983,2.329 0.408,0.741 0.901,1.437 1.473,2.064 0.573,0.626 1.225,1.182 1.934,1.654 0.71,0.473 1.476,0.862 2.272,1.173 0.796,0.311 1.622,0.545 2.461,0.717 0.84,0.172 1.691,0.28 2.545,0.34 0.427,0.03 0.858,0.048 1.286,0.054 0.214,0.004 0.427,0.003 0.642,0.003h0.643,1.285 1.286,5.14 10.284,10.284 5.143,2.57 0.643c0.215,0 0.428,0.001 0.642,-0.003 0.429,-0.007 0.857,-0.025 1.284,-0.054 0.854,-0.06 1.707,-0.169 2.546,-0.34 0.839,-0.172 1.665,-0.406 2.461,-0.717 0.797,-0.311 1.562,-0.7 2.272,-1.173 0.71,-0.472 1.362,-1.028 1.934,-1.654 0.573,-0.627 1.066,-1.323 1.473,-2.064 0.408,-0.741 0.732,-1.523 0.984,-2.329 0.251,-0.805 0.43,-1.631 0.552,-2.465 0.122,-0.833 0.186,-1.673 0.205,-2.516 0.003,-0.105 0.004,-0.211 0.006,-0.316 0.001,-0.105 0.002,-0.212 0.002,-0.316v-0.631,-1.264 -0.631,-0.632 -1.262,-5.053 -10.105,-7.606 -80.292,-80.84 -20.209,-10.106 -5.051,-1.264 -0.632,-0.632 -0.63,-0.632 -0.632c0,-0.21 -0.002,-0.421 -0.007,-0.631 -0.02,-0.841 -0.084,-1.684 -0.206,-2.517 -0.122,-0.834 -0.3,-1.659 -0.552,-2.465 -0.252,-0.806 -0.575,-1.588 -0.983,-2.329 -0.408,-0.741 -0.901,-1.437 -1.473,-2.064 -0.572,-0.626 -1.225,-1.182 -1.934,-1.654 -0.71,-0.472 -1.476,-0.861 -2.272,-1.173 -0.796,-0.311 -1.622,-0.545 -2.461,-0.716 -0.839,-0.172 -1.691,-0.281 -2.545,-0.341 -0.427,-0.03 -0.859,-0.047 -1.285,-0.054 -0.106,-0.002 -0.214,-0.003 -0.321,-0.003h-0.322,-0.642 -1.286,-1.287 -5.14,-5.142z"
      android:fillColor="#3b4043"
      android:fillType="evenOdd"/>
  <path
      android:pathData="m58,131c0,3.866 -3.134,7 -7,7 -3.866,0 -7,-3.134 -7,-7 0,-3.866 3.134,-7 7,-7 3.866,0 7,3.134 7,7z"
      android:fillColor="#202124"
      android:fillType="evenOdd"/>
  <path
      android:pathData="m40,152.555c0,4.112 -3.134,7.445 -7,7.445s-7,-3.333 -7,-7.445v-21.11c0,-4.112 3.134,-7.445 7,-7.445s7,3.333 7,7.445z"
      android:fillColor="#202124"
      android:fillType="evenOdd"/>
  <path
      android:pathData="m22,131c0,3.866 -3.134,7 -7,7s-7,-3.134 -7,-7c0,-3.866 3.134,-7 7,-7s7,3.134 7,7z"
      android:fillColor="#202124"
      android:fillType="evenOdd"/>
  <path
      android:pathData="m58,111c0,3.866 -3.134,7 -7,7 -3.866,0 -7,-3.134 -7,-7 0,-3.866 3.134,-7 7,-7 3.866,0 7,3.134 7,7z"
      android:fillColor="#202124"
      android:fillType="evenOdd"/>
  <path
      android:pathData="m40,111c0,3.866 -3.134,7 -7,7s-7,-3.134 -7,-7c0,-3.866 3.134,-7 7,-7s7,3.134 7,7z"
      android:fillColor="#202124"
      android:fillType="evenOdd"/>
  <path
      android:pathData="m22,111c0,3.866 -3.134,7 -7,7s-7,-3.134 -7,-7c0,-3.866 3.134,-7 7,-7s7,3.134 7,7z"
      android:fillColor="#202124"
      android:fillType="evenOdd"/>
  <path
      android:pathData="m33,48c13.807,0 25,11.193 25,25s-11.193,25 -25,25c-13.807,0 -25,-11.193 -25,-25s11.193,-25 25,-25z"
      android:fillColor="#202124"
      android:fillType="evenOdd"/>
  <path
      android:pathData="m22,73c0,6.075 4.925,11 11,11 6.075,0 11,-4.925 11,-11 0,-6.075 -4.925,-11 -11,-11 -6.075,0 -11,4.925 -11,11z"
      android:fillColor="#3b4043"
      android:fillType="evenOdd"/>
  <path
      android:pathData="m58,35c0,3.866 -3.134,7 -7,7 -3.866,0 -7,-3.134 -7,-7 0,-3.866 3.134,-7 7,-7 3.866,0 7,3.134 7,7z"
      android:fillColor="#202124"
      android:fillType="evenOdd"/>
  <path
      android:pathData="m40,35c0,3.866 -3.134,7 -7,7s-7,-3.134 -7,-7c0,-3.866 3.134,-7 7,-7s7,3.134 7,7z"
      android:fillColor="#202124"
      android:fillType="evenOdd"/>
  <path
      android:pathData="m22,35c0,3.866 -3.134,7 -7,7s-7,-3.134 -7,-7 3.134,-7 7,-7 7,3.134 7,7z"
      android:fillColor="#202124"
      android:fillType="evenOdd"/>
  <path
      android:pathData="m52.75,18c-0.966,0 -1.75,-0.895 -1.75,-2 0,-1.105 0.783,-2 1.75,-2h3.5c0.966,0 1.75,0.895 1.75,2 0,1.104 -0.784,2 -1.75,2z"
      android:fillColor="#202124"
      android:fillType="evenOdd"/>
  <path
      android:pathData="m46,18c-1.104,0 -2,-0.896 -2,-2s0.896,-2 2,-2 2,0.896 2,2 -0.896,2 -2,2z"
      android:fillColor="#202124"
      android:fillType="evenOdd"/>
  <path
      android:pathData="m22,15c0,3.866 -3.134,7 -7,7s-7,-3.134 -7,-7c0,-3.866 3.134,-7 7,-7s7,3.134 7,7z"
      android:fillColor="#202124"
      android:fillType="evenOdd"/>
  <path
      android:pathData="m24.956,224.519h1.496c0,0.529 0.001,1.059 0.001,1.588 0,0.427 0.346,0.773 0.771,0.773 0.425,0 0.77,-0.357 0.77,-0.782v-7.796h1.496v7.534c0,0.908 -0.59,1.724 -2.267,1.724 -1.678,0 -2.267,-1.066 -2.267,-1.724z"
      android:fillColor="#202124"
      android:fillType="evenOdd"/>
  <path
      android:pathData="m32.936,222.229c-0.272,-0.001 -0.544,-0.002 -0.816,-0.003v-3.063h0.816c0.544,0 0.68,0.114 0.68,1.544 0,1.429 -0.137,1.523 -0.68,1.523m0.09,4.447c-0.302,-0.001 -0.604,-0.001 -0.907,-0.002v-3.675h0.907c0.59,0 0.59,0.419 0.59,1.849 0,1.429 0,1.829 -0.59,1.829m2.085,-5.335v-1.555c0,-1.009 -0.918,-1.486 -1.495,-1.486h-2.993v9.123h2.993c1.121,0 1.495,-0.727 1.495,-1.192v-2.734c0,-0.386 -0.397,-0.896 -0.985,-0.896 0.588,0 0.985,-0.806 0.985,-1.26"
      android:fillColor="#202124"
      android:fillType="evenOdd"/>
  <path
      android:pathData="m40.733,227.424h-4.533v-9.123h1.496v7.806c0,0.426 0.346,0.773 0.771,0.773 0.426,0 0.771,-0.347 0.771,-0.773 0,-0.53 -0.001,-1.06 -0.001,-1.589h1.497z"
      android:fillColor="#202124"
      android:fillType="evenOdd"/>
  <path
      android:pathData="m27.087,218.301 l-1.383,3.154 -1.383,-3.154z"
      android:fillColor="#202124"
      android:fillType="evenOdd"/>
  <path
      android:pathData="m25.705,221.792c0.478,0 0.865,0.387 0.865,0.867 0,0.478 -0.387,0.867 -0.865,0.867 -0.478,0 -0.866,-0.388 -0.866,-0.867 0,-0.479 0.388,-0.867 0.866,-0.867"
      android:fillColor="#202124"
      android:fillType="evenOdd"/>
</vector>
