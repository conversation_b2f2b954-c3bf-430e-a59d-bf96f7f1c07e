// AUTOGENERATED - DO NOT EDIT
// ---------------------------
// This file has been generated by
// AOSP://external/perfetto/tools/gen_merged_protos
// merging the perfetto config protos.
// This fused proto is intended to be copied in:
//  - Android tree, for statsd.
//  - Google internal repos.

syntax = "proto2";

package perfetto.protos;

// Begin of protos/perfetto/common/gpu_counter_descriptor.proto

// Description of GPU counters.
// This message is sent by a GPU counter producer to specify the counters
// available in the hardware.
message GpuCounterDescriptor {
  // Logical groups for a counter.  This is used in the UI to present the
  // related counters together.
  enum GpuCounterGroup {
    UNCLASSIFIED = 0;
    SYSTEM = 1;
    VERTICES = 2;
    FRAGMENTS = 3;
    PRIMITIVES = 4;
    // Includes counters relating to caching and bandwidth.
    MEMORY = 5;
    COMPUTE = 6;
  }

  message GpuCounterSpec {
    optional uint32 counter_id = 1;
    optional string name = 2;
    optional string description = 3;
    // MeasureUnit unit (deprecated)
    reserved 4;
    oneof peak_value {
      int64 int_peak_value = 5;
      double double_peak_value = 6;
    }
    repeated MeasureUnit numerator_units = 7;
    repeated MeasureUnit denominator_units = 8;
    optional bool select_by_default = 9;
    repeated GpuCounterGroup groups = 10;
  }
  repeated GpuCounterSpec specs = 1;

  // Allow producer to group counters into block to represent counter islands.
  // A capacity may be specified to indicate the number of counters that can be
  // enable simultaneously in that block.
  message GpuCounterBlock {
    // required. Unique ID for the counter group.
    optional uint32 block_id = 1;
    // optional. Number of counters supported by the block. No limit if unset.
    optional uint32 block_capacity = 2;
    // optional. Name of block.
    optional string name = 3;
    // optional. Description for the block.
    optional string description = 4;
    // list of counters that are part of the block.
    repeated uint32 counter_ids = 5;
  }
  repeated GpuCounterBlock blocks = 2;

  // optional.  Minimum sampling period supported by the producer in
  // nanoseconds.
  optional uint64 min_sampling_period_ns = 3;

  // optional.  Maximum sampling period supported by the producer in
  // nanoseconds.
  optional uint64 max_sampling_period_ns = 4;

  // optional.  The producer supports counter sampling by instrumenting the
  // command buffer.
  optional bool supports_instrumented_sampling = 5;

  // next id: 41
  enum MeasureUnit {
    NONE = 0;

    BIT = 1;
    KILOBIT = 2;
    MEGABIT = 3;
    GIGABIT = 4;
    TERABIT = 5;
    PETABIT = 6;

    BYTE = 7;
    KILOBYTE = 8;
    MEGABYTE = 9;
    GIGABYTE = 10;
    TERABYTE = 11;
    PETABYTE = 12;

    HERTZ = 13;
    KILOHERTZ = 14;
    MEGAHERTZ = 15;
    GIGAHERTZ = 16;
    TERAHERTZ = 17;
    PETAHERTZ = 18;

    NANOSECOND = 19;
    MICROSECOND = 20;
    MILLISECOND = 21;
    SECOND = 22;
    MINUTE = 23;
    HOUR = 24;

    VERTEX = 25;
    PIXEL = 26;
    TRIANGLE = 27;
    PRIMITIVE = 38;
    FRAGMENT = 39;

    MILLIWATT = 28;
    WATT = 29;
    KILOWATT = 30;

    JOULE = 31;
    VOLT = 32;
    AMPERE = 33;

    CELSIUS = 34;
    FAHRENHEIT = 35;
    KELVIN = 36;

    // Values should be out of 100.
    PERCENT = 37;

    INSTRUCTION = 40;
  }
}

// End of protos/perfetto/common/gpu_counter_descriptor.proto

// Begin of protos/perfetto/common/track_event_descriptor.proto

message TrackEventCategory {
  optional string name = 1;
  optional string description = 2;
  repeated string tags = 3;
}

message TrackEventDescriptor {
  repeated TrackEventCategory available_categories = 1;
}

// End of protos/perfetto/common/track_event_descriptor.proto

// Begin of protos/perfetto/common/data_source_descriptor.proto

// This message is sent from Producer(s) to the tracing Service when registering
// to advertise their capabilities. It describes the structure of tracing
// protos that will be produced by the data source and the supported filters.
message DataSourceDescriptor {
  // e.g., "linux.ftrace", "chromium.tracing"
  optional string name = 1;

  // When true the data source is expected to ack the stop request through the
  // NotifyDataSourceStopped() IPC. This field has been introduced after
  // Android P in Jul 2018 and is not supported on older versions.
  optional bool will_notify_on_stop = 2;

  // When true the data source is expected to ack the start request through the
  // NotifyDataSourceStarted() IPC. This field has been introduced after
  // Android P in March 2019 and is not supported on older versions.
  optional bool will_notify_on_start = 3;

  // If true, opt into receiving the ClearIncrementalState() IPC. This should be
  // set if the data source writes packets that refer to previous trace
  // contents, and knows how to stop referring to the already-emitted data.
  optional bool handles_incremental_state_clear = 4;

  // Optional specification about available GPU counters.
  optional GpuCounterDescriptor gpu_counter_descriptor = 5 [lazy = true];

  optional TrackEventDescriptor track_event_descriptor = 6 [lazy = true];
}

// End of protos/perfetto/common/data_source_descriptor.proto

// Begin of protos/perfetto/common/tracing_service_state.proto

// Reports the state of the tracing service. Used to gather details about the
// data sources connected.
// See ConsumerPort::QueryServiceState().
message TracingServiceState {
  // Describes a producer process.
  message Producer {
    // Unique ID of the producer (monotonic counter).
    optional int32 id = 1;

    // Typically matches the process name.
    optional string name = 2;

    // Unix uid of the remote process.
    optional int32 uid = 3;
  }

  // Describes a data source registered by a producer. Data sources are listed
  // regardless of the fact that they are being used or not.
  message DataSource {
    // Descriptor passed by the data source when calling RegisterDataSource().
    optional DataSourceDescriptor ds_descriptor = 1;

    // ID of the producer, as per Producer.id.
    optional int32 producer_id = 2;
  }

  // Lists all the producers connected.
  repeated Producer producers = 1;

  // Lists the data sources available.
  repeated DataSource data_sources = 2;

  // Total number of tracing sessions.
  optional int32 num_sessions = 3;

  // Number of tracing sessions in the started state. Always <= num_sessions.
  optional int32 num_sessions_started = 4;
}

// End of protos/perfetto/common/tracing_service_state.proto

// Begin of protos/perfetto/common/builtin_clock.proto

enum BuiltinClock {
  BUILTIN_CLOCK_UNKNOWN = 0;
  BUILTIN_CLOCK_REALTIME = 1;
  BUILTIN_CLOCK_REALTIME_COARSE = 2;
  BUILTIN_CLOCK_MONOTONIC = 3;
  BUILTIN_CLOCK_MONOTONIC_COARSE = 4;
  BUILTIN_CLOCK_MONOTONIC_RAW = 5;
  BUILTIN_CLOCK_BOOTTIME = 6;
  BUILTIN_CLOCK_MAX_ID = 63;

  reserved 7, 8;
}

// End of protos/perfetto/common/builtin_clock.proto

// Begin of protos/perfetto/common/android_log_constants.proto

// Values from NDK's android/log.h.
enum AndroidLogId {
  // MAIN.
  LID_DEFAULT = 0;

  LID_RADIO = 1;
  LID_EVENTS = 2;
  LID_SYSTEM = 3;
  LID_CRASH = 4;
  LID_STATS = 5;
  LID_SECURITY = 6;
  LID_KERNEL = 7;
}

enum AndroidLogPriority {
  PRIO_UNSPECIFIED = 0;

  // _DEFAULT, but should never be seen in logs.
  PRIO_UNUSED = 1;

  PRIO_VERBOSE = 2;
  PRIO_DEBUG = 3;
  PRIO_INFO = 4;
  PRIO_WARN = 5;
  PRIO_ERROR = 6;
  PRIO_FATAL = 7;
}

// End of protos/perfetto/common/android_log_constants.proto

// Begin of protos/perfetto/config/android/android_log_config.proto

message AndroidLogConfig {
  repeated AndroidLogId log_ids = 1;

  // Was |poll_ms|, deprecated.
  reserved 2;

  // If set ignores all log messages whose prio is < the given value.
  optional AndroidLogPriority min_prio = 3;

  // If non-empty ignores all log messages whose tag doesn't match one of the
  // specified values.
  repeated string filter_tags = 4;
}

// End of protos/perfetto/config/android/android_log_config.proto

// Begin of protos/perfetto/config/android/android_polled_state_config.proto

// Data source that polls for display state.
message AndroidPolledStateConfig {
  // Frequency of polling. If absent the state will be recorded once, at the
  // start of the trace.
  // This is required to be > 100ms to avoid excessive CPU usage.
  optional uint32 poll_ms = 1;
}

// End of protos/perfetto/config/android/android_polled_state_config.proto

// Begin of protos/perfetto/config/android/packages_list_config.proto

// Data source that lists details (such as version code) about packages on an
// Android device.
message PackagesListConfig {
  // If not empty, emit info about only the following list of package names
  // (exact match, no regex). Otherwise, emit info about all packages.
  repeated string package_name_filter = 1;
}

// End of protos/perfetto/config/android/packages_list_config.proto

// Begin of protos/perfetto/config/chrome/chrome_config.proto

message ChromeConfig {
  optional string trace_config = 1;

  // When enabled, the data source should only fill in fields in the output that
  // are not potentially privacy sensitive.
  optional bool privacy_filtering_enabled = 2;

  // Instead of emitting binary protobuf, convert the trace data to the legacy
  // JSON format. Note that the trace data will still be returned as a series of
  // TracePackets, but the embedded data will be JSON instead of serialized
  // protobuf.
  optional bool convert_to_legacy_json = 3;

  // Priority of the tracing session client. A higher priority session may
  // preempt a lower priority one in configurations where concurrent sessions
  // aren't supported.
  enum ClientPriority {
    UNKNOWN = 0;
    BACKGROUND = 1;
    USER_INITIATED = 2;
  }
  optional ClientPriority client_priority = 4;
}

// End of protos/perfetto/config/chrome/chrome_config.proto

// Begin of protos/perfetto/config/ftrace/ftrace_config.proto

message FtraceConfig {
  repeated string ftrace_events = 1;
  repeated string atrace_categories = 2;
  repeated string atrace_apps = 3;
  // *Per-CPU* buffer size.
  optional uint32 buffer_size_kb = 10;
  optional uint32 drain_period_ms = 11;

  // Configuration for compact encoding of scheduler events. When enabled (and
  // recording the relevant ftrace events), specific high-volume events are
  // encoded in a denser format than normal.
  message CompactSchedConfig {
    // If true, and sched_switch or sched_waking ftrace events are enabled,
    // record those events in the compact format.
    optional bool enabled = 1;
  }
  optional CompactSchedConfig compact_sched = 12;
}

// End of protos/perfetto/config/ftrace/ftrace_config.proto

// Begin of protos/perfetto/config/gpu/gpu_counter_config.proto

message GpuCounterConfig {
  // Desired sampling interval for counters.
  optional uint64 counter_period_ns = 1;

  // List of counters to be sampled. Counter IDs correspond to the ones
  // described in GpuCounterSpec in the data source descriptor.
  repeated uint32 counter_ids = 2;

  // Sample counters by instrumenting command buffers.
  optional bool instrumented_sampling = 3;

  // Fix gpu clock rate during trace session.
  optional bool fix_gpu_clock = 4;
}

// End of protos/perfetto/config/gpu/gpu_counter_config.proto

// Begin of protos/perfetto/config/gpu/vulkan_memory_config.proto

message VulkanMemoryConfig {
  // Tracking driver memory usage events
  optional bool track_driver_memory_usage = 1;

  // Tracking device memory usage events
  optional bool track_device_memory_usage = 2;
}

// End of protos/perfetto/config/gpu/vulkan_memory_config.proto

// Begin of protos/perfetto/config/inode_file/inode_file_config.proto

message InodeFileConfig {
  message MountPointMappingEntry {
    optional string mountpoint = 1;
    repeated string scan_roots = 2;
  }

  // How long to pause between batches.
  optional uint32 scan_interval_ms = 1;

  // How long to wait before the first scan in order to accumulate inodes.
  optional uint32 scan_delay_ms = 2;

  // How many inodes to scan in one batch.
  optional uint32 scan_batch_size = 3;

  // Do not scan for inodes not found in the static map.
  optional bool do_not_scan = 4;

  // If non-empty, only scan inodes corresponding to block devices named in
  // this list.
  repeated string scan_mount_points = 5;

  // When encountering an inode belonging to a block device corresponding
  // to one of the mount points in this map, scan its scan_roots instead.
  repeated MountPointMappingEntry mount_point_mapping = 6;
}

// End of protos/perfetto/config/inode_file/inode_file_config.proto

// Begin of protos/perfetto/config/power/android_power_config.proto

message AndroidPowerConfig {
  enum BatteryCounters {
    BATTERY_COUNTER_UNSPECIFIED = 0;

    // Coulomb counter.
    BATTERY_COUNTER_CHARGE = 1;

    // Charge (%).
    BATTERY_COUNTER_CAPACITY_PERCENT = 2;

    // Instantaneous current.
    BATTERY_COUNTER_CURRENT = 3;

    // Avg current.
    BATTERY_COUNTER_CURRENT_AVG = 4;
  }
  optional uint32 battery_poll_ms = 1;
  repeated BatteryCounters battery_counters = 2;

  // Where available enables per-power-rail measurements.
  optional bool collect_power_rails = 3;
}

// End of protos/perfetto/config/power/android_power_config.proto

// Begin of protos/perfetto/config/process_stats/process_stats_config.proto

message ProcessStatsConfig {
  enum Quirks {
    QUIRKS_UNSPECIFIED = 0;

    // This has been deprecated and ignored as per 2018-05-01. Full scan at
    // startup is now disabled by default and can be re-enabled using the
    // |scan_all_processes_on_start| arg.
    DISABLE_INITIAL_DUMP = 1 [deprecated = true];

    DISABLE_ON_DEMAND = 2;
  }

  repeated Quirks quirks = 1;

  // If enabled all processes will be scanned and dumped when the trace starts.
  optional bool scan_all_processes_on_start = 2;

  // If enabled thread names are also recoded (this is redundant if sched_switch
  // is enabled).
  optional bool record_thread_names = 3;

  // If > 0 samples counters (see process_stats.proto) from
  // /proc/pid/status and oom_score_adj every X ms.
  // This is required to be > 100ms to avoid excessive CPU usage.
  // TODO(primiano): add CPU cost for change this value.
  optional uint32 proc_stats_poll_ms = 4;

  // If empty samples stats for all processes. If non empty samples stats only
  // for processes matching the given string in their argv0 (i.e. the first
  // entry of /proc/pid/cmdline).
  // TODO(primiano): implement this feature.
  // repeated string proc_stats_filter = 5;

  // This is required to be either = 0 or a multiple of |proc_stats_poll_ms|
  // (default: |proc_stats_poll_ms|). If = 0, will be set to
  // |proc_stats_poll_ms|. Non-multiples will be rounded down to the nearest
  // multiple.
  optional uint32 proc_stats_cache_ttl_ms = 6;

  // Whether to record /proc/tid/time_in_state.
  optional bool record_thread_time_in_state = 7;

  // Size of the cache for thread time_in_state cpu freq values.
  // If not specificed, the default is used.
  optional uint32 thread_time_in_state_cache_size = 8;
}

// End of protos/perfetto/config/process_stats/process_stats_config.proto

// Begin of protos/perfetto/config/profiling/heapprofd_config.proto

// Configuration for go/heapprofd.
// Next id: 23
message HeapprofdConfig {
  message ContinuousDumpConfig {
    // ms to wait before first dump.
    optional uint32 dump_phase_ms = 5;
    // ms to wait between following dumps.
    optional uint32 dump_interval_ms = 6;
  }

  // Sampling rate for all heaps not specified via heap_sampling_intervals.
  //
  // These are:
  // * All heaps if heap_sampling_intervals is empty.
  // * Those profiled due to all_heaps and not named in heaps if
  //   heap_sampling_intervals is not empty.
  // * The implicit libc.malloc heap if heaps is empty.
  //
  // Set to 1 for perfect accuracy.
  // Otherwise, sample every sample_interval_bytes on average.
  //
  // See
  // https://perfetto.dev/docs/data-sources/native-heap-profiler#sampling-interval
  // for more details.
  //
  // BUGS
  // Before Android 12, setting this to 0 would crash the target process.
  optional uint64 sampling_interval_bytes = 1;

  // E.g. surfaceflinger, com.android.phone
  // This input is normalized in the following way: if it contains slashes,
  // everything up to the last slash is discarded. If it contains "@",
  // everything after the first @ is discared.
  // E.g. /system/bin/surfaceflinger@1.0 normalizes to surfaceflinger.
  // This transformation is also applied to the processes' command lines when
  // matching.
  repeated string process_cmdline = 2;

  // For watermark based triggering or local debugging.
  repeated uint64 pid = 4;

  // Which heaps to sample, e.g. "libc.malloc". If left empty, only samples
  // "malloc".
  //
  // Introduced in Android 12.
  repeated string heaps = 20;

  // If given, needs to be the same length as heaps and gives the sampling
  // interval for the respective entry in heaps.
  //
  // Otherwise, sampling_interval_bytes is used.
  //
  // It is recommended to set sampling_interval_bytes to a reasonable default
  // value when using this, as a value of 0 for sampling_interval_bytes will
  // crash the target process before Android 12.
  //
  // Introduced in Android 12.
  repeated uint64 heap_sampling_intervals = 22;

  // Sample all heaps registered by target process. Introduced in Android 12.
  optional bool all_heaps = 21;

  // Profile all processes eligible for profiling on the system.
  // See
  // https://perfetto.dev/docs/data-sources/native-heap-profiler#heapprofd-targets
  // for which processes are eligible.
  //
  // On unmodified userdebug builds, this will lead to system crashes. Zygote
  // will crash when trying to launch a new process as it will have an
  // unexpected open socket to heapprofd.
  //
  // heapprofd will likely be overloaded by the amount of data for low
  // sampling intervals.
  optional bool all = 5;

  // Do not profile processes whose anon RSS + swap < given value.
  // Introduced in Android 11.
  optional uint32 min_anonymous_memory_kb = 15;

  // Stop profile if heapprofd memory usage goes beyond the given value.
  // Introduced in Android 11.
  optional uint32 max_heapprofd_memory_kb = 16;

  // Stop profile if heapprofd CPU time since start of this data-source
  // goes beyond given value.
  // Introduced in Android 11.
  optional uint64 max_heapprofd_cpu_secs = 17;

  // Do not emit function names for mappings starting with this prefix.
  // E.g. /system to not emit symbols for any system libraries.
  repeated string skip_symbol_prefix = 7;

  // Dump at a predefined interval.
  optional ContinuousDumpConfig continuous_dump_config = 6;

  // Size of the shared memory buffer between the profiled processes and
  // heapprofd. Defaults to 8 MiB. If larger than 500 MiB, truncated to 500
  // MiB.
  //
  // Needs to be:
  // * at least 8192,
  // * a power of two,
  // * a multiple of 4096.
  optional uint64 shmem_size_bytes = 8;

  // When the shmem buffer is full, block the client instead of ending the
  // trace. Use with caution as this will significantly slow down the target
  // process.
  optional bool block_client = 9;

  // If set, stop the trace session after blocking the client for this
  // timeout. Needs to be larger than 100 us, otherwise no retries are done.
  // Introduced in Android 11.
  optional uint32 block_client_timeout_us = 14;

  // Do not profile processes from startup, only match already running
  // processes.
  //
  // Can not be set at the same time as no_running.
  // Introduced in Android 11.
  optional bool no_startup = 10;

  // Do not profile running processes. Only match processes on startup.
  //
  // Can not be set at the same time as no_startup.
  // Introduced in Android 11.
  optional bool no_running = 11;

  // Gather information on how many bytes of allocations are on non-referenced
  // pages. The way to use this generally is:
  // 1. Start profile of app.
  // 2. Start app.
  // 3. Trigger a dump by sending SIGUSR1 to heapprofd.
  // 4. Do operations.
  // 5. End profile.
  //
  // You can then find the allocations that were not used for the operations you
  // did in step 4.
  optional bool idle_allocations = 12;

  // Cause heapprofd to emit a single dump at the end, showing the memory usage
  // at the point in time when the sampled heap usage of the process was at its
  // maximum. This causes ProfilePacket.HeapSample.self_max to be set, and
  // self_allocated and self_freed to not be set.
  // Introduced in Android 11.
  optional bool dump_at_max = 13;

  // FEATURE FLAGS. THERE BE DRAGONS.

  // Escape hatch if the session is being torn down because of a forked child
  // that shares memory space, but is not correctly identified as a vforked
  // child.
  // Introduced in Android 11.
  optional bool disable_fork_teardown = 18;

  // We try to automatically detect when a target applicatation vforks but then
  // does a memory allocation (or free). This auto-detection can be disabled
  // with this.
  // Introduced in Android 11.
  optional bool disable_vfork_detection = 19;
}

// End of protos/perfetto/config/profiling/heapprofd_config.proto

// Begin of protos/perfetto/config/profiling/java_hprof_config.proto

// Configuration for go/heapprofd.
message JavaHprofConfig {
  // If dump_interval_ms != 0, the following configuration is used.
  message ContinuousDumpConfig {
    // ms to wait before first continuous dump.
    // A dump is always created at the beginning of the trace.
    optional uint32 dump_phase_ms = 1;
    // ms to wait between following dumps.
    optional uint32 dump_interval_ms = 2;
  }

  // This input is normalized in the following way: if it contains slashes,
  // everything up to the last slash is discarded. If it contains "@",
  // everything after the first @ is discared.
  // E.g. /system/bin/surfaceflinger@1.0 normalizes to surfaceflinger.
  // This transformation is also applied to the processes' command lines when
  // matching.
  repeated string process_cmdline = 1;

  // For watermark based triggering or local debugging.
  repeated uint64 pid = 2;

  // Dump at a predefined interval.
  optional ContinuousDumpConfig continuous_dump_config = 3;

  // Do not profile processes whose anon RSS + swap < given value.
  optional uint32 min_anonymous_memory_kb = 4;

  // Include the process' /proc/self/smaps.
  // This only shows maps that:
  // * start with /system
  // * start with /vendor
  // * start with /data/app
  // * contain "extracted in memory from Y", where Y matches any of the above
  optional bool dump_smaps = 5;

  // Exclude objects of the following types from the profile. This can be
  // useful if lots of uninteresting objects, e.g. "sun.misc.Cleaner".
  repeated string ignored_types = 6;
}

// End of protos/perfetto/config/profiling/java_hprof_config.proto

// Begin of protos/perfetto/config/profiling/perf_event_config.proto

// Configuration for the traced_perf profiler.
//
// At the time of writing, the config options are restricted to the periodic
// system-wide stack sampling use-case (|all_cpus| must be true).
message PerfEventConfig {
  // If true, sample events on all CPUs.
  optional bool all_cpus = 1;

  // Per-cpu sampling frequency (requested from the kernel). Not guaranteed to
  // be honored as the kernel can throttle the sampling rate if it's too high.
  // If unset, an implementation-defined default is used.
  optional uint32 sampling_frequency = 2;

  // How often the per-cpu ring buffers are read by the producer.
  // If unset, an implementation-defined default is used.
  optional uint32 ring_buffer_read_period_ms = 8;

  // Size (in 4k pages) of each per-cpu ring buffer that is filled by the
  // kernel. If set, must be a power of two.
  // If unset, an implementation-defined default is used.
  optional uint32 ring_buffer_pages = 3;

  // Process ID (TGID) allowlist. If this list is not empty, only matching
  // samples will be retained. If multiple allow/deny-lists are
  // specified by the config, then all of them are evaluated for each sampled
  // process.
  repeated int32 target_pid = 4;

  // Command line allowlist, matched against the
  // /proc/<pid>/cmdline (not the comm string), with both sides being
  // "normalized". Normalization is as follows: (1) trim everything beyond the
  // first null or "@" byte; (2) if the string contains forward slashes, trim
  // everything up to and including the last one.
  repeated string target_cmdline = 5;

  // List of excluded pids.
  repeated int32 exclude_pid = 6;

  // List of excluded cmdlines. Normalized in the same way as |target_cmdline|.
  repeated string exclude_cmdline = 7;

  // Number of additional command lines to sample. Only those which are neither
  // explicitly included nor excluded will be considered. Processes are accepted
  // on a first come, first served basis.
  optional uint32 additional_cmdline_count = 11;

  ////////////////////
  // Uncommon options:

  // Timeout for the remote /proc/<pid>/{maps,mem} file descriptors for a
  // sampled process. This is primarily for Android, where this lookup is
  // asynchronous. As long as the producer is waiting, the associated samples
  // will be kept enqueued (putting pressure on the capacity of the shared
  // unwinding queue). Once a lookup for a process expires, all associated
  // samples are discarded. However, if the lookup still succeeds after the
  // timeout, future samples will be handled normally.
  // If unset, an implementation-defined default is used.
  optional uint32 remote_descriptor_timeout_ms = 9;

  // Optional period for clearing state cached by the unwinder. This is a heavy
  // operation that is only necessary for traces that target a wide set of
  // processes, and require the memory footprint to be reset periodically.
  // If unset, the cached state will not be cleared.
  optional uint32 unwind_state_clear_period_ms = 10;
}

// End of protos/perfetto/config/profiling/perf_event_config.proto

// Begin of protos/perfetto/common/sys_stats_counters.proto

// When editing entries here remember also to update "sys_stats_counters.h" with
// the corresponding string definitions for the actual /proc files parser.

// Counter definitions for Linux's /proc/meminfo.
enum MeminfoCounters {
  MEMINFO_UNSPECIFIED = 0;
  MEMINFO_MEM_TOTAL = 1;
  MEMINFO_MEM_FREE = 2;
  MEMINFO_MEM_AVAILABLE = 3;
  MEMINFO_BUFFERS = 4;
  MEMINFO_CACHED = 5;
  MEMINFO_SWAP_CACHED = 6;
  MEMINFO_ACTIVE = 7;
  MEMINFO_INACTIVE = 8;
  MEMINFO_ACTIVE_ANON = 9;
  MEMINFO_INACTIVE_ANON = 10;
  MEMINFO_ACTIVE_FILE = 11;
  MEMINFO_INACTIVE_FILE = 12;
  MEMINFO_UNEVICTABLE = 13;
  MEMINFO_MLOCKED = 14;
  MEMINFO_SWAP_TOTAL = 15;
  MEMINFO_SWAP_FREE = 16;
  MEMINFO_DIRTY = 17;
  MEMINFO_WRITEBACK = 18;
  MEMINFO_ANON_PAGES = 19;
  MEMINFO_MAPPED = 20;
  MEMINFO_SHMEM = 21;
  MEMINFO_SLAB = 22;
  MEMINFO_SLAB_RECLAIMABLE = 23;
  MEMINFO_SLAB_UNRECLAIMABLE = 24;
  MEMINFO_KERNEL_STACK = 25;
  MEMINFO_PAGE_TABLES = 26;
  MEMINFO_COMMIT_LIMIT = 27;
  MEMINFO_COMMITED_AS = 28;
  MEMINFO_VMALLOC_TOTAL = 29;
  MEMINFO_VMALLOC_USED = 30;
  MEMINFO_VMALLOC_CHUNK = 31;
  MEMINFO_CMA_TOTAL = 32;
  MEMINFO_CMA_FREE = 33;
}

// Counter definitions for Linux's /proc/vmstat.
enum VmstatCounters {
  VMSTAT_UNSPECIFIED = 0;
  VMSTAT_NR_FREE_PAGES = 1;
  VMSTAT_NR_ALLOC_BATCH = 2;
  VMSTAT_NR_INACTIVE_ANON = 3;
  VMSTAT_NR_ACTIVE_ANON = 4;
  VMSTAT_NR_INACTIVE_FILE = 5;
  VMSTAT_NR_ACTIVE_FILE = 6;
  VMSTAT_NR_UNEVICTABLE = 7;
  VMSTAT_NR_MLOCK = 8;
  VMSTAT_NR_ANON_PAGES = 9;
  VMSTAT_NR_MAPPED = 10;
  VMSTAT_NR_FILE_PAGES = 11;
  VMSTAT_NR_DIRTY = 12;
  VMSTAT_NR_WRITEBACK = 13;
  VMSTAT_NR_SLAB_RECLAIMABLE = 14;
  VMSTAT_NR_SLAB_UNRECLAIMABLE = 15;
  VMSTAT_NR_PAGE_TABLE_PAGES = 16;
  VMSTAT_NR_KERNEL_STACK = 17;
  VMSTAT_NR_OVERHEAD = 18;
  VMSTAT_NR_UNSTABLE = 19;
  VMSTAT_NR_BOUNCE = 20;
  VMSTAT_NR_VMSCAN_WRITE = 21;
  VMSTAT_NR_VMSCAN_IMMEDIATE_RECLAIM = 22;
  VMSTAT_NR_WRITEBACK_TEMP = 23;
  VMSTAT_NR_ISOLATED_ANON = 24;
  VMSTAT_NR_ISOLATED_FILE = 25;
  VMSTAT_NR_SHMEM = 26;
  VMSTAT_NR_DIRTIED = 27;
  VMSTAT_NR_WRITTEN = 28;
  VMSTAT_NR_PAGES_SCANNED = 29;
  VMSTAT_WORKINGSET_REFAULT = 30;
  VMSTAT_WORKINGSET_ACTIVATE = 31;
  VMSTAT_WORKINGSET_NODERECLAIM = 32;
  VMSTAT_NR_ANON_TRANSPARENT_HUGEPAGES = 33;
  VMSTAT_NR_FREE_CMA = 34;
  VMSTAT_NR_SWAPCACHE = 35;
  VMSTAT_NR_DIRTY_THRESHOLD = 36;
  VMSTAT_NR_DIRTY_BACKGROUND_THRESHOLD = 37;
  VMSTAT_PGPGIN = 38;
  VMSTAT_PGPGOUT = 39;
  VMSTAT_PGPGOUTCLEAN = 40;
  VMSTAT_PSWPIN = 41;
  VMSTAT_PSWPOUT = 42;
  VMSTAT_PGALLOC_DMA = 43;
  VMSTAT_PGALLOC_NORMAL = 44;
  VMSTAT_PGALLOC_MOVABLE = 45;
  VMSTAT_PGFREE = 46;
  VMSTAT_PGACTIVATE = 47;
  VMSTAT_PGDEACTIVATE = 48;
  VMSTAT_PGFAULT = 49;
  VMSTAT_PGMAJFAULT = 50;
  VMSTAT_PGREFILL_DMA = 51;
  VMSTAT_PGREFILL_NORMAL = 52;
  VMSTAT_PGREFILL_MOVABLE = 53;
  VMSTAT_PGSTEAL_KSWAPD_DMA = 54;
  VMSTAT_PGSTEAL_KSWAPD_NORMAL = 55;
  VMSTAT_PGSTEAL_KSWAPD_MOVABLE = 56;
  VMSTAT_PGSTEAL_DIRECT_DMA = 57;
  VMSTAT_PGSTEAL_DIRECT_NORMAL = 58;
  VMSTAT_PGSTEAL_DIRECT_MOVABLE = 59;
  VMSTAT_PGSCAN_KSWAPD_DMA = 60;
  VMSTAT_PGSCAN_KSWAPD_NORMAL = 61;
  VMSTAT_PGSCAN_KSWAPD_MOVABLE = 62;
  VMSTAT_PGSCAN_DIRECT_DMA = 63;
  VMSTAT_PGSCAN_DIRECT_NORMAL = 64;
  VMSTAT_PGSCAN_DIRECT_MOVABLE = 65;
  VMSTAT_PGSCAN_DIRECT_THROTTLE = 66;
  VMSTAT_PGINODESTEAL = 67;
  VMSTAT_SLABS_SCANNED = 68;
  VMSTAT_KSWAPD_INODESTEAL = 69;
  VMSTAT_KSWAPD_LOW_WMARK_HIT_QUICKLY = 70;
  VMSTAT_KSWAPD_HIGH_WMARK_HIT_QUICKLY = 71;
  VMSTAT_PAGEOUTRUN = 72;
  VMSTAT_ALLOCSTALL = 73;
  VMSTAT_PGROTATED = 74;
  VMSTAT_DROP_PAGECACHE = 75;
  VMSTAT_DROP_SLAB = 76;
  VMSTAT_PGMIGRATE_SUCCESS = 77;
  VMSTAT_PGMIGRATE_FAIL = 78;
  VMSTAT_COMPACT_MIGRATE_SCANNED = 79;
  VMSTAT_COMPACT_FREE_SCANNED = 80;
  VMSTAT_COMPACT_ISOLATED = 81;
  VMSTAT_COMPACT_STALL = 82;
  VMSTAT_COMPACT_FAIL = 83;
  VMSTAT_COMPACT_SUCCESS = 84;
  VMSTAT_COMPACT_DAEMON_WAKE = 85;
  VMSTAT_UNEVICTABLE_PGS_CULLED = 86;
  VMSTAT_UNEVICTABLE_PGS_SCANNED = 87;
  VMSTAT_UNEVICTABLE_PGS_RESCUED = 88;
  VMSTAT_UNEVICTABLE_PGS_MLOCKED = 89;
  VMSTAT_UNEVICTABLE_PGS_MUNLOCKED = 90;
  VMSTAT_UNEVICTABLE_PGS_CLEARED = 91;
  VMSTAT_UNEVICTABLE_PGS_STRANDED = 92;
  VMSTAT_NR_ZSPAGES = 93;
  VMSTAT_NR_ION_HEAP = 94;
  VMSTAT_NR_GPU_HEAP = 95;
  VMSTAT_ALLOCSTALL_DMA = 96;
  VMSTAT_ALLOCSTALL_MOVABLE = 97;
  VMSTAT_ALLOCSTALL_NORMAL = 98;
  VMSTAT_COMPACT_DAEMON_FREE_SCANNED = 99;
  VMSTAT_COMPACT_DAEMON_MIGRATE_SCANNED = 100;
  VMSTAT_NR_FASTRPC = 101;
  VMSTAT_NR_INDIRECTLY_RECLAIMABLE = 102;
  VMSTAT_NR_ION_HEAP_POOL = 103;
  VMSTAT_NR_KERNEL_MISC_RECLAIMABLE = 104;
  VMSTAT_NR_SHADOW_CALL_STACK_BYTES = 105;
  VMSTAT_NR_SHMEM_HUGEPAGES = 106;
  VMSTAT_NR_SHMEM_PMDMAPPED = 107;
  VMSTAT_NR_UNRECLAIMABLE_PAGES = 108;
  VMSTAT_NR_ZONE_ACTIVE_ANON = 109;
  VMSTAT_NR_ZONE_ACTIVE_FILE = 110;
  VMSTAT_NR_ZONE_INACTIVE_ANON = 111;
  VMSTAT_NR_ZONE_INACTIVE_FILE = 112;
  VMSTAT_NR_ZONE_UNEVICTABLE = 113;
  VMSTAT_NR_ZONE_WRITE_PENDING = 114;
  VMSTAT_OOM_KILL = 115;
  VMSTAT_PGLAZYFREE = 116;
  VMSTAT_PGLAZYFREED = 117;
  VMSTAT_PGREFILL = 118;
  VMSTAT_PGSCAN_DIRECT = 119;
  VMSTAT_PGSCAN_KSWAPD = 120;
  VMSTAT_PGSKIP_DMA = 121;
  VMSTAT_PGSKIP_MOVABLE = 122;
  VMSTAT_PGSKIP_NORMAL = 123;
  VMSTAT_PGSTEAL_DIRECT = 124;
  VMSTAT_PGSTEAL_KSWAPD = 125;
  VMSTAT_SWAP_RA = 126;
  VMSTAT_SWAP_RA_HIT = 127;
  VMSTAT_WORKINGSET_RESTORE = 128;
}
// End of protos/perfetto/common/sys_stats_counters.proto

// Begin of protos/perfetto/config/sys_stats/sys_stats_config.proto

// This file defines the configuration for the Linux /proc poller data source,
// which injects counters in the trace.
// Counters that are needed in the trace must be explicitly listed in the
// *_counters fields. This is to avoid spamming the trace with all counters
// at all times.
// The sampling rate is configurable. All polling rates (*_period_ms) need
// to be integer multiples of each other.
// OK:     [10ms, 10ms, 10ms],  [10ms, 20ms, 10ms],  [10ms, 20ms, 60ms]
// Not OK: [10ms, 10ms, 11ms],  [10ms, 15ms, 20ms]
message SysStatsConfig {
  // Polls /proc/meminfo every X ms, if non-zero.
  // This is required to be > 10ms to avoid excessive CPU usage.
  // Cost: 0.3 ms [read] + 0.07 ms [parse + trace injection]
  optional uint32 meminfo_period_ms = 1;

  // If empty all known counters are reported. Otherwise, only the counters
  // specified below are reported.
  repeated MeminfoCounters meminfo_counters = 2;

  // Polls /proc/vmstat every X ms, if non-zero.
  // This is required to be > 10ms to avoid excessive CPU usage.
  // Cost: 0.2 ms [read] + 0.3 ms [parse + trace injection]
  optional uint32 vmstat_period_ms = 3;
  repeated VmstatCounters vmstat_counters = 4;

  // Pols /proc/stat every X ms, if non-zero.
  // This is required to be > 10ms to avoid excessive CPU usage.
  // Cost: 4.1 ms [read] + 1.9 ms [parse + trace injection]
  optional uint32 stat_period_ms = 5;
  enum StatCounters {
    STAT_UNSPECIFIED = 0;
    STAT_CPU_TIMES = 1;
    STAT_IRQ_COUNTS = 2;
    STAT_SOFTIRQ_COUNTS = 3;
    STAT_FORK_COUNT = 4;
  }
  repeated StatCounters stat_counters = 6;
}

// End of protos/perfetto/config/sys_stats/sys_stats_config.proto

// Begin of protos/perfetto/config/test_config.proto

// The configuration for a fake producer used in tests.
message TestConfig {
  message DummyFields {
    optional uint32 field_uint32 = 1;
    optional int32 field_int32 = 2;
    optional uint64 field_uint64 = 3;
    optional int64 field_int64 = 4;
    optional fixed64 field_fixed64 = 5;
    optional sfixed64 field_sfixed64 = 6;
    optional fixed32 field_fixed32 = 7;
    optional sfixed32 field_sfixed32 = 8;
    optional double field_double = 9;
    optional float field_float = 10;
    optional sint64 field_sint64 = 11;
    optional sint32 field_sint32 = 12;
    optional string field_string = 13;
    optional bytes field_bytes = 14;
  }

  // The number of messages the fake producer should send.
  optional uint32 message_count = 1;

  // The maximum number of messages which should be sent each second.
  // The actual obserced speed may be lower if the producer is unable to
  // work fast enough.
  // If this is zero or unset, the producer will send as fast as possible.
  optional uint32 max_messages_per_second = 2;

  // The seed value for a simple multiplicative congruential pseudo-random
  // number sequence.
  optional uint32 seed = 3;

  // The size of each message in bytes. Should be greater than or equal 5 to
  // account for the number of bytes needed to encode the random number and a
  // null byte for the string.
  optional uint32 message_size = 4;

  // Whether the producer should send a event batch when the data source is
  // is initially registered.
  optional bool send_batch_on_register = 5;

  optional DummyFields dummy_fields = 6;
}

// End of protos/perfetto/config/test_config.proto

// Begin of protos/perfetto/config/track_event/track_event_config.proto

message TrackEventConfig {
  // The following fields define the set of enabled trace categories. Each list
  // item is a glob.
  //
  // To determine if category is enabled, it is checked against the filters in
  // the following order:
  //
  //   1. Exact matches in enabled categories.
  //   2. Exact matches in enabled tags.
  //   3. Exact matches in disabled categories.
  //   4. Exact matches in disabled tags.
  //   5. Pattern matches in enabled categories.
  //   6. Pattern matches in enabled tags.
  //   7. Pattern matches in disabled categories.
  //   8. Pattern matches in disabled tags.
  //
  // If none of the steps produced a match, the category is enabled by default.
  //
  // Examples:
  //
  //  - To enable all non-slow/debug categories:
  //
  //       No configuration needed, happens by default.
  //
  //  - To enable a specific category:
  //
  //       disabled_categories = ["*"]
  //       enabled_categories = ["my_category"]
  //
  //  - To enable only categories with a specific tag:
  //
  //       disabled_tags = ["*"]
  //       enabled_tags = ["my_tag"]
  //

  // Default: []
  repeated string disabled_categories = 1;

  // Default: []
  repeated string enabled_categories = 2;

  // Default: [“slow”, “debug”]
  repeated string disabled_tags = 3;

  // Default: []
  repeated string enabled_tags = 4;
}

// End of protos/perfetto/config/track_event/track_event_config.proto

// Begin of protos/perfetto/config/data_source_config.proto

// The configuration that is passed to each data source when starting tracing.
message DataSourceConfig {
  // Data source unique name, e.g., "linux.ftrace". This must match
  // the name passed by the data source when it registers (see
  // RegisterDataSource()).
  optional string name = 1;

  // The index of the logging buffer where TracePacket(s) will be stored.
  // This field doesn't make a major difference for the Producer(s). The final
  // logging buffers, in fact, are completely owned by the Service. We just ask
  // the Producer to copy this number into the chunk headers it emits, so that
  // the Service can quickly identify the buffer where to move the chunks into
  // without expensive lookups on its fastpath.
  optional uint32 target_buffer = 2;

  // Set by the service to indicate the duration of the trace.
  // DO NOT SET in consumer as this will be overridden by the service.
  optional uint32 trace_duration_ms = 3;

  // Set by the service to indicate how long it waits after StopDataSource.
  // DO NOT SET in consumer as this will be overridden by the service.
  optional uint32 stop_timeout_ms = 7;

  // Set by the service to indicate whether this tracing session has extra
  // guardrails.
  // DO NOT SET in consumer as this will be overridden by the service.
  optional bool enable_extra_guardrails = 6;

  // Set by the service to indicate which tracing session the data source
  // belongs to. The intended use case for this is checking if two data sources,
  // one of which produces metadata for the other one, belong to the same trace
  // session and hence should be linked together.
  // This field was introduced in Aug 2018 after Android P.
  optional uint64 tracing_session_id = 4;

  // Keeep the lower IDs (up to 99) for fields that are *not* specific to
  // data-sources and needs to be processed by the traced daemon.

  // All data source config fields must be marked as [lazy=true]. This prevents
  // the proto-to-cpp generator from recursing into those when generating the
  // cpp classes and polluting tracing/core with data-source-specific classes.
  // Instead they are treated as opaque strings containing raw proto bytes.

  // Data source name: linux.ftrace
  optional FtraceConfig ftrace_config = 100 [lazy = true];
  // Data source name: linux.inode_file_map
  optional InodeFileConfig inode_file_config = 102 [lazy = true];
  // Data source name: linux.process_stats
  optional ProcessStatsConfig process_stats_config = 103 [lazy = true];
  // Data source name: linux.sys_stats
  optional SysStatsConfig sys_stats_config = 104 [lazy = true];
  // Data source name: android.heapprofd
  // Introduced in Android 10.
  optional HeapprofdConfig heapprofd_config = 105 [lazy = true];
  // Data source name: android.java_hprof
  // Introduced in Android 11.
  optional JavaHprofConfig java_hprof_config = 110 [lazy = true];
  // Data source name: android.power
  optional AndroidPowerConfig android_power_config = 106 [lazy = true];
  // Data source name: android.log
  optional AndroidLogConfig android_log_config = 107 [lazy = true];
  // TODO(fmayer): Add data source name for this.
  optional GpuCounterConfig gpu_counter_config = 108 [lazy = true];
  // Data source name: android.packages_list
  optional PackagesListConfig packages_list_config = 109 [lazy = true];
  // Data source name: linux.perf
  optional PerfEventConfig perf_event_config = 111 [lazy = true];
  // Data source name: vulkan.memory_tracker
  optional VulkanMemoryConfig vulkan_memory_config = 112 [lazy = true];
  // Data source name: track_event
  optional TrackEventConfig track_event_config = 113 [lazy = true];
  // Data source name: android.polled_state
  optional AndroidPolledStateConfig android_polled_state_config = 114
      [lazy = true];

  // Chrome is special as it doesn't use the perfetto IPC layer. We want to
  // avoid proto serialization and de-serialization there because that would
  // just add extra hops on top of the Mojo ser/des. Instead we auto-generate a
  // C++ class for it so it can pass around plain C++ objets.
  optional ChromeConfig chrome_config = 101;

  // This is a fallback mechanism to send a free-form text config to the
  // producer. In theory this should never be needed. All the code that
  // is part of the platform (i.e. traced service) is supposed to *not* truncate
  // the trace config proto and propagate unknown fields. However, if anything
  // in the pipeline (client or backend) ends up breaking this forward compat
  // plan, this field will become the escape hatch to allow future data sources
  // to get some meaningful configuration.
  optional string legacy_config = 1000;

  // This field is only used for testing.
  optional TestConfig for_testing = 1001;

  // Was |for_testing|. Caused more problems then found.
  reserved *********;
}

// End of protos/perfetto/config/data_source_config.proto

// Begin of protos/perfetto/config/trace_config.proto

// The overall config that is used when starting a new tracing session through
// ProducerPort::StartTracing().
// It contains the general config for the logging buffer(s) and the configs for
// all the data source being enabled.
//
// Next id: 30.
message TraceConfig {
  message BufferConfig {
    optional uint32 size_kb = 1;

    // |page_size|, now deprecated.
    reserved 2;

    // |optimize_for|, now deprecated.
    reserved 3;

    enum FillPolicy {
      UNSPECIFIED = 0;

      // Default behavior. The buffer operates as a conventional ring buffer.
      // If the writer is faster than the reader (or if the reader reads only
      // after tracing is stopped) newly written packets will overwrite old
      // packets.
      RING_BUFFER = 1;

      // Behaves like RING_BUFFER as long as there is space in the buffer or
      // the reader catches up with the writer. As soon as the writer hits
      // an unread chunk, it stops accepting new data in the buffer.
      DISCARD = 2;
    }
    optional FillPolicy fill_policy = 4;
  }
  repeated BufferConfig buffers = 1;

  message DataSource {
    // Filters and data-source specific config. It contains also the unique name
    // of the data source, the one passed in the  DataSourceDescriptor when they
    // register on the service.
    optional protos.DataSourceConfig config = 1;

    // Optional. If multiple producers (~processes) expose the same data source
    // and either |producer_name_filter| or |producer_name_regex_filter| is set,
    // the data source is enabled only for producers whose names match any of
    // the filters.
    // |producer_name_filter| has to be an exact match, while
    // |producer_name_regex_filter| is a regular expression.
    // This allows to enable a data source only for specific processes.
    // The "repeated" fields have OR semantics: specifying a filter ["foo",
    // "bar"] will enable data sources on both "foo" and "bar" (if they exist).
    repeated string producer_name_filter = 2;
    repeated string producer_name_regex_filter = 3;
  }
  repeated DataSource data_sources = 2;

  // Config for disabling builtin data sources in the tracing service.
  message BuiltinDataSource {
    // Disable emitting clock timestamps into the trace.
    optional bool disable_clock_snapshotting = 1;

    // Disable echoing the original trace config in the trace.
    optional bool disable_trace_config = 2;

    // Disable emitting system info (build fingerprint, cpuinfo, etc).
    optional bool disable_system_info = 3;

    // Disable emitting events for data-source state changes (e.g. the marker
    // for all data sources having ACKed the start of the trace).
    optional bool disable_service_events = 4;

    // The authoritative clock domain for the trace. Defaults to BOOTTIME. See
    // also ClockSnapshot's primary_trace_clock. The configured value is written
    // into the trace as part of the ClockSnapshots emitted by the service.
    // Trace processor will attempt to translate packet/event timestamps from
    // various data sources (and their chosen clock domains) to this domain
    // during import. Added in Android R.
    optional BuiltinClock primary_trace_clock = 5;

    // Time interval in between snapshotting of sync markers, clock snapshots,
    // stats, and other periodic service-emitted events. Note that the service
    // only keeps track of the first and the most recent snapshot until
    // ReadBuffers() is called.
    optional uint32 snapshot_interval_ms = 6;
  }
  optional BuiltinDataSource builtin_data_sources = 20;

  // If specified, the trace will be stopped |duration_ms| after starting.
  // This does *not* count the time the system is suspended, so we will run
  // for duration_ms of system activity, not wall time.
  //
  // However in case of traces with triggers, see
  // TriggerConfig.trigger_timeout_ms instead.
  optional uint32 duration_ms = 3;

  // This is set when --dropbox is passed to the Perfetto command line client
  // and enables guardrails that limit resource usage for traces requested
  // by statsd.
  optional bool enable_extra_guardrails = 4;

  enum LockdownModeOperation {
    LOCKDOWN_UNCHANGED = 0;
    LOCKDOWN_CLEAR = 1;
    LOCKDOWN_SET = 2;
  }
  // Reject producers that are not running under the same UID as the tracing
  // service.
  optional LockdownModeOperation lockdown_mode = 5;

  message ProducerConfig {
    // Identifies the producer for which this config is for.
    optional string producer_name = 1;

    // Specifies the preferred size of the shared memory buffer. If the size is
    // larger than the max size, the max will be used. If it is smaller than
    // the page size or doesn't fit pages evenly into it, it will fall back to
    // the size specified by the producer or finally the default shared memory
    // size.
    optional uint32 shm_size_kb = 2;

    // Specifies the preferred size of each page in the shared memory buffer.
    // Must be an integer multiple of 4K.
    optional uint32 page_size_kb = 3;
  }

  repeated ProducerConfig producers = 6;

  // Contains statsd-specific metadata about an alert associated with the trace.
  message StatsdMetadata {
    // The identifier of the alert which triggered this trace.
    optional int64 triggering_alert_id = 1;
    // The uid which registered the triggering configuration with statsd.
    optional int32 triggering_config_uid = 2;
    // The identifier of the config which triggered the alert.
    optional int64 triggering_config_id = 3;
    // The identifier of the subscription which triggered this trace.
    optional int64 triggering_subscription_id = 4;
  }

  // Statsd-specific metadata.
  optional StatsdMetadata statsd_metadata = 7;

  // When true && |output_path| is empty, the EnableTracing() request must
  // provide a file descriptor. The service will then periodically read packets
  // out of the trace buffer and store it into the passed file.
  // If |output_path| is not empty no fd should be passed, the service
  // will create a new file and write into that (see comment below).
  optional bool write_into_file = 8;

  // This must point to a non-existing file. If the file exists the service
  // will NOT overwrite and will fail instead as a security precaution.
  // On Android, when this is used with the system traced, the path must be
  // within /data/misc/perfetto-traces/ or the trace will fail.
  // This option has been introduced in Android R. Before R write_into_file
  // can be used only with the "pass a file descriptor over IPC" mode.
  optional string output_path = 29;

  // Optional. If non-zero tunes the write period. A min value of 100ms is
  // enforced (i.e. smaller values are ignored).
  optional uint32 file_write_period_ms = 9;

  // Optional. When non zero the periodic write stops once at most X bytes
  // have been written into the file. Tracing is disabled when this limit is
  // reached, even if |duration_ms| has not been reached yet.
  optional uint64 max_file_size_bytes = 10;

  // Contains flags which override the default values of the guardrails inside
  // Perfetto. These values are only affect userdebug builds.
  message GuardrailOverrides {
    // Override the default limit (in bytes) for uploading data to server within
    // a 24 hour period.
    optional uint64 max_upload_per_day_bytes = 1;
  }

  optional GuardrailOverrides guardrail_overrides = 11;

  // When true, data sources are not started until an explicit call to
  // StartTracing() on the consumer port. This is to support early
  // initialization and fast trace triggering. This can be used only when the
  // Consumer explicitly triggers the StartTracing() method.
  // This should not be used in a remote trace config via statsd, doing so will
  // result in a hung trace session.
  optional bool deferred_start = 12;

  // When set, it periodically issues a Flush() to all data source, forcing them
  // to commit their data into the tracing service. This can be used for
  // quasi-real-time streaming mode and to guarantee some partial ordering of
  // events in the trace in windows of X ms.
  optional uint32 flush_period_ms = 13;

  // Wait for this long for producers to acknowledge flush requests.
  // Default 5s.
  optional uint32 flush_timeout_ms = 14;

  // Wait for this long for producers to acknowledge stop requests.
  // Default 5s.
  optional uint32 data_source_stop_timeout_ms = 23;

  // |disable_clock_snapshotting| moved.
  reserved 15;

  // Android-only. If set, sends an intent to the Traceur system app when the
  // trace ends to notify it about the trace readiness.
  optional bool notify_traceur = 16;

  // Triggers allow producers to start or stop the tracing session when an event
  // occurs.
  //
  // For example if we are tracing probabilistically, most traces will be
  // uninteresting. Triggers allow us to keep only the interesting ones such as
  // those traces during which the device temperature reached a certain
  // threshold. In this case the producer can activate a trigger to keep
  // (STOP_TRACING) the trace, otherwise it can also begin a trace
  // (START_TRACING) because it knows something is about to happen.
  message TriggerConfig {
    enum TriggerMode {
      UNSPECIFIED = 0;

      // When this mode is chosen, data sources are not started until one of the
      // |triggers| are received. This supports early initialization and fast
      // starting of the tracing system. On triggering, the session will then
      // record for |stop_delay_ms|. However if no trigger is seen
      // after |trigger_timeout_ms| the session will be stopped and no data will
      // be returned.
      START_TRACING = 1;

      // When this mode is chosen, the session will be started via the normal
      // EnableTracing() & StartTracing(). If no trigger is ever seen
      // the session will be stopped after |trigger_timeout_ms| and no data will
      // be returned. However if triggered the trace will stop after
      // |stop_delay_ms| and any data in the buffer will be returned to the
      // consumer.
      STOP_TRACING = 2;
    }
    optional TriggerMode trigger_mode = 1;

    message Trigger {
      // The producer must specify this name to activate the trigger.
      optional string name = 1;

      // The a std::regex that will match the producer that can activate this
      // trigger. This is optional. If unset any producers can activate this
      // trigger.
      optional string producer_name_regex = 2;

      // After a trigger is received either in START_TRACING or STOP_TRACING
      // mode then the trace will end |stop_delay_ms| after triggering.
      optional uint32 stop_delay_ms = 3;
    }
    // A list of triggers which are related to this configuration. If ANY
    // trigger is seen then an action will be performed based on |trigger_mode|.
    repeated Trigger triggers = 2;

    // Required and must be positive if a TriggerConfig is specified. This is
    // how long this TraceConfig should wait for a trigger to arrive. After this
    // period of time if no trigger is seen the TracingSession will be cleaned
    // up.
    optional uint32 trigger_timeout_ms = 3;
  }
  optional TriggerConfig trigger_config = 17;

  // When this is non-empty the perfetto command line tool will ignore the rest
  // of this TraceConfig and instead connect to the perfetto service as a
  // producer and send these triggers, potentially stopping or starting traces
  // that were previous configured to use a TriggerConfig.
  repeated string activate_triggers = 18;

  // Configuration for trace contents that reference earlier trace data. For
  // example, a data source might intern strings, and emit packets containing
  // {interned id : string} pairs. Future packets from that data source can then
  // use the interned ids instead of duplicating the raw string contents. The
  // trace parser will then need to use that interning table to fully interpret
  // the rest of the trace.
  message IncrementalStateConfig {
    // If nonzero, notify eligible data sources to clear their incremental state
    // periodically, with the given period. The notification is sent only to
    // data sources that have |handles_incremental_state_clear| set in their
    // DataSourceDescriptor. The notification requests that the data source
    // stops referring to past trace contents. This is particularly useful when
    // tracing in ring buffer mode, where it is not exceptional to overwrite old
    // trace data.
    //
    // Warning: this time-based global clearing is likely to be removed in the
    // future, to be replaced with a smarter way of sending the notifications
    // only when necessary.
    optional uint32 clear_period_ms = 1;
  }
  optional IncrementalStateConfig incremental_state_config = 21;

  // Additional guardrail used by the Perfetto command line client.
  // On user builds when --dropbox is set perfetto will refuse to trace unless
  // this is also set.
  // Added in Q.
  optional bool allow_user_build_tracing = 19;

  // If set the tracing service will ensure there is at most one tracing session
  // with this key.
  optional string unique_session_name = 22;

  // Compress trace with the given method. Best effort.
  enum CompressionType {
    COMPRESSION_TYPE_UNSPECIFIED = 0;
    COMPRESSION_TYPE_DEFLATE = 1;
  }
  optional CompressionType compression_type = 24;

  // Android-only. Debug builds only. Not for general use. If set, saves a
  // Dropbox trace into an incident. This field is read by perfetto_cmd, rather
  // than the tracing service. All fields are mandatory.
  message IncidentReportConfig {
    optional string destination_package = 1;
    optional string destination_class = 2;
    // Level of filtering in the requested incident. See |Destination| in
    // frameworks/base/core/proto/android/privacy.proto.
    optional int32 privacy_level = 3;
    // If true, do not write the trace into dropbox (i.e. incident only).
    // Otherwise, write to both dropbox and incident.
    optional bool skip_dropbox = 4;
  }
  optional IncidentReportConfig incident_report_config = 25;

  // DEPRECATED. Was trace_uuid, use trace_uuid_msb and trace_uuid_lsb instead.
  reserved 26;
  // An identifier clients can use to tie this trace to other logging.
  // Alternative encoding of trace_uuid as two int64s.
  optional int64 trace_uuid_msb = 27;
  optional int64 trace_uuid_lsb = 28;
}

// End of protos/perfetto/config/trace_config.proto

// Begin of protos/perfetto/common/trace_stats.proto

// Statistics for the internals of the tracing service.
//
// Next id: 11.
message TraceStats {
  // From TraceBuffer::Stats.
  //
  // Next id: 20.
  message BufferStats {
    // Size of the circular buffer in bytes.
    optional uint64 buffer_size = 12;

    // Num. bytes written into the circular buffer, including chunk headers.
    optional uint64 bytes_written = 1;

    // Num. bytes overwritten before they have been read (i.e. loss of data).
    optional uint64 bytes_overwritten = 13;

    // Total size of chunks that were fully read from the circular buffer by the
    // consumer. This may not be equal to |bytes_written| either in the middle
    // of tracing, or if |chunks_overwritten| is non-zero. Note that this is the
    // size of the chunks read from the buffer, including chunk headers, which
    // will be different from the total size of packets returned to the
    // consumer.
    //
    // The current utilization of the trace buffer (mid-tracing) can be obtained
    // by subtracting |bytes_read| and |bytes_overwritten| from |bytes_written|,
    // adding the difference of |padding_bytes_written| and
    // |padding_bytes_cleared|, and comparing this sum to the |buffer_size|.
    // Note that this represents the total size of buffered data in the buffer,
    // yet this data may be spread non-contiguously through the buffer and may
    // be overridden before the utilization reaches 100%.
    optional uint64 bytes_read = 14;

    // Num. bytes that were allocated as padding between chunks in the circular
    // buffer.
    optional uint64 padding_bytes_written = 15;

    // Num. of padding bytes that were removed from the circular buffer when
    // they were overwritten.
    //
    // The difference between |padding_bytes_written| and
    // |padding_bytes_cleared| denotes the total size of padding currently
    // present in the buffer.
    optional uint64 padding_bytes_cleared = 16;

    // Num. chunks (!= packets) written into the buffer.
    optional uint64 chunks_written = 2;

    // Num. chunks (!= packets) rewritten into the buffer. This means we rewrote
    // the same chunk with additional packets appended to the end.
    optional uint64 chunks_rewritten = 10;

    // Num. chunks overwritten before they have been read (i.e. loss of data).
    optional uint64 chunks_overwritten = 3;

    // Num. chunks discarded (i.e. loss of data). Can be > 0 only when a buffer
    // is configured with FillPolicy == DISCARD.
    optional uint64 chunks_discarded = 18;

    // Num. chunks (!= packets) that were fully read from the circular buffer by
    // the consumer. This may not be equal to |chunks_written| either in the
    // middle of tracing, or if |chunks_overwritten| is non-zero.
    optional uint64 chunks_read = 17;

    // Num. chunks that were committed out of order.
    optional uint64 chunks_committed_out_of_order = 11;

    // Num. times the ring buffer wrapped around.
    optional uint64 write_wrap_count = 4;

    // Num. out-of-band (OOB) patches that succeeded.
    optional uint64 patches_succeeded = 5;

    // Num. OOB patches that failed (e.g., the chunk to patch was gone).
    optional uint64 patches_failed = 6;

    // Num. readaheads (for large multi-chunk packet reads) that ended up in a
    // successful packet read.
    optional uint64 readaheads_succeeded = 7;

    // Num. readaheads aborted because of missing chunks in the sequence stream.
    // Note that a small number > 0 is totally expected: occasionally, when
    // issuing a read, the very last packet in a sequence might be incomplete
    // (because the producer is still writing it while we read). The read will
    // stop at that point, for that sequence, increasing this counter.
    optional uint64 readaheads_failed = 8;

    // Num. of violations of the SharedMemoryABI found while writing or reading
    // the buffer. This is an indication of either a bug in the producer(s) or
    // malicious producer(s).
    optional uint64 abi_violations = 9;

    // The fields below have been introduced in Android R.

    // Num. of times the service detected packet loss on a trace writer
    // sequence. This is usually caused by exhaustion of available chunks in the
    // writer process's SMB. Note that this relies on the client's TraceWriter
    // indicating this loss to the service -- packets lost for other reasons are
    // not reflected in this stat.
    optional uint64 trace_writer_packet_loss = 19;
  }

  // Stats for the TraceBuffer(s) of the current trace session.
  repeated BufferStats buffer_stats = 1;

  // Num. producers connected (whether they are involved in the current tracing
  // session or not).
  optional uint32 producers_connected = 2;

  // Num. producers ever seen for all trace sessions since startup (it's a good
  // proxy for inferring num. producers crashed / killed).
  optional uint64 producers_seen = 3;

  // Num. data sources registered for all trace sessions.
  optional uint32 data_sources_registered = 4;

  // Num. data sources ever seen for all trace sessions since startup.
  optional uint64 data_sources_seen = 5;

  // Num. concurrently active tracing sessions.
  optional uint32 tracing_sessions = 6;

  // Num. buffers for all tracing session (not just the current one). This will
  // be >= buffer_stats.size(), because the latter is only about the current
  // session.
  optional uint32 total_buffers = 7;

  // The fields below have been introduced in Android Q.

  // Num. chunks that were discarded by the service before attempting to commit
  // them to a buffer, e.g. because the producer specified an invalid buffer ID.
  optional uint64 chunks_discarded = 8;

  // Num. patches that were discarded by the service before attempting to apply
  // them to a buffer, e.g. because the producer specified an invalid buffer ID.
  optional uint64 patches_discarded = 9;

  // Packets that failed validation of the TrustedPacket. If this is > 0, there
  // is a bug in the producer.
  optional uint64 invalid_packets = 10;
}

// End of protos/perfetto/common/trace_stats.proto

// Begin of protos/perfetto/trace/android/android_log.proto

message AndroidLogPacket {
  message LogEvent {
    // The log buffer (e.g. MAIN, SYSTEM, RADIO) the event comes from.
    optional AndroidLogId log_id = 1;

    // PID (TGID), TID and UID of the task that emitted the event.
    optional int32 pid = 2;
    optional int32 tid = 3;
    optional int32 uid = 4;

    // Timestamp [ns]. The clock source is CLOCK_REALTIME, unlike many other
    // Perfetto trace events that instead use CLOCK_BOOTTIME. The trace
    // processor will take care of realigning clocks using the ClockSnapshot(s).
    optional uint64 timestamp = 5;

    // When log_id == LID_EVENTS, |tag| corresponds to the event name defined in
    // the second column of /system/etc/event-log-tags. For all other events,
    // |tag| is the app-specified argument passed to __android_log_write().
    optional string tag = 6;

    // Empty when log_id == LID_EVENTS.
    optional AndroidLogPriority prio = 7;

    // Empty when log_id == LID_EVENTS.
    optional string message = 8;

    message Arg {
      optional string name = 1;
      oneof value {
        int64 int_value = 2;
        float float_value = 3;
        string string_value = 4;
      }
    }
    // Only populated when log_id == LID_EVENTS.
    repeated Arg args = 9;
  }

  repeated LogEvent events = 1;

  // Stats are emitted only upon Flush() and are monotonic (i.e. they are
  // absolute counters since the beginning of the lifetime of the tracing
  // session and NOT relative to the previous Stats snapshot).
  message Stats {
    // Total number of log events seen, including errors and skipped entries
    // (num of events stored in the trace = total - failed - skipped).
    optional uint64 num_total = 1;

    // Parser failures.
    optional uint64 num_failed = 2;

    // Messages skipped due to filters.
    optional uint64 num_skipped = 3;
  }
  optional Stats stats = 2;
}

// End of protos/perfetto/trace/android/android_log.proto

// Begin of protos/perfetto/trace/android/gpu_mem_event.proto

// Generated by Android's GpuService.
message GpuMemTotalEvent {
  optional uint32 gpu_id = 1;
  optional uint32 pid = 2;
  optional uint64 size = 3;
}

// End of protos/perfetto/trace/android/gpu_mem_event.proto

// Begin of protos/perfetto/trace/android/graphics_frame_event.proto

// Generated by Android's SurfaceFlinger.
message GraphicsFrameEvent {
  enum BufferEventType {
    UNSPECIFIED = 0;
    DEQUEUE = 1;
    QUEUE = 2;
    POST = 3;
    ACQUIRE_FENCE = 4;
    LATCH = 5;
    // HWC will compose this buffer
    HWC_COMPOSITION_QUEUED = 6;
    // renderEngine composition
    FALLBACK_COMPOSITION = 7;
    PRESENT_FENCE = 8;
    RELEASE_FENCE = 9;
    MODIFY = 10;
    DETACH = 11;
    ATTACH = 12;
    CANCEL = 13;
  }

  message BufferEvent {
    optional uint32 frame_number = 1;
    optional BufferEventType type = 2;
    optional string layer_name = 3;
    // If no duration is set, the event is an instant event.
    optional uint64 duration_ns = 4;
    // Unique buffer identifier.
    optional uint32 buffer_id = 5;
  }

  optional BufferEvent buffer_event = 1;
}

// End of protos/perfetto/trace/android/graphics_frame_event.proto

// Begin of protos/perfetto/trace/android/initial_display_state.proto

message InitialDisplayState {
  // Same values as android.view.Display.STATE_*
  optional int32 display_state = 1;
  optional double brightness = 2;
}

// End of protos/perfetto/trace/android/initial_display_state.proto

// Begin of protos/perfetto/trace/android/packages_list.proto

message PackagesList {
  message PackageInfo {
    optional string name = 1;
    optional uint64 uid = 2;
    optional bool debuggable = 3;
    optional bool profileable_from_shell = 4;
    optional int64 version_code = 5;
  }

  repeated PackageInfo packages = 1;

  // At least one error occurred parsing the packages.list.
  optional bool parse_error = 2;

  // Failed to open / read packages.list.
  optional bool read_error = 3;
}

// End of protos/perfetto/trace/android/packages_list.proto

// Begin of protos/perfetto/trace/chrome/chrome_benchmark_metadata.proto

// This message is not intended to be written by the chrome on the device.
// It's emitted on the host by the telemetry benchmark infrastructure (it's a
// part of the trace that's written by the telemetry tracing agent).
message ChromeBenchmarkMetadata {
  // Time when the benchmark execution started (host unixtime in microseconds).
  optional int64 benchmark_start_time_us = 1;

  // Time when this particular story was run (host unixtime in microseconds).
  optional int64 story_run_time_us = 2;

  // Name of benchmark.
  optional string benchmark_name = 3;

  // Description of benchmark.
  optional string benchmark_description = 4;

  // Optional label.
  optional string label = 5;

  // Name of story.
  optional string story_name = 6;

  // List of story tags.
  repeated string story_tags = 7;

  // Index of the story run (>0 if the same story was run several times).
  optional int32 story_run_index = 8;

  // Whether this run failed.
  optional bool had_failures = 9;
}

// End of protos/perfetto/trace/chrome/chrome_benchmark_metadata.proto

// Begin of protos/perfetto/trace/chrome/chrome_metadata.proto

// Metadata for chrome traces.
message ChromeMetadataPacket {
  optional BackgroundTracingMetadata background_tracing_metadata = 1;

  // Version code of Chrome used by Android's Play Store. This field is only set
  // on Android.
  optional int32 chrome_version_code = 2;
}

// Metadata related to background tracing scenarios, states and triggers.
message BackgroundTracingMetadata {
  // Information about a trigger rule defined in the experiment config.
  message TriggerRule {
    enum TriggerType {
      TRIGGER_UNSPECIFIED = 0;

      // Traces are triggered by specific range of values of an UMA histogram.
      MONITOR_AND_DUMP_WHEN_SPECIFIC_HISTOGRAM_AND_VALUE = 1;

      // Traces are triggered by specific named events in chromium codebase,
      // like "second-update-failure".
      MONITOR_AND_DUMP_WHEN_TRIGGER_NAMED = 2;
    }
    optional TriggerType trigger_type = 1;

    // Configuration of histogram trigger.
    message HistogramRule {
      // UMA histogram name hash, same as HistogramEventProto.name_hash.
      optional fixed64 histogram_name_hash = 1;

      // Range of values of the histogram that activates trigger.
      optional int64 histogram_min_trigger = 2;
      optional int64 histogram_max_trigger = 3;
    }
    optional HistogramRule histogram_rule = 2;

    // Configuration of named trigger.
    message NamedRule {
      enum EventType {
        UNSPECIFIED = 0;
        SESSION_RESTORE = 1;
        NAVIGATION = 2;
        STARTUP = 3;
        REACHED_CODE = 4;

        TEST_RULE = 1000;
      }
      optional EventType event_type = 1;
    }
    optional NamedRule named_rule = 3;
  }

  // Specifies the rule that caused the trace to be uploaded.
  optional TriggerRule triggered_rule = 1;

  // List of all active triggers in current session, when trace was triggered.
  repeated TriggerRule active_rules = 2;
}

// End of protos/perfetto/trace/chrome/chrome_metadata.proto

// Begin of protos/perfetto/trace/chrome/chrome_trace_event.proto

message ChromeTracedValue {
  enum NestedType {
    DICT = 0;
    ARRAY = 1;
  }
  optional NestedType nested_type = 1;

  repeated string dict_keys = 2;
  repeated ChromeTracedValue dict_values = 3;
  repeated ChromeTracedValue array_values = 4;
  optional int32 int_value = 5;
  optional double double_value = 6;
  optional bool bool_value = 7;
  optional string string_value = 8;
}

message ChromeStringTableEntry {
  optional string value = 1;
  optional int32 index = 2;
}

// Deprecated, use TrackEvent protos instead.
message ChromeTraceEvent {
  message Arg {
    optional string name = 1;

    oneof value {
      bool bool_value = 2;
      uint64 uint_value = 3;
      int64 int_value = 4;
      double double_value = 5;
      string string_value = 6;
      // Pointers are stored in a separate type as the JSON output treats them
      // differently from other uint64 values.
      uint64 pointer_value = 7;
      string json_value = 8;
      ChromeTracedValue traced_value = 10;
    }

    // Takes precedence over |name| if set,
    // and is an index into |string_table|.
    optional uint32 name_index = 9;
  }

  optional string name = 1;
  optional int64 timestamp = 2;
  optional int32 phase = 3;
  optional int32 thread_id = 4;
  optional int64 duration = 5;
  optional int64 thread_duration = 6;
  optional string scope = 7;
  optional uint64 id = 8;
  optional uint32 flags = 9;
  optional string category_group_name = 10;
  optional int32 process_id = 11;
  optional int64 thread_timestamp = 12;
  optional uint64 bind_id = 13;

  repeated Arg args = 14;

  // Takes precedence over respectively |name| and
  // |category_group_name_index| if set,
  // and are indices into |string_table|.
  optional uint32 name_index = 15;
  optional uint32 category_group_name_index = 16;
}

message ChromeMetadata {
  optional string name = 1;

  oneof value {
    string string_value = 2;
    bool bool_value = 3;
    int64 int_value = 4;
    string json_value = 5;
  }
}

// Subtraces produced in legacy json format by Chrome tracing agents not yet
// updated to support the new binary format, e.g. ETW and CrOS ARC.
// TODO(eseckler): Update these agents to become perfetto producers.
message ChromeLegacyJsonTrace {
  enum TraceType {
    USER_TRACE = 0;

    // Deprecated.
    SYSTEM_TRACE = 1;
  }
  optional TraceType type = 1;
  optional string data = 2;
}

message ChromeEventBundle {
  // Deprecated, use TrackEvent protos instead.
  repeated ChromeTraceEvent trace_events = 1 [deprecated = true];
  repeated ChromeMetadata metadata = 2;
  // ftrace output from CrOS and Cast system tracing agents.
  // TODO(eseckler): Replace system traces with native perfetto service.
  repeated string legacy_ftrace_output = 4;
  repeated ChromeLegacyJsonTrace legacy_json_trace = 5;

  // Contents of a string table that's valid for
  // the whole ChromeEventBundle entry.
  repeated ChromeStringTableEntry string_table = 3 [deprecated = true];
}

// End of protos/perfetto/trace/chrome/chrome_trace_event.proto

// Begin of protos/perfetto/trace/clock_snapshot.proto

// A snapshot of clock readings to allow for trace alignment.
message ClockSnapshot {
  message Clock {
    // DEPRECATED. This enum has moved to ../common/builtin_clock.proto.
    enum BuiltinClocks {
      UNKNOWN = 0;
      REALTIME = 1;
      REALTIME_COARSE = 2;
      MONOTONIC = 3;
      MONOTONIC_COARSE = 4;
      MONOTONIC_RAW = 5;
      BOOTTIME = 6;
      BUILTIN_CLOCK_MAX_ID = 63;

      reserved 7, 8;
    }

    // Clock IDs have the following semantic:
    // [1, 63]:    Builtin types, see BuiltinClock from
    //             ../common/builtin_clock.proto.
    // [64, 127]:  User-defined clocks. These clocks are sequence-scoped. They
    //             are only valid within the same |trusted_packet_sequence_id|
    //             (i.e. only for TracePacket(s) emitted by the same TraceWriter
    //             that emitted the clock snapshot).
    // [128, MAX]: Reserved for future use. The idea is to allow global clock
    //             IDs and setting this ID to hash(full_clock_name) & ~127.
    optional uint32 clock_id = 1;

    // Absolute timestamp. Unit is ns unless specified otherwise by the
    // unit_multiplier_ns field below.
    optional uint64 timestamp = 2;

    // When true each TracePacket's timestamp should be interpreted as a delta
    // from the last TracePacket's timestamp (referencing this clock) emitted by
    // the same packet_sequence_id. Should only be used for user-defined
    // sequence-local clocks. The first packet timestamp after each
    // ClockSnapshot that contains this clock is relative to the |timestamp| in
    // the ClockSnapshot.
    optional bool is_incremental = 3;

    // Allows to specify a custom unit different than the default (ns) for this
    // clock domain. A multiplier of 1000 means that a timestamp = 3 should be
    // interpreted as 3000 ns = 3 us. All snapshots for the same clock within a
    // trace need to use the same unit.
    optional uint64 unit_multiplier_ns = 4;
  }
  repeated Clock clocks = 1;

  // The authoritative clock domain for the trace. Defaults to BOOTTIME, but can
  // be overridden in TraceConfig's builtin_data_sources. Trace processor will
  // attempt to translate packet/event timestamps from various data sources (and
  // their chosen clock domains) to this domain during import.
  optional BuiltinClock primary_trace_clock = 2;
}

// End of protos/perfetto/trace/clock_snapshot.proto

// Begin of protos/perfetto/common/descriptor.proto

// The protocol compiler can output a FileDescriptorSet containing the .proto
// files it parses.
message FileDescriptorSet {
  repeated FileDescriptorProto file = 1;
}

// Describes a complete .proto file.
message FileDescriptorProto {
  // file name, relative to root of source tree
  optional string name = 1;
  // e.g. "foo", "foo.bar", etc.
  optional string package = 2;

  // Names of files imported by this file.
  repeated string dependency = 3;
  // Indexes of the public imported files in the dependency list above.
  repeated int32 public_dependency = 10;
  // Indexes of the weak imported files in the dependency list.
  // For Google-internal migration only. Do not use.
  repeated int32 weak_dependency = 11;

  // All top-level definitions in this file.
  repeated DescriptorProto message_type = 4;
  repeated EnumDescriptorProto enum_type = 5;
  repeated FieldDescriptorProto extension = 7;

  reserved 6;
  reserved 8;
  reserved 9;
  reserved 12;
}

// Describes a message type.
message DescriptorProto {
  optional string name = 1;

  repeated FieldDescriptorProto field = 2;
  repeated FieldDescriptorProto extension = 6;

  repeated DescriptorProto nested_type = 3;
  repeated EnumDescriptorProto enum_type = 4;

  reserved 5;

  repeated OneofDescriptorProto oneof_decl = 8;

  reserved 7;

  // Range of reserved tag numbers. Reserved tag numbers may not be used by
  // fields or extension ranges in the same message. Reserved ranges may
  // not overlap.
  message ReservedRange {
    // Inclusive.
    optional int32 start = 1;
    // Exclusive.
    optional int32 end = 2;
  }
  repeated ReservedRange reserved_range = 9;
  // Reserved field names, which may not be used by fields in the same message.
  // A given name may only be reserved once.
  repeated string reserved_name = 10;
}

// Describes a field within a message.
message FieldDescriptorProto {
  enum Type {
    // 0 is reserved for errors.
    // Order is weird for historical reasons.
    TYPE_DOUBLE = 1;
    TYPE_FLOAT = 2;
    // Not ZigZag encoded.  Negative numbers take 10 bytes.  Use TYPE_SINT64 if
    // negative values are likely.
    TYPE_INT64 = 3;
    TYPE_UINT64 = 4;
    // Not ZigZag encoded.  Negative numbers take 10 bytes.  Use TYPE_SINT32 if
    // negative values are likely.
    TYPE_INT32 = 5;
    TYPE_FIXED64 = 6;
    TYPE_FIXED32 = 7;
    TYPE_BOOL = 8;
    TYPE_STRING = 9;
    // Tag-delimited aggregate.
    // Group type is deprecated and not supported in proto3. However, Proto3
    // implementations should still be able to parse the group wire format and
    // treat group fields as unknown fields.
    TYPE_GROUP = 10;
    // Length-delimited aggregate.
    TYPE_MESSAGE = 11;

    // New in version 2.
    TYPE_BYTES = 12;
    TYPE_UINT32 = 13;
    TYPE_ENUM = 14;
    TYPE_SFIXED32 = 15;
    TYPE_SFIXED64 = 16;
    // Uses ZigZag encoding.
    TYPE_SINT32 = 17;
    // Uses ZigZag encoding.
    TYPE_SINT64 = 18;
  };

  enum Label {
    // 0 is reserved for errors
    LABEL_OPTIONAL = 1;
    LABEL_REQUIRED = 2;
    LABEL_REPEATED = 3;
  };

  optional string name = 1;
  optional int32 number = 3;
  optional Label label = 4;

  // If type_name is set, this need not be set.  If both this and type_name
  // are set, this must be one of TYPE_ENUM, TYPE_MESSAGE or TYPE_GROUP.
  optional Type type = 5;

  // For message and enum types, this is the name of the type.  If the name
  // starts with a '.', it is fully-qualified.  Otherwise, C++-like scoping
  // rules are used to find the type (i.e. first the nested types within this
  // message are searched, then within the parent, on up to the root
  // namespace).
  optional string type_name = 6;

  // For extensions, this is the name of the type being extended.  It is
  // resolved in the same manner as type_name.
  optional string extendee = 2;

  // For numeric types, contains the original text representation of the value.
  // For booleans, "true" or "false".
  // For strings, contains the default text contents (not escaped in any way).
  // For bytes, contains the C escaped value.  All bytes >= 128 are escaped.
  // TODO(kenton):  Base-64 encode?
  optional string default_value = 7;

  // If set, gives the index of a oneof in the containing type's oneof_decl
  // list.  This field is a member of that oneof.
  optional int32 oneof_index = 9;

  reserved 10;

  reserved 8;
}

// Describes a oneof.
message OneofDescriptorProto {
  optional string name = 1;
  optional OneofOptions options = 2;
}

// Describes an enum type.
message EnumDescriptorProto {
  optional string name = 1;

  repeated EnumValueDescriptorProto value = 2;

  reserved 3;
  reserved 4;

  // Reserved enum value names, which may not be reused. A given name may only
  // be reserved once.
  repeated string reserved_name = 5;
}

// Describes a value within an enum.
message EnumValueDescriptorProto {
  optional string name = 1;
  optional int32 number = 2;

  reserved 3;
}

message OneofOptions {
  reserved 999;

  // Clients can define custom options in extensions of this message. See above.
  extensions 1000 to max;
}

// End of protos/perfetto/common/descriptor.proto

// Begin of protos/perfetto/trace/extension_descriptor.proto

// This message contains descriptors used to parse extension fields of
// TrackEvent.
//
// See docs/design-docs/extensions.md for more details.
message ExtensionDescriptor {
  optional FileDescriptorSet extension_set = 1;
}

// End of protos/perfetto/trace/extension_descriptor.proto

// Begin of protos/perfetto/trace/filesystem/inode_file_map.proto

// Represents the mapping between inode numbers in a block device and their path
// on the filesystem
message InodeFileMap {
  // Representation of Entry
  message Entry {
    optional uint64 inode_number = 1;

    // The path to the file, e.g. "etc/file.xml"
    // List of strings for multiple hardlinks
    repeated string paths = 2;

    // The file type
    enum Type {
      UNKNOWN = 0;
      FILE = 1;
      DIRECTORY = 2;
    }
    optional Type type = 3;
  }

  optional uint64 block_device_id = 1;

  // The mount points of the block device, e.g. ["system"].
  repeated string mount_points = 2;

  // The list of all the entries from the block device
  repeated Entry entries = 3;
}

// End of protos/perfetto/trace/filesystem/inode_file_map.proto

// Begin of protos/perfetto/trace/ftrace/binder.proto

message BinderTransactionFtraceEvent {
  optional int32 debug_id = 1;
  optional int32 target_node = 2;
  optional int32 to_proc = 3;
  optional int32 to_thread = 4;
  optional int32 reply = 5;
  optional uint32 code = 6;
  optional uint32 flags = 7;
}
message BinderTransactionReceivedFtraceEvent {
  optional int32 debug_id = 1;
}
message BinderSetPriorityFtraceEvent {
  optional int32 proc = 1;
  optional int32 thread = 2;
  optional uint32 old_prio = 3;
  optional uint32 new_prio = 4;
  optional uint32 desired_prio = 5;
}
message BinderLockFtraceEvent {
  optional string tag = 1;
}
message BinderLockedFtraceEvent {
  optional string tag = 1;
}
message BinderUnlockFtraceEvent {
  optional string tag = 1;
}
message BinderTransactionAllocBufFtraceEvent {
  optional uint64 data_size = 1;
  optional int32 debug_id = 2;
  optional uint64 offsets_size = 3;
}

// End of protos/perfetto/trace/ftrace/binder.proto

// Begin of protos/perfetto/trace/ftrace/block.proto

message BlockRqIssueFtraceEvent {
  optional uint64 dev = 1;
  optional uint64 sector = 2;
  optional uint32 nr_sector = 3;
  optional uint32 bytes = 4;
  optional string rwbs = 5;
  optional string comm = 6;
  optional string cmd = 7;
}
message BlockBioBackmergeFtraceEvent {
  optional uint64 dev = 1;
  optional uint64 sector = 2;
  optional uint32 nr_sector = 3;
  optional string rwbs = 4;
  optional string comm = 5;
}
message BlockBioBounceFtraceEvent {
  optional uint64 dev = 1;
  optional uint64 sector = 2;
  optional uint32 nr_sector = 3;
  optional string rwbs = 4;
  optional string comm = 5;
}
message BlockBioCompleteFtraceEvent {
  optional uint64 dev = 1;
  optional uint64 sector = 2;
  optional uint32 nr_sector = 3;
  optional int32 error = 4;
  optional string rwbs = 5;
}
message BlockBioFrontmergeFtraceEvent {
  optional uint64 dev = 1;
  optional uint64 sector = 2;
  optional uint32 nr_sector = 3;
  optional string rwbs = 4;
  optional string comm = 5;
}
message BlockBioQueueFtraceEvent {
  optional uint64 dev = 1;
  optional uint64 sector = 2;
  optional uint32 nr_sector = 3;
  optional string rwbs = 4;
  optional string comm = 5;
}
message BlockBioRemapFtraceEvent {
  optional uint64 dev = 1;
  optional uint64 sector = 2;
  optional uint32 nr_sector = 3;
  optional uint64 old_dev = 4;
  optional uint64 old_sector = 5;
  optional string rwbs = 6;
}
message BlockDirtyBufferFtraceEvent {
  optional uint64 dev = 1;
  optional uint64 sector = 2;
  optional uint64 size = 3;
}
message BlockGetrqFtraceEvent {
  optional uint64 dev = 1;
  optional uint64 sector = 2;
  optional uint32 nr_sector = 3;
  optional string rwbs = 4;
  optional string comm = 5;
}
message BlockPlugFtraceEvent {
  optional string comm = 1;
}
message BlockRqAbortFtraceEvent {
  optional uint64 dev = 1;
  optional uint64 sector = 2;
  optional uint32 nr_sector = 3;
  optional int32 errors = 4;
  optional string rwbs = 5;
  optional string cmd = 6;
}
message BlockRqCompleteFtraceEvent {
  optional uint64 dev = 1;
  optional uint64 sector = 2;
  optional uint32 nr_sector = 3;
  optional int32 errors = 4;
  optional string rwbs = 5;
  optional string cmd = 6;
}
message BlockRqInsertFtraceEvent {
  optional uint64 dev = 1;
  optional uint64 sector = 2;
  optional uint32 nr_sector = 3;
  optional uint32 bytes = 4;
  optional string rwbs = 5;
  optional string comm = 6;
  optional string cmd = 7;
}
message BlockRqRemapFtraceEvent {
  optional uint64 dev = 1;
  optional uint64 sector = 2;
  optional uint32 nr_sector = 3;
  optional uint64 old_dev = 4;
  optional uint64 old_sector = 5;
  optional uint32 nr_bios = 6;
  optional string rwbs = 7;
}
message BlockRqRequeueFtraceEvent {
  optional uint64 dev = 1;
  optional uint64 sector = 2;
  optional uint32 nr_sector = 3;
  optional int32 errors = 4;
  optional string rwbs = 5;
  optional string cmd = 6;
}
message BlockSleeprqFtraceEvent {
  optional uint64 dev = 1;
  optional uint64 sector = 2;
  optional uint32 nr_sector = 3;
  optional string rwbs = 4;
  optional string comm = 5;
}
message BlockSplitFtraceEvent {
  optional uint64 dev = 1;
  optional uint64 sector = 2;
  optional uint64 new_sector = 3;
  optional string rwbs = 4;
  optional string comm = 5;
}
message BlockTouchBufferFtraceEvent {
  optional uint64 dev = 1;
  optional uint64 sector = 2;
  optional uint64 size = 3;
}
message BlockUnplugFtraceEvent {
  optional int32 nr_rq = 1;
  optional string comm = 2;
}

// End of protos/perfetto/trace/ftrace/block.proto

// Begin of protos/perfetto/trace/ftrace/cgroup.proto

message CgroupAttachTaskFtraceEvent {
  optional int32 dst_root = 1;
  optional int32 dst_id = 2;
  optional int32 pid = 3;
  optional string comm = 4;
  optional string cname = 5;
}
message CgroupMkdirFtraceEvent {
  optional int32 root = 1;
  optional int32 id = 2;
  optional string cname = 3;
}
message CgroupRemountFtraceEvent {
  optional int32 root = 1;
  optional uint32 ss_mask = 2;
  optional string name = 3;
}
message CgroupRmdirFtraceEvent {
  optional int32 root = 1;
  optional int32 id = 2;
  optional string cname = 3;
}
message CgroupTransferTasksFtraceEvent {
  optional int32 dst_root = 1;
  optional int32 dst_id = 2;
  optional int32 pid = 3;
  optional string comm = 4;
  optional string cname = 5;
}
message CgroupDestroyRootFtraceEvent {
  optional int32 root = 1;
  optional uint32 ss_mask = 2;
  optional string name = 3;
}
message CgroupReleaseFtraceEvent {
  optional int32 root = 1;
  optional int32 id = 2;
  optional string cname = 3;
}
message CgroupRenameFtraceEvent {
  optional int32 root = 1;
  optional int32 id = 2;
  optional string cname = 3;
}
message CgroupSetupRootFtraceEvent {
  optional int32 root = 1;
  optional uint32 ss_mask = 2;
  optional string name = 3;
}

// End of protos/perfetto/trace/ftrace/cgroup.proto

// Begin of protos/perfetto/trace/ftrace/clk.proto

message ClkEnableFtraceEvent {
  optional string name = 1;
}
message ClkDisableFtraceEvent {
  optional string name = 1;
}
message ClkSetRateFtraceEvent {
  optional string name = 1;
  optional uint64 rate = 2;
}

// End of protos/perfetto/trace/ftrace/clk.proto

// Begin of protos/perfetto/trace/ftrace/compaction.proto

message MmCompactionBeginFtraceEvent {
  optional uint64 zone_start = 1;
  optional uint64 migrate_pfn = 2;
  optional uint64 free_pfn = 3;
  optional uint64 zone_end = 4;
  optional uint32 sync = 5;
}
message MmCompactionDeferCompactionFtraceEvent {
  optional int32 nid = 1;
  optional uint32 idx = 2;
  optional int32 order = 3;
  optional uint32 considered = 4;
  optional uint32 defer_shift = 5;
  optional int32 order_failed = 6;
}
message MmCompactionDeferredFtraceEvent {
  optional int32 nid = 1;
  optional uint32 idx = 2;
  optional int32 order = 3;
  optional uint32 considered = 4;
  optional uint32 defer_shift = 5;
  optional int32 order_failed = 6;
}
message MmCompactionDeferResetFtraceEvent {
  optional int32 nid = 1;
  optional uint32 idx = 2;
  optional int32 order = 3;
  optional uint32 considered = 4;
  optional uint32 defer_shift = 5;
  optional int32 order_failed = 6;
}
message MmCompactionEndFtraceEvent {
  optional uint64 zone_start = 1;
  optional uint64 migrate_pfn = 2;
  optional uint64 free_pfn = 3;
  optional uint64 zone_end = 4;
  optional uint32 sync = 5;
  optional int32 status = 6;
}
message MmCompactionFinishedFtraceEvent {
  optional int32 nid = 1;
  optional uint32 idx = 2;
  optional int32 order = 3;
  optional int32 ret = 4;
}
message MmCompactionIsolateFreepagesFtraceEvent {
  optional uint64 start_pfn = 1;
  optional uint64 end_pfn = 2;
  optional uint64 nr_scanned = 3;
  optional uint64 nr_taken = 4;
}
message MmCompactionIsolateMigratepagesFtraceEvent {
  optional uint64 start_pfn = 1;
  optional uint64 end_pfn = 2;
  optional uint64 nr_scanned = 3;
  optional uint64 nr_taken = 4;
}
message MmCompactionKcompactdSleepFtraceEvent {
  optional int32 nid = 1;
}
message MmCompactionKcompactdWakeFtraceEvent {
  optional int32 nid = 1;
  optional int32 order = 2;
  optional uint32 classzone_idx = 3;
}
message MmCompactionMigratepagesFtraceEvent {
  optional uint64 nr_migrated = 1;
  optional uint64 nr_failed = 2;
}
message MmCompactionSuitableFtraceEvent {
  optional int32 nid = 1;
  optional uint32 idx = 2;
  optional int32 order = 3;
  optional int32 ret = 4;
}
message MmCompactionTryToCompactPagesFtraceEvent {
  optional int32 order = 1;
  optional uint32 gfp_mask = 2;
  optional uint32 mode = 3;
}
message MmCompactionWakeupKcompactdFtraceEvent {
  optional int32 nid = 1;
  optional int32 order = 2;
  optional uint32 classzone_idx = 3;
}

// End of protos/perfetto/trace/ftrace/compaction.proto

// Begin of protos/perfetto/trace/ftrace/cpuhp.proto

message CpuhpExitFtraceEvent {
  optional uint32 cpu = 1;
  optional int32 idx = 2;
  optional int32 ret = 3;
  optional int32 state = 4;
}
message CpuhpMultiEnterFtraceEvent {
  optional uint32 cpu = 1;
  optional uint64 fun = 2;
  optional int32 idx = 3;
  optional int32 target = 4;
}
message CpuhpEnterFtraceEvent {
  optional uint32 cpu = 1;
  optional uint64 fun = 2;
  optional int32 idx = 3;
  optional int32 target = 4;
}
message CpuhpLatencyFtraceEvent {
  optional uint32 cpu = 1;
  optional int32 ret = 2;
  optional uint32 state = 3;
  optional uint64 time = 4;
}

// End of protos/perfetto/trace/ftrace/cpuhp.proto

// Begin of protos/perfetto/trace/ftrace/ext4.proto

message Ext4DaWriteBeginFtraceEvent {
  optional uint64 dev = 1;
  optional uint64 ino = 2;
  optional int64 pos = 3;
  optional uint32 len = 4;
  optional uint32 flags = 5;
}
message Ext4DaWriteEndFtraceEvent {
  optional uint64 dev = 1;
  optional uint64 ino = 2;
  optional int64 pos = 3;
  optional uint32 len = 4;
  optional uint32 copied = 5;
}
message Ext4SyncFileEnterFtraceEvent {
  optional uint64 dev = 1;
  optional uint64 ino = 2;
  optional uint64 parent = 3;
  optional int32 datasync = 4;
}
message Ext4SyncFileExitFtraceEvent {
  optional uint64 dev = 1;
  optional uint64 ino = 2;
  optional int32 ret = 3;
}
message Ext4AllocDaBlocksFtraceEvent {
  optional uint64 dev = 1;
  optional uint64 ino = 2;
  optional uint32 data_blocks = 3;
  optional uint32 meta_blocks = 4;
}
message Ext4AllocateBlocksFtraceEvent {
  optional uint64 dev = 1;
  optional uint64 ino = 2;
  optional uint64 block = 3;
  optional uint32 len = 4;
  optional uint32 logical = 5;
  optional uint32 lleft = 6;
  optional uint32 lright = 7;
  optional uint64 goal = 8;
  optional uint64 pleft = 9;
  optional uint64 pright = 10;
  optional uint32 flags = 11;
}
message Ext4AllocateInodeFtraceEvent {
  optional uint64 dev = 1;
  optional uint64 ino = 2;
  optional uint64 dir = 3;
  optional uint32 mode = 4;
}
message Ext4BeginOrderedTruncateFtraceEvent {
  optional uint64 dev = 1;
  optional uint64 ino = 2;
  optional int64 new_size = 3;
}
message Ext4CollapseRangeFtraceEvent {
  optional uint64 dev = 1;
  optional uint64 ino = 2;
  optional int64 offset = 3;
  optional int64 len = 4;
}
message Ext4DaReleaseSpaceFtraceEvent {
  optional uint64 dev = 1;
  optional uint64 ino = 2;
  optional uint64 i_blocks = 3;
  optional int32 freed_blocks = 4;
  optional int32 reserved_data_blocks = 5;
  optional int32 reserved_meta_blocks = 6;
  optional int32 allocated_meta_blocks = 7;
  optional uint32 mode = 8;
}
message Ext4DaReserveSpaceFtraceEvent {
  optional uint64 dev = 1;
  optional uint64 ino = 2;
  optional uint64 i_blocks = 3;
  optional int32 reserved_data_blocks = 4;
  optional int32 reserved_meta_blocks = 5;
  optional uint32 mode = 6;
  optional int32 md_needed = 7;
}
message Ext4DaUpdateReserveSpaceFtraceEvent {
  optional uint64 dev = 1;
  optional uint64 ino = 2;
  optional uint64 i_blocks = 3;
  optional int32 used_blocks = 4;
  optional int32 reserved_data_blocks = 5;
  optional int32 reserved_meta_blocks = 6;
  optional int32 allocated_meta_blocks = 7;
  optional int32 quota_claim = 8;
  optional uint32 mode = 9;
}
message Ext4DaWritePagesFtraceEvent {
  optional uint64 dev = 1;
  optional uint64 ino = 2;
  optional uint64 first_page = 3;
  optional int64 nr_to_write = 4;
  optional int32 sync_mode = 5;
  optional uint64 b_blocknr = 6;
  optional uint32 b_size = 7;
  optional uint32 b_state = 8;
  optional int32 io_done = 9;
  optional int32 pages_written = 10;
}
message Ext4DaWritePagesExtentFtraceEvent {
  optional uint64 dev = 1;
  optional uint64 ino = 2;
  optional uint64 lblk = 3;
  optional uint32 len = 4;
  optional uint32 flags = 5;
}
message Ext4DirectIOEnterFtraceEvent {
  optional uint64 dev = 1;
  optional uint64 ino = 2;
  optional int64 pos = 3;
  optional uint64 len = 4;
  optional int32 rw = 5;
}
message Ext4DirectIOExitFtraceEvent {
  optional uint64 dev = 1;
  optional uint64 ino = 2;
  optional int64 pos = 3;
  optional uint64 len = 4;
  optional int32 rw = 5;
  optional int32 ret = 6;
}
message Ext4DiscardBlocksFtraceEvent {
  optional uint64 dev = 1;
  optional uint64 blk = 2;
  optional uint64 count = 3;
}
message Ext4DiscardPreallocationsFtraceEvent {
  optional uint64 dev = 1;
  optional uint64 ino = 2;
}
message Ext4DropInodeFtraceEvent {
  optional uint64 dev = 1;
  optional uint64 ino = 2;
  optional int32 drop = 3;
}
message Ext4EsCacheExtentFtraceEvent {
  optional uint64 dev = 1;
  optional uint64 ino = 2;
  optional uint32 lblk = 3;
  optional uint32 len = 4;
  optional uint64 pblk = 5;
  optional uint32 status = 6;
}
message Ext4EsFindDelayedExtentRangeEnterFtraceEvent {
  optional uint64 dev = 1;
  optional uint64 ino = 2;
  optional uint32 lblk = 3;
}
message Ext4EsFindDelayedExtentRangeExitFtraceEvent {
  optional uint64 dev = 1;
  optional uint64 ino = 2;
  optional uint32 lblk = 3;
  optional uint32 len = 4;
  optional uint64 pblk = 5;
  optional uint64 status = 6;
}
message Ext4EsInsertExtentFtraceEvent {
  optional uint64 dev = 1;
  optional uint64 ino = 2;
  optional uint32 lblk = 3;
  optional uint32 len = 4;
  optional uint64 pblk = 5;
  optional uint64 status = 6;
}
message Ext4EsLookupExtentEnterFtraceEvent {
  optional uint64 dev = 1;
  optional uint64 ino = 2;
  optional uint32 lblk = 3;
}
message Ext4EsLookupExtentExitFtraceEvent {
  optional uint64 dev = 1;
  optional uint64 ino = 2;
  optional uint32 lblk = 3;
  optional uint32 len = 4;
  optional uint64 pblk = 5;
  optional uint64 status = 6;
  optional int32 found = 7;
}
message Ext4EsRemoveExtentFtraceEvent {
  optional uint64 dev = 1;
  optional uint64 ino = 2;
  optional int64 lblk = 3;
  optional int64 len = 4;
}
message Ext4EsShrinkFtraceEvent {
  optional uint64 dev = 1;
  optional int32 nr_shrunk = 2;
  optional uint64 scan_time = 3;
  optional int32 nr_skipped = 4;
  optional int32 retried = 5;
}
message Ext4EsShrinkCountFtraceEvent {
  optional uint64 dev = 1;
  optional int32 nr_to_scan = 2;
  optional int32 cache_cnt = 3;
}
message Ext4EsShrinkScanEnterFtraceEvent {
  optional uint64 dev = 1;
  optional int32 nr_to_scan = 2;
  optional int32 cache_cnt = 3;
}
message Ext4EsShrinkScanExitFtraceEvent {
  optional uint64 dev = 1;
  optional int32 nr_shrunk = 2;
  optional int32 cache_cnt = 3;
}
message Ext4EvictInodeFtraceEvent {
  optional uint64 dev = 1;
  optional uint64 ino = 2;
  optional int32 nlink = 3;
}
message Ext4ExtConvertToInitializedEnterFtraceEvent {
  optional uint64 dev = 1;
  optional uint64 ino = 2;
  optional uint32 m_lblk = 3;
  optional uint32 m_len = 4;
  optional uint32 u_lblk = 5;
  optional uint32 u_len = 6;
  optional uint64 u_pblk = 7;
}
message Ext4ExtConvertToInitializedFastpathFtraceEvent {
  optional uint64 dev = 1;
  optional uint64 ino = 2;
  optional uint32 m_lblk = 3;
  optional uint32 m_len = 4;
  optional uint32 u_lblk = 5;
  optional uint32 u_len = 6;
  optional uint64 u_pblk = 7;
  optional uint32 i_lblk = 8;
  optional uint32 i_len = 9;
  optional uint64 i_pblk = 10;
}
message Ext4ExtHandleUnwrittenExtentsFtraceEvent {
  optional uint64 dev = 1;
  optional uint64 ino = 2;
  optional int32 flags = 3;
  optional uint32 lblk = 4;
  optional uint64 pblk = 5;
  optional uint32 len = 6;
  optional uint32 allocated = 7;
  optional uint64 newblk = 8;
}
message Ext4ExtInCacheFtraceEvent {
  optional uint64 dev = 1;
  optional uint64 ino = 2;
  optional uint32 lblk = 3;
  optional int32 ret = 4;
}
message Ext4ExtLoadExtentFtraceEvent {
  optional uint64 dev = 1;
  optional uint64 ino = 2;
  optional uint64 pblk = 3;
  optional uint32 lblk = 4;
}
message Ext4ExtMapBlocksEnterFtraceEvent {
  optional uint64 dev = 1;
  optional uint64 ino = 2;
  optional uint32 lblk = 3;
  optional uint32 len = 4;
  optional uint32 flags = 5;
}
message Ext4ExtMapBlocksExitFtraceEvent {
  optional uint64 dev = 1;
  optional uint64 ino = 2;
  optional uint32 flags = 3;
  optional uint64 pblk = 4;
  optional uint32 lblk = 5;
  optional uint32 len = 6;
  optional uint32 mflags = 7;
  optional int32 ret = 8;
}
message Ext4ExtPutInCacheFtraceEvent {
  optional uint64 dev = 1;
  optional uint64 ino = 2;
  optional uint32 lblk = 3;
  optional uint32 len = 4;
  optional uint64 start = 5;
}
message Ext4ExtRemoveSpaceFtraceEvent {
  optional uint64 dev = 1;
  optional uint64 ino = 2;
  optional uint32 start = 3;
  optional uint32 end = 4;
  optional int32 depth = 5;
}
message Ext4ExtRemoveSpaceDoneFtraceEvent {
  optional uint64 dev = 1;
  optional uint64 ino = 2;
  optional uint32 start = 3;
  optional uint32 end = 4;
  optional int32 depth = 5;
  optional int64 partial = 6;
  optional uint32 eh_entries = 7;
}
message Ext4ExtRmIdxFtraceEvent {
  optional uint64 dev = 1;
  optional uint64 ino = 2;
  optional uint64 pblk = 3;
}
message Ext4ExtRmLeafFtraceEvent {
  optional uint64 dev = 1;
  optional uint64 ino = 2;
  optional int64 partial = 3;
  optional uint32 start = 4;
  optional uint32 ee_lblk = 5;
  optional uint64 ee_pblk = 6;
  optional int32 ee_len = 7;
}
message Ext4ExtShowExtentFtraceEvent {
  optional uint64 dev = 1;
  optional uint64 ino = 2;
  optional uint64 pblk = 3;
  optional uint32 lblk = 4;
  optional uint32 len = 5;
}
message Ext4FallocateEnterFtraceEvent {
  optional uint64 dev = 1;
  optional uint64 ino = 2;
  optional int64 offset = 3;
  optional int64 len = 4;
  optional int32 mode = 5;
  optional int64 pos = 6;
}
message Ext4FallocateExitFtraceEvent {
  optional uint64 dev = 1;
  optional uint64 ino = 2;
  optional int64 pos = 3;
  optional uint32 blocks = 4;
  optional int32 ret = 5;
}
message Ext4FindDelallocRangeFtraceEvent {
  optional uint64 dev = 1;
  optional uint64 ino = 2;
  optional uint32 from = 3;
  optional uint32 to = 4;
  optional int32 reverse = 5;
  optional int32 found = 6;
  optional uint32 found_blk = 7;
}
message Ext4ForgetFtraceEvent {
  optional uint64 dev = 1;
  optional uint64 ino = 2;
  optional uint64 block = 3;
  optional int32 is_metadata = 4;
  optional uint32 mode = 5;
}
message Ext4FreeBlocksFtraceEvent {
  optional uint64 dev = 1;
  optional uint64 ino = 2;
  optional uint64 block = 3;
  optional uint64 count = 4;
  optional int32 flags = 5;
  optional uint32 mode = 6;
}
message Ext4FreeInodeFtraceEvent {
  optional uint64 dev = 1;
  optional uint64 ino = 2;
  optional uint32 uid = 3;
  optional uint32 gid = 4;
  optional uint64 blocks = 5;
  optional uint32 mode = 6;
}
message Ext4GetImpliedClusterAllocExitFtraceEvent {
  optional uint64 dev = 1;
  optional uint32 flags = 2;
  optional uint32 lblk = 3;
  optional uint64 pblk = 4;
  optional uint32 len = 5;
  optional int32 ret = 6;
}
message Ext4GetReservedClusterAllocFtraceEvent {
  optional uint64 dev = 1;
  optional uint64 ino = 2;
  optional uint32 lblk = 3;
  optional uint32 len = 4;
}
message Ext4IndMapBlocksEnterFtraceEvent {
  optional uint64 dev = 1;
  optional uint64 ino = 2;
  optional uint32 lblk = 3;
  optional uint32 len = 4;
  optional uint32 flags = 5;
}
message Ext4IndMapBlocksExitFtraceEvent {
  optional uint64 dev = 1;
  optional uint64 ino = 2;
  optional uint32 flags = 3;
  optional uint64 pblk = 4;
  optional uint32 lblk = 5;
  optional uint32 len = 6;
  optional uint32 mflags = 7;
  optional int32 ret = 8;
}
message Ext4InsertRangeFtraceEvent {
  optional uint64 dev = 1;
  optional uint64 ino = 2;
  optional int64 offset = 3;
  optional int64 len = 4;
}
message Ext4InvalidatepageFtraceEvent {
  optional uint64 dev = 1;
  optional uint64 ino = 2;
  optional uint64 index = 3;
  optional uint64 offset = 4;
  optional uint32 length = 5;
}
message Ext4JournalStartFtraceEvent {
  optional uint64 dev = 1;
  optional uint64 ip = 2;
  optional int32 blocks = 3;
  optional int32 rsv_blocks = 4;
  optional int32 nblocks = 5;
}
message Ext4JournalStartReservedFtraceEvent {
  optional uint64 dev = 1;
  optional uint64 ip = 2;
  optional int32 blocks = 3;
}
message Ext4JournalledInvalidatepageFtraceEvent {
  optional uint64 dev = 1;
  optional uint64 ino = 2;
  optional uint64 index = 3;
  optional uint64 offset = 4;
  optional uint32 length = 5;
}
message Ext4JournalledWriteEndFtraceEvent {
  optional uint64 dev = 1;
  optional uint64 ino = 2;
  optional int64 pos = 3;
  optional uint32 len = 4;
  optional uint32 copied = 5;
}
message Ext4LoadInodeFtraceEvent {
  optional uint64 dev = 1;
  optional uint64 ino = 2;
}
message Ext4LoadInodeBitmapFtraceEvent {
  optional uint64 dev = 1;
  optional uint32 group = 2;
}
message Ext4MarkInodeDirtyFtraceEvent {
  optional uint64 dev = 1;
  optional uint64 ino = 2;
  optional uint64 ip = 3;
}
message Ext4MbBitmapLoadFtraceEvent {
  optional uint64 dev = 1;
  optional uint32 group = 2;
}
message Ext4MbBuddyBitmapLoadFtraceEvent {
  optional uint64 dev = 1;
  optional uint32 group = 2;
}
message Ext4MbDiscardPreallocationsFtraceEvent {
  optional uint64 dev = 1;
  optional int32 needed = 2;
}
message Ext4MbNewGroupPaFtraceEvent {
  optional uint64 dev = 1;
  optional uint64 ino = 2;
  optional uint64 pa_pstart = 3;
  optional uint64 pa_lstart = 4;
  optional uint32 pa_len = 5;
}
message Ext4MbNewInodePaFtraceEvent {
  optional uint64 dev = 1;
  optional uint64 ino = 2;
  optional uint64 pa_pstart = 3;
  optional uint64 pa_lstart = 4;
  optional uint32 pa_len = 5;
}
message Ext4MbReleaseGroupPaFtraceEvent {
  optional uint64 dev = 1;
  optional uint64 pa_pstart = 2;
  optional uint32 pa_len = 3;
}
message Ext4MbReleaseInodePaFtraceEvent {
  optional uint64 dev = 1;
  optional uint64 ino = 2;
  optional uint64 block = 3;
  optional uint32 count = 4;
}
message Ext4MballocAllocFtraceEvent {
  optional uint64 dev = 1;
  optional uint64 ino = 2;
  optional uint32 orig_logical = 3;
  optional int32 orig_start = 4;
  optional uint32 orig_group = 5;
  optional int32 orig_len = 6;
  optional uint32 goal_logical = 7;
  optional int32 goal_start = 8;
  optional uint32 goal_group = 9;
  optional int32 goal_len = 10;
  optional uint32 result_logical = 11;
  optional int32 result_start = 12;
  optional uint32 result_group = 13;
  optional int32 result_len = 14;
  optional uint32 found = 15;
  optional uint32 groups = 16;
  optional uint32 buddy = 17;
  optional uint32 flags = 18;
  optional uint32 tail = 19;
  optional uint32 cr = 20;
}
message Ext4MballocDiscardFtraceEvent {
  optional uint64 dev = 1;
  optional uint64 ino = 2;
  optional int32 result_start = 3;
  optional uint32 result_group = 4;
  optional int32 result_len = 5;
}
message Ext4MballocFreeFtraceEvent {
  optional uint64 dev = 1;
  optional uint64 ino = 2;
  optional int32 result_start = 3;
  optional uint32 result_group = 4;
  optional int32 result_len = 5;
}
message Ext4MballocPreallocFtraceEvent {
  optional uint64 dev = 1;
  optional uint64 ino = 2;
  optional uint32 orig_logical = 3;
  optional int32 orig_start = 4;
  optional uint32 orig_group = 5;
  optional int32 orig_len = 6;
  optional uint32 result_logical = 7;
  optional int32 result_start = 8;
  optional uint32 result_group = 9;
  optional int32 result_len = 10;
}
message Ext4OtherInodeUpdateTimeFtraceEvent {
  optional uint64 dev = 1;
  optional uint64 ino = 2;
  optional uint64 orig_ino = 3;
  optional uint32 uid = 4;
  optional uint32 gid = 5;
  optional uint32 mode = 6;
}
message Ext4PunchHoleFtraceEvent {
  optional uint64 dev = 1;
  optional uint64 ino = 2;
  optional int64 offset = 3;
  optional int64 len = 4;
  optional int32 mode = 5;
}
message Ext4ReadBlockBitmapLoadFtraceEvent {
  optional uint64 dev = 1;
  optional uint32 group = 2;
}
message Ext4ReadpageFtraceEvent {
  optional uint64 dev = 1;
  optional uint64 ino = 2;
  optional uint64 index = 3;
}
message Ext4ReleasepageFtraceEvent {
  optional uint64 dev = 1;
  optional uint64 ino = 2;
  optional uint64 index = 3;
}
message Ext4RemoveBlocksFtraceEvent {
  optional uint64 dev = 1;
  optional uint64 ino = 2;
  optional uint32 from = 3;
  optional uint32 to = 4;
  optional int64 partial = 5;
  optional uint64 ee_pblk = 6;
  optional uint32 ee_lblk = 7;
  optional uint32 ee_len = 8;
}
message Ext4RequestBlocksFtraceEvent {
  optional uint64 dev = 1;
  optional uint64 ino = 2;
  optional uint32 len = 3;
  optional uint32 logical = 4;
  optional uint32 lleft = 5;
  optional uint32 lright = 6;
  optional uint64 goal = 7;
  optional uint64 pleft = 8;
  optional uint64 pright = 9;
  optional uint32 flags = 10;
}
message Ext4RequestInodeFtraceEvent {
  optional uint64 dev = 1;
  optional uint64 dir = 2;
  optional uint32 mode = 3;
}
message Ext4SyncFsFtraceEvent {
  optional uint64 dev = 1;
  optional int32 wait = 2;
}
message Ext4TrimAllFreeFtraceEvent {
  optional int32 dev_major = 1;
  optional int32 dev_minor = 2;
  optional uint32 group = 3;
  optional int32 start = 4;
  optional int32 len = 5;
}
message Ext4TrimExtentFtraceEvent {
  optional int32 dev_major = 1;
  optional int32 dev_minor = 2;
  optional uint32 group = 3;
  optional int32 start = 4;
  optional int32 len = 5;
}
message Ext4TruncateEnterFtraceEvent {
  optional uint64 dev = 1;
  optional uint64 ino = 2;
  optional uint64 blocks = 3;
}
message Ext4TruncateExitFtraceEvent {
  optional uint64 dev = 1;
  optional uint64 ino = 2;
  optional uint64 blocks = 3;
}
message Ext4UnlinkEnterFtraceEvent {
  optional uint64 dev = 1;
  optional uint64 ino = 2;
  optional uint64 parent = 3;
  optional int64 size = 4;
}
message Ext4UnlinkExitFtraceEvent {
  optional uint64 dev = 1;
  optional uint64 ino = 2;
  optional int32 ret = 3;
}
message Ext4WriteBeginFtraceEvent {
  optional uint64 dev = 1;
  optional uint64 ino = 2;
  optional int64 pos = 3;
  optional uint32 len = 4;
  optional uint32 flags = 5;
}
message Ext4WriteEndFtraceEvent {
  optional uint64 dev = 1;
  optional uint64 ino = 2;
  optional int64 pos = 3;
  optional uint32 len = 4;
  optional uint32 copied = 5;
}
message Ext4WritepageFtraceEvent {
  optional uint64 dev = 1;
  optional uint64 ino = 2;
  optional uint64 index = 3;
}
message Ext4WritepagesFtraceEvent {
  optional uint64 dev = 1;
  optional uint64 ino = 2;
  optional int64 nr_to_write = 3;
  optional int64 pages_skipped = 4;
  optional int64 range_start = 5;
  optional int64 range_end = 6;
  optional uint64 writeback_index = 7;
  optional int32 sync_mode = 8;
  optional uint32 for_kupdate = 9;
  optional uint32 range_cyclic = 10;
}
message Ext4WritepagesResultFtraceEvent {
  optional uint64 dev = 1;
  optional uint64 ino = 2;
  optional int32 ret = 3;
  optional int32 pages_written = 4;
  optional int64 pages_skipped = 5;
  optional uint64 writeback_index = 6;
  optional int32 sync_mode = 7;
}
message Ext4ZeroRangeFtraceEvent {
  optional uint64 dev = 1;
  optional uint64 ino = 2;
  optional int64 offset = 3;
  optional int64 len = 4;
  optional int32 mode = 5;
}

// End of protos/perfetto/trace/ftrace/ext4.proto

// Begin of protos/perfetto/trace/ftrace/f2fs.proto

message F2fsDoSubmitBioFtraceEvent {
  optional uint64 dev = 1;
  optional int32 btype = 2;
  optional uint32 sync = 3;
  optional uint64 sector = 4;
  optional uint32 size = 5;
}
message F2fsEvictInodeFtraceEvent {
  optional uint64 dev = 1;
  optional uint64 ino = 2;
  optional uint64 pino = 3;
  optional uint32 mode = 4;
  optional int64 size = 5;
  optional uint32 nlink = 6;
  optional uint64 blocks = 7;
  optional uint32 advise = 8;
}
message F2fsFallocateFtraceEvent {
  optional uint64 dev = 1;
  optional uint64 ino = 2;
  optional int32 mode = 3;
  optional int64 offset = 4;
  optional int64 len = 5;
  optional int64 size = 6;
  optional uint64 blocks = 7;
  optional int32 ret = 8;
}
message F2fsGetDataBlockFtraceEvent {
  optional uint64 dev = 1;
  optional uint64 ino = 2;
  optional uint64 iblock = 3;
  optional uint64 bh_start = 4;
  optional uint64 bh_size = 5;
  optional int32 ret = 6;
}
message F2fsGetVictimFtraceEvent {
  optional uint64 dev = 1;
  optional int32 type = 2;
  optional int32 gc_type = 3;
  optional int32 alloc_mode = 4;
  optional int32 gc_mode = 5;
  optional uint32 victim = 6;
  optional uint32 ofs_unit = 7;
  optional uint32 pre_victim = 8;
  optional uint32 prefree = 9;
  optional uint32 free = 10;
}
message F2fsIgetFtraceEvent {
  optional uint64 dev = 1;
  optional uint64 ino = 2;
  optional uint64 pino = 3;
  optional uint32 mode = 4;
  optional int64 size = 5;
  optional uint32 nlink = 6;
  optional uint64 blocks = 7;
  optional uint32 advise = 8;
}
message F2fsIgetExitFtraceEvent {
  optional uint64 dev = 1;
  optional uint64 ino = 2;
  optional int32 ret = 3;
}
message F2fsNewInodeFtraceEvent {
  optional uint64 dev = 1;
  optional uint64 ino = 2;
  optional int32 ret = 3;
}
message F2fsReadpageFtraceEvent {
  optional uint64 dev = 1;
  optional uint64 ino = 2;
  optional uint64 index = 3;
  optional uint64 blkaddr = 4;
  optional int32 type = 5;
}
message F2fsReserveNewBlockFtraceEvent {
  optional uint64 dev = 1;
  optional uint32 nid = 2;
  optional uint32 ofs_in_node = 3;
}
message F2fsSetPageDirtyFtraceEvent {
  optional uint64 dev = 1;
  optional uint64 ino = 2;
  optional int32 type = 3;
  optional int32 dir = 4;
  optional uint64 index = 5;
  optional int32 dirty = 6;
}
message F2fsSubmitWritePageFtraceEvent {
  optional uint64 dev = 1;
  optional uint64 ino = 2;
  optional int32 type = 3;
  optional uint64 index = 4;
  optional uint32 block = 5;
}
message F2fsSyncFileEnterFtraceEvent {
  optional uint64 dev = 1;
  optional uint64 ino = 2;
  optional uint64 pino = 3;
  optional uint32 mode = 4;
  optional int64 size = 5;
  optional uint32 nlink = 6;
  optional uint64 blocks = 7;
  optional uint32 advise = 8;
}
message F2fsSyncFileExitFtraceEvent {
  optional uint64 dev = 1;
  optional uint64 ino = 2;
  optional uint32 need_cp = 3;
  optional int32 datasync = 4;
  optional int32 ret = 5;
}
message F2fsSyncFsFtraceEvent {
  optional uint64 dev = 1;
  optional int32 dirty = 2;
  optional int32 wait = 3;
}
message F2fsTruncateFtraceEvent {
  optional uint64 dev = 1;
  optional uint64 ino = 2;
  optional uint64 pino = 3;
  optional uint32 mode = 4;
  optional int64 size = 5;
  optional uint32 nlink = 6;
  optional uint64 blocks = 7;
  optional uint32 advise = 8;
}
message F2fsTruncateBlocksEnterFtraceEvent {
  optional uint64 dev = 1;
  optional uint64 ino = 2;
  optional int64 size = 3;
  optional uint64 blocks = 4;
  optional uint64 from = 5;
}
message F2fsTruncateBlocksExitFtraceEvent {
  optional uint64 dev = 1;
  optional uint64 ino = 2;
  optional int32 ret = 3;
}
message F2fsTruncateDataBlocksRangeFtraceEvent {
  optional uint64 dev = 1;
  optional uint64 ino = 2;
  optional uint32 nid = 3;
  optional uint32 ofs = 4;
  optional int32 free = 5;
}
message F2fsTruncateInodeBlocksEnterFtraceEvent {
  optional uint64 dev = 1;
  optional uint64 ino = 2;
  optional int64 size = 3;
  optional uint64 blocks = 4;
  optional uint64 from = 5;
}
message F2fsTruncateInodeBlocksExitFtraceEvent {
  optional uint64 dev = 1;
  optional uint64 ino = 2;
  optional int32 ret = 3;
}
message F2fsTruncateNodeFtraceEvent {
  optional uint64 dev = 1;
  optional uint64 ino = 2;
  optional uint32 nid = 3;
  optional uint32 blk_addr = 4;
}
message F2fsTruncateNodesEnterFtraceEvent {
  optional uint64 dev = 1;
  optional uint64 ino = 2;
  optional uint32 nid = 3;
  optional uint32 blk_addr = 4;
}
message F2fsTruncateNodesExitFtraceEvent {
  optional uint64 dev = 1;
  optional uint64 ino = 2;
  optional int32 ret = 3;
}
message F2fsTruncatePartialNodesFtraceEvent {
  optional uint64 dev = 1;
  optional uint64 ino = 2;
  optional uint32 nid = 3;
  optional int32 depth = 4;
  optional int32 err = 5;
}
message F2fsUnlinkEnterFtraceEvent {
  optional uint64 dev = 1;
  optional uint64 ino = 2;
  optional int64 size = 3;
  optional uint64 blocks = 4;
  optional string name = 5;
}
message F2fsUnlinkExitFtraceEvent {
  optional uint64 dev = 1;
  optional uint64 ino = 2;
  optional int32 ret = 3;
}
message F2fsVmPageMkwriteFtraceEvent {
  optional uint64 dev = 1;
  optional uint64 ino = 2;
  optional int32 type = 3;
  optional int32 dir = 4;
  optional uint64 index = 5;
  optional int32 dirty = 6;
}
message F2fsWriteBeginFtraceEvent {
  optional uint64 dev = 1;
  optional uint64 ino = 2;
  optional int64 pos = 3;
  optional uint32 len = 4;
  optional uint32 flags = 5;
}
message F2fsWriteCheckpointFtraceEvent {
  optional uint64 dev = 1;
  optional uint32 is_umount = 2;
  optional string msg = 3;
}
message F2fsWriteEndFtraceEvent {
  optional uint64 dev = 1;
  optional uint64 ino = 2;
  optional int64 pos = 3;
  optional uint32 len = 4;
  optional uint32 copied = 5;
}

// End of protos/perfetto/trace/ftrace/f2fs.proto

// Begin of protos/perfetto/trace/ftrace/fence.proto

message FenceInitFtraceEvent {
  optional uint32 context = 1;
  optional string driver = 2;
  optional uint32 seqno = 3;
  optional string timeline = 4;
}
message FenceDestroyFtraceEvent {
  optional uint32 context = 1;
  optional string driver = 2;
  optional uint32 seqno = 3;
  optional string timeline = 4;
}
message FenceEnableSignalFtraceEvent {
  optional uint32 context = 1;
  optional string driver = 2;
  optional uint32 seqno = 3;
  optional string timeline = 4;
}
message FenceSignaledFtraceEvent {
  optional uint32 context = 1;
  optional string driver = 2;
  optional uint32 seqno = 3;
  optional string timeline = 4;
}

// End of protos/perfetto/trace/ftrace/fence.proto

// Begin of protos/perfetto/trace/ftrace/filemap.proto

message MmFilemapAddToPageCacheFtraceEvent {
  optional uint64 pfn = 1;
  optional uint64 i_ino = 2;
  optional uint64 index = 3;
  optional uint64 s_dev = 4;
  optional uint64 page = 5;
}
message MmFilemapDeleteFromPageCacheFtraceEvent {
  optional uint64 pfn = 1;
  optional uint64 i_ino = 2;
  optional uint64 index = 3;
  optional uint64 s_dev = 4;
  optional uint64 page = 5;
}

// End of protos/perfetto/trace/ftrace/filemap.proto

// Begin of protos/perfetto/trace/ftrace/ftrace.proto

message PrintFtraceEvent {
  optional uint64 ip = 1;
  optional string buf = 2;
}

// End of protos/perfetto/trace/ftrace/ftrace.proto

// Begin of protos/perfetto/trace/ftrace/generic.proto

// This generic proto is used to output events in the trace
// when a specific proto for that event does not exist.
message GenericFtraceEvent {
  message Field {
    optional string name = 1;
    oneof value {
      string str_value = 3;
      int64 int_value = 4;
      uint64 uint_value = 5;
    }
  }

  optional string event_name = 1;
  repeated Field field = 2;
}

// End of protos/perfetto/trace/ftrace/generic.proto

// Begin of protos/perfetto/trace/ftrace/gpu_mem.proto

message GpuMemTotalFtraceEvent {
  optional uint32 gpu_id = 1;
  optional uint32 pid = 2;
  optional uint64 size = 3;
}

// End of protos/perfetto/trace/ftrace/gpu_mem.proto

// Begin of protos/perfetto/trace/ftrace/i2c.proto

message I2cReadFtraceEvent {
  optional int32 adapter_nr = 1;
  optional uint32 msg_nr = 2;
  optional uint32 addr = 3;
  optional uint32 flags = 4;
  optional uint32 len = 5;
}
message I2cWriteFtraceEvent {
  optional int32 adapter_nr = 1;
  optional uint32 msg_nr = 2;
  optional uint32 addr = 3;
  optional uint32 flags = 4;
  optional uint32 len = 5;
  optional uint32 buf = 6;
}
message I2cResultFtraceEvent {
  optional int32 adapter_nr = 1;
  optional uint32 nr_msgs = 2;
  optional int32 ret = 3;
}
message I2cReplyFtraceEvent {
  optional int32 adapter_nr = 1;
  optional uint32 msg_nr = 2;
  optional uint32 addr = 3;
  optional uint32 flags = 4;
  optional uint32 len = 5;
  optional uint32 buf = 6;
}
message SmbusReadFtraceEvent {
  optional int32 adapter_nr = 1;
  optional uint32 flags = 2;
  optional uint32 addr = 3;
  optional uint32 command = 4;
  optional uint32 protocol = 5;
}
message SmbusWriteFtraceEvent {
  optional int32 adapter_nr = 1;
  optional uint32 addr = 2;
  optional uint32 flags = 3;
  optional uint32 command = 4;
  optional uint32 len = 5;
  optional uint32 protocol = 6;
}
message SmbusResultFtraceEvent {
  optional int32 adapter_nr = 1;
  optional uint32 addr = 2;
  optional uint32 flags = 3;
  optional uint32 read_write = 4;
  optional uint32 command = 5;
  optional int32 res = 6;
  optional uint32 protocol = 7;
}
message SmbusReplyFtraceEvent {
  optional int32 adapter_nr = 1;
  optional uint32 addr = 2;
  optional uint32 flags = 3;
  optional uint32 command = 4;
  optional uint32 len = 5;
  optional uint32 protocol = 6;
}

// End of protos/perfetto/trace/ftrace/i2c.proto

// Begin of protos/perfetto/trace/ftrace/ion.proto

message IonStatFtraceEvent {
  optional uint32 buffer_id = 1;
  optional int64 len = 2;
  optional uint64 total_allocated = 3;
}

// End of protos/perfetto/trace/ftrace/ion.proto

// Begin of protos/perfetto/trace/ftrace/ipi.proto

message IpiEntryFtraceEvent {
  optional string reason = 1;
}
message IpiExitFtraceEvent {
  optional string reason = 1;
}
message IpiRaiseFtraceEvent {
  optional uint32 target_cpus = 1;
  optional string reason = 2;
}

// End of protos/perfetto/trace/ftrace/ipi.proto

// Begin of protos/perfetto/trace/ftrace/irq.proto

message SoftirqEntryFtraceEvent {
  optional uint32 vec = 1;
}
message SoftirqExitFtraceEvent {
  optional uint32 vec = 1;
}
message SoftirqRaiseFtraceEvent {
  optional uint32 vec = 1;
}
message IrqHandlerEntryFtraceEvent {
  optional int32 irq = 1;
  optional string name = 2;
  optional uint32 handler = 3;
}
message IrqHandlerExitFtraceEvent {
  optional int32 irq = 1;
  optional int32 ret = 2;
}

// End of protos/perfetto/trace/ftrace/irq.proto

// Begin of protos/perfetto/trace/ftrace/kmem.proto

message AllocPagesIommuEndFtraceEvent {
  optional uint32 gfp_flags = 1;
  optional uint32 order = 2;
}
message AllocPagesIommuFailFtraceEvent {
  optional uint32 gfp_flags = 1;
  optional uint32 order = 2;
}
message AllocPagesIommuStartFtraceEvent {
  optional uint32 gfp_flags = 1;
  optional uint32 order = 2;
}
message AllocPagesSysEndFtraceEvent {
  optional uint32 gfp_flags = 1;
  optional uint32 order = 2;
}
message AllocPagesSysFailFtraceEvent {
  optional uint32 gfp_flags = 1;
  optional uint32 order = 2;
}
message AllocPagesSysStartFtraceEvent {
  optional uint32 gfp_flags = 1;
  optional uint32 order = 2;
}
message DmaAllocContiguousRetryFtraceEvent {
  optional int32 tries = 1;
}
message IommuMapRangeFtraceEvent {
  optional uint64 chunk_size = 1;
  optional uint64 len = 2;
  optional uint64 pa = 3;
  optional uint64 va = 4;
}
message IommuSecPtblMapRangeEndFtraceEvent {
  optional uint64 len = 1;
  optional int32 num = 2;
  optional uint32 pa = 3;
  optional int32 sec_id = 4;
  optional uint64 va = 5;
}
message IommuSecPtblMapRangeStartFtraceEvent {
  optional uint64 len = 1;
  optional int32 num = 2;
  optional uint32 pa = 3;
  optional int32 sec_id = 4;
  optional uint64 va = 5;
}
message IonAllocBufferEndFtraceEvent {
  optional string client_name = 1;
  optional uint32 flags = 2;
  optional string heap_name = 3;
  optional uint64 len = 4;
  optional uint32 mask = 5;
}
message IonAllocBufferFailFtraceEvent {
  optional string client_name = 1;
  optional int64 error = 2;
  optional uint32 flags = 3;
  optional string heap_name = 4;
  optional uint64 len = 5;
  optional uint32 mask = 6;
}
message IonAllocBufferFallbackFtraceEvent {
  optional string client_name = 1;
  optional int64 error = 2;
  optional uint32 flags = 3;
  optional string heap_name = 4;
  optional uint64 len = 5;
  optional uint32 mask = 6;
}
message IonAllocBufferStartFtraceEvent {
  optional string client_name = 1;
  optional uint32 flags = 2;
  optional string heap_name = 3;
  optional uint64 len = 4;
  optional uint32 mask = 5;
}
message IonCpAllocRetryFtraceEvent {
  optional int32 tries = 1;
}
message IonCpSecureBufferEndFtraceEvent {
  optional uint64 align = 1;
  optional uint64 flags = 2;
  optional string heap_name = 3;
  optional uint64 len = 4;
}
message IonCpSecureBufferStartFtraceEvent {
  optional uint64 align = 1;
  optional uint64 flags = 2;
  optional string heap_name = 3;
  optional uint64 len = 4;
}
message IonPrefetchingFtraceEvent {
  optional uint64 len = 1;
}
message IonSecureCmaAddToPoolEndFtraceEvent {
  optional uint32 is_prefetch = 1;
  optional uint64 len = 2;
  optional int32 pool_total = 3;
}
message IonSecureCmaAddToPoolStartFtraceEvent {
  optional uint32 is_prefetch = 1;
  optional uint64 len = 2;
  optional int32 pool_total = 3;
}
message IonSecureCmaAllocateEndFtraceEvent {
  optional uint64 align = 1;
  optional uint64 flags = 2;
  optional string heap_name = 3;
  optional uint64 len = 4;
}
message IonSecureCmaAllocateStartFtraceEvent {
  optional uint64 align = 1;
  optional uint64 flags = 2;
  optional string heap_name = 3;
  optional uint64 len = 4;
}
message IonSecureCmaShrinkPoolEndFtraceEvent {
  optional uint64 drained_size = 1;
  optional uint64 skipped_size = 2;
}
message IonSecureCmaShrinkPoolStartFtraceEvent {
  optional uint64 drained_size = 1;
  optional uint64 skipped_size = 2;
}
message KfreeFtraceEvent {
  optional uint64 call_site = 1;
  optional uint64 ptr = 2;
}
message KmallocFtraceEvent {
  optional uint64 bytes_alloc = 1;
  optional uint64 bytes_req = 2;
  optional uint64 call_site = 3;
  optional uint32 gfp_flags = 4;
  optional uint64 ptr = 5;
}
message KmallocNodeFtraceEvent {
  optional uint64 bytes_alloc = 1;
  optional uint64 bytes_req = 2;
  optional uint64 call_site = 3;
  optional uint32 gfp_flags = 4;
  optional int32 node = 5;
  optional uint64 ptr = 6;
}
message KmemCacheAllocFtraceEvent {
  optional uint64 bytes_alloc = 1;
  optional uint64 bytes_req = 2;
  optional uint64 call_site = 3;
  optional uint32 gfp_flags = 4;
  optional uint64 ptr = 5;
}
message KmemCacheAllocNodeFtraceEvent {
  optional uint64 bytes_alloc = 1;
  optional uint64 bytes_req = 2;
  optional uint64 call_site = 3;
  optional uint32 gfp_flags = 4;
  optional int32 node = 5;
  optional uint64 ptr = 6;
}
message KmemCacheFreeFtraceEvent {
  optional uint64 call_site = 1;
  optional uint64 ptr = 2;
}
message MigratePagesEndFtraceEvent {
  optional int32 mode = 1;
}
message MigratePagesStartFtraceEvent {
  optional int32 mode = 1;
}
message MigrateRetryFtraceEvent {
  optional int32 tries = 1;
}
message MmPageAllocFtraceEvent {
  optional uint32 gfp_flags = 1;
  optional int32 migratetype = 2;
  optional uint32 order = 3;
  optional uint64 page = 4;
  optional uint64 pfn = 5;
}
message MmPageAllocExtfragFtraceEvent {
  optional int32 alloc_migratetype = 1;
  optional int32 alloc_order = 2;
  optional int32 fallback_migratetype = 3;
  optional int32 fallback_order = 4;
  optional uint64 page = 5;
  optional int32 change_ownership = 6;
  optional uint64 pfn = 7;
}
message MmPageAllocZoneLockedFtraceEvent {
  optional int32 migratetype = 1;
  optional uint32 order = 2;
  optional uint64 page = 3;
  optional uint64 pfn = 4;
}
message MmPageFreeFtraceEvent {
  optional uint32 order = 1;
  optional uint64 page = 2;
  optional uint64 pfn = 3;
}
message MmPageFreeBatchedFtraceEvent {
  optional int32 cold = 1;
  optional uint64 page = 2;
  optional uint64 pfn = 3;
}
message MmPagePcpuDrainFtraceEvent {
  optional int32 migratetype = 1;
  optional uint32 order = 2;
  optional uint64 page = 3;
  optional uint64 pfn = 4;
}
message RssStatFtraceEvent {
  optional int32 member = 1;
  optional int64 size = 2;
  optional uint32 curr = 3;
  optional uint32 mm_id = 4;
}
message IonHeapShrinkFtraceEvent {
  optional string heap_name = 1;
  optional uint64 len = 2;
  optional int64 total_allocated = 3;
}
message IonHeapGrowFtraceEvent {
  optional string heap_name = 1;
  optional uint64 len = 2;
  optional int64 total_allocated = 3;
}
message IonBufferCreateFtraceEvent {
  optional uint64 addr = 1;
  optional uint64 len = 2;
}
message IonBufferDestroyFtraceEvent {
  optional uint64 addr = 1;
  optional uint64 len = 2;
}

// End of protos/perfetto/trace/ftrace/kmem.proto

// Begin of protos/perfetto/trace/ftrace/lowmemorykiller.proto

message LowmemoryKillFtraceEvent {
  optional string comm = 1;
  optional int32 pid = 2;
  optional int64 pagecache_size = 3;
  optional int64 pagecache_limit = 4;
  optional int64 free = 5;
}

// End of protos/perfetto/trace/ftrace/lowmemorykiller.proto

// Begin of protos/perfetto/trace/ftrace/mdss.proto

message MdpCmdKickoffFtraceEvent {
  optional uint32 ctl_num = 1;
  optional int32 kickoff_cnt = 2;
}
message MdpCommitFtraceEvent {
  optional uint32 num = 1;
  optional uint32 play_cnt = 2;
  optional uint32 clk_rate = 3;
  optional uint64 bandwidth = 4;
}
message MdpPerfSetOtFtraceEvent {
  optional uint32 pnum = 1;
  optional uint32 xin_id = 2;
  optional uint32 rd_lim = 3;
  optional uint32 is_vbif_rt = 4;
}
message MdpSsppChangeFtraceEvent {
  optional uint32 num = 1;
  optional uint32 play_cnt = 2;
  optional uint32 mixer = 3;
  optional uint32 stage = 4;
  optional uint32 flags = 5;
  optional uint32 format = 6;
  optional uint32 img_w = 7;
  optional uint32 img_h = 8;
  optional uint32 src_x = 9;
  optional uint32 src_y = 10;
  optional uint32 src_w = 11;
  optional uint32 src_h = 12;
  optional uint32 dst_x = 13;
  optional uint32 dst_y = 14;
  optional uint32 dst_w = 15;
  optional uint32 dst_h = 16;
}
message TracingMarkWriteFtraceEvent {
  optional int32 pid = 1;
  optional string trace_name = 2;
  optional uint32 trace_begin = 3;
}
message MdpCmdPingpongDoneFtraceEvent {
  optional uint32 ctl_num = 1;
  optional uint32 intf_num = 2;
  optional uint32 pp_num = 3;
  optional int32 koff_cnt = 4;
}
message MdpCompareBwFtraceEvent {
  optional uint64 new_ab = 1;
  optional uint64 new_ib = 2;
  optional uint64 new_wb = 3;
  optional uint64 old_ab = 4;
  optional uint64 old_ib = 5;
  optional uint64 old_wb = 6;
  optional uint32 params_changed = 7;
  optional uint32 update_bw = 8;
}
message MdpPerfSetPanicLutsFtraceEvent {
  optional uint32 pnum = 1;
  optional uint32 fmt = 2;
  optional uint32 mode = 3;
  optional uint32 panic_lut = 4;
  optional uint32 robust_lut = 5;
}
message MdpSsppSetFtraceEvent {
  optional uint32 num = 1;
  optional uint32 play_cnt = 2;
  optional uint32 mixer = 3;
  optional uint32 stage = 4;
  optional uint32 flags = 5;
  optional uint32 format = 6;
  optional uint32 img_w = 7;
  optional uint32 img_h = 8;
  optional uint32 src_x = 9;
  optional uint32 src_y = 10;
  optional uint32 src_w = 11;
  optional uint32 src_h = 12;
  optional uint32 dst_x = 13;
  optional uint32 dst_y = 14;
  optional uint32 dst_w = 15;
  optional uint32 dst_h = 16;
}
message MdpCmdReadptrDoneFtraceEvent {
  optional uint32 ctl_num = 1;
  optional int32 koff_cnt = 2;
}
message MdpMisrCrcFtraceEvent {
  optional uint32 block_id = 1;
  optional uint32 vsync_cnt = 2;
  optional uint32 crc = 3;
}
message MdpPerfSetQosLutsFtraceEvent {
  optional uint32 pnum = 1;
  optional uint32 fmt = 2;
  optional uint32 intf = 3;
  optional uint32 rot = 4;
  optional uint32 fl = 5;
  optional uint32 lut = 6;
  optional uint32 linear = 7;
}
message MdpTraceCounterFtraceEvent {
  optional int32 pid = 1;
  optional string counter_name = 2;
  optional int32 value = 3;
}
message MdpCmdReleaseBwFtraceEvent {
  optional uint32 ctl_num = 1;
}
message MdpMixerUpdateFtraceEvent {
  optional uint32 mixer_num = 1;
}
message MdpPerfSetWmLevelsFtraceEvent {
  optional uint32 pnum = 1;
  optional uint32 use_space = 2;
  optional uint32 priority_bytes = 3;
  optional uint32 wm0 = 4;
  optional uint32 wm1 = 5;
  optional uint32 wm2 = 6;
  optional uint32 mb_cnt = 7;
  optional uint32 mb_size = 8;
}
message MdpVideoUnderrunDoneFtraceEvent {
  optional uint32 ctl_num = 1;
  optional uint32 underrun_cnt = 2;
}
message MdpCmdWaitPingpongFtraceEvent {
  optional uint32 ctl_num = 1;
  optional int32 kickoff_cnt = 2;
}
message MdpPerfPrefillCalcFtraceEvent {
  optional uint32 pnum = 1;
  optional uint32 latency_buf = 2;
  optional uint32 ot = 3;
  optional uint32 y_buf = 4;
  optional uint32 y_scaler = 5;
  optional uint32 pp_lines = 6;
  optional uint32 pp_bytes = 7;
  optional uint32 post_sc = 8;
  optional uint32 fbc_bytes = 9;
  optional uint32 prefill_bytes = 10;
}
message MdpPerfUpdateBusFtraceEvent {
  optional int32 client = 1;
  optional uint64 ab_quota = 2;
  optional uint64 ib_quota = 3;
}
message RotatorBwAoAsContextFtraceEvent {
  optional uint32 state = 1;
}

// End of protos/perfetto/trace/ftrace/mdss.proto

// Begin of protos/perfetto/trace/ftrace/mm_event.proto

message MmEventRecordFtraceEvent {
  optional uint32 avg_lat = 1;
  optional uint32 count = 2;
  optional uint32 max_lat = 3;
  optional uint32 type = 4;
}

// End of protos/perfetto/trace/ftrace/mm_event.proto

// Begin of protos/perfetto/trace/ftrace/oom.proto

message OomScoreAdjUpdateFtraceEvent {
  optional string comm = 1;
  optional int32 oom_score_adj = 2;
  optional int32 pid = 3;
}
message MarkVictimFtraceEvent {
  optional int32 pid = 1;
}

// End of protos/perfetto/trace/ftrace/oom.proto

// Begin of protos/perfetto/trace/ftrace/power.proto

message CpuFrequencyFtraceEvent {
  optional uint32 state = 1;
  optional uint32 cpu_id = 2;
}
message CpuFrequencyLimitsFtraceEvent {
  optional uint32 min_freq = 1;
  optional uint32 max_freq = 2;
  optional uint32 cpu_id = 3;
}
message CpuIdleFtraceEvent {
  optional uint32 state = 1;
  optional uint32 cpu_id = 2;
}
message ClockEnableFtraceEvent {
  optional string name = 1;
  optional uint64 state = 2;
  optional uint64 cpu_id = 3;
}
message ClockDisableFtraceEvent {
  optional string name = 1;
  optional uint64 state = 2;
  optional uint64 cpu_id = 3;
}
message ClockSetRateFtraceEvent {
  optional string name = 1;
  optional uint64 state = 2;
  optional uint64 cpu_id = 3;
}
message SuspendResumeFtraceEvent {
  optional string action = 1;
  optional int32 val = 2;
  optional uint32 start = 3;
}
message GpuFrequencyFtraceEvent {
  optional uint32 gpu_id = 1;
  optional uint32 state = 2;
}

// End of protos/perfetto/trace/ftrace/power.proto

// Begin of protos/perfetto/trace/ftrace/raw_syscalls.proto

message SysEnterFtraceEvent {
  optional int64 id = 1;
}
message SysExitFtraceEvent {
  optional int64 id = 1;
  optional int64 ret = 2;
}

// End of protos/perfetto/trace/ftrace/raw_syscalls.proto

// Begin of protos/perfetto/trace/ftrace/regulator.proto

message RegulatorDisableFtraceEvent {
  optional string name = 1;
}
message RegulatorDisableCompleteFtraceEvent {
  optional string name = 1;
}
message RegulatorEnableFtraceEvent {
  optional string name = 1;
}
message RegulatorEnableCompleteFtraceEvent {
  optional string name = 1;
}
message RegulatorEnableDelayFtraceEvent {
  optional string name = 1;
}
message RegulatorSetVoltageFtraceEvent {
  optional string name = 1;
  optional int32 min = 2;
  optional int32 max = 3;
}
message RegulatorSetVoltageCompleteFtraceEvent {
  optional string name = 1;
  optional uint32 val = 2;
}

// End of protos/perfetto/trace/ftrace/regulator.proto

// Begin of protos/perfetto/trace/ftrace/sched.proto

message SchedSwitchFtraceEvent {
  optional string prev_comm = 1;
  optional int32 prev_pid = 2;
  optional int32 prev_prio = 3;
  optional int64 prev_state = 4;
  optional string next_comm = 5;
  optional int32 next_pid = 6;
  optional int32 next_prio = 7;
}
message SchedWakeupFtraceEvent {
  optional string comm = 1;
  optional int32 pid = 2;
  optional int32 prio = 3;
  optional int32 success = 4;
  optional int32 target_cpu = 5;
}
message SchedBlockedReasonFtraceEvent {
  optional int32 pid = 1;
  optional uint64 caller = 2;
  optional uint32 io_wait = 3;
}
message SchedCpuHotplugFtraceEvent {
  optional int32 affected_cpu = 1;
  optional int32 error = 2;
  optional int32 status = 3;
}
message SchedWakingFtraceEvent {
  optional string comm = 1;
  optional int32 pid = 2;
  optional int32 prio = 3;
  optional int32 success = 4;
  optional int32 target_cpu = 5;
}
message SchedWakeupNewFtraceEvent {
  optional string comm = 1;
  optional int32 pid = 2;
  optional int32 prio = 3;
  optional int32 success = 4;
  optional int32 target_cpu = 5;
}
message SchedProcessExecFtraceEvent {
  optional string filename = 1;
  optional int32 pid = 2;
  optional int32 old_pid = 3;
}
message SchedProcessExitFtraceEvent {
  optional string comm = 1;
  optional int32 pid = 2;
  optional int32 tgid = 3;
  optional int32 prio = 4;
}
message SchedProcessForkFtraceEvent {
  optional string parent_comm = 1;
  optional int32 parent_pid = 2;
  optional string child_comm = 3;
  optional int32 child_pid = 4;
}
message SchedProcessFreeFtraceEvent {
  optional string comm = 1;
  optional int32 pid = 2;
  optional int32 prio = 3;
}
message SchedProcessHangFtraceEvent {
  optional string comm = 1;
  optional int32 pid = 2;
}
message SchedProcessWaitFtraceEvent {
  optional string comm = 1;
  optional int32 pid = 2;
  optional int32 prio = 3;
}

// End of protos/perfetto/trace/ftrace/sched.proto

// Begin of protos/perfetto/trace/ftrace/scm.proto

message ScmCallStartFtraceEvent {
  optional uint32 arginfo = 1;
  optional uint64 x0 = 2;
  optional uint64 x5 = 3;
}
message ScmCallEndFtraceEvent {}

// End of protos/perfetto/trace/ftrace/scm.proto

// Begin of protos/perfetto/trace/ftrace/sde.proto

message SdeTracingMarkWriteFtraceEvent {
  optional int32 pid = 1;
  optional string trace_name = 2;
  optional uint32 trace_type = 3;
  optional int32 value = 4;
  optional uint32 trace_begin = 5;
}

// End of protos/perfetto/trace/ftrace/sde.proto

// Begin of protos/perfetto/trace/ftrace/signal.proto

message SignalDeliverFtraceEvent {
  optional int32 code = 1;
  optional uint64 sa_flags = 2;
  optional int32 sig = 3;
}
message SignalGenerateFtraceEvent {
  optional int32 code = 1;
  optional string comm = 2;
  optional int32 group = 3;
  optional int32 pid = 4;
  optional int32 result = 5;
  optional int32 sig = 6;
}

// End of protos/perfetto/trace/ftrace/signal.proto

// Begin of protos/perfetto/trace/ftrace/sync.proto

message SyncPtFtraceEvent {
  optional string timeline = 1;
  optional string value = 2;
}
message SyncTimelineFtraceEvent {
  optional string name = 1;
  optional string value = 2;
}
message SyncWaitFtraceEvent {
  optional string name = 1;
  optional int32 status = 2;
  optional uint32 begin = 3;
}

// End of protos/perfetto/trace/ftrace/sync.proto

// Begin of protos/perfetto/trace/ftrace/systrace.proto

message ZeroFtraceEvent {
  optional int32 flag = 1;
  optional string name = 2;
  optional int32 pid = 3;
  optional int64 value = 4;
}

// End of protos/perfetto/trace/ftrace/systrace.proto

// Begin of protos/perfetto/trace/ftrace/task.proto

message TaskNewtaskFtraceEvent {
  optional int32 pid = 1;
  optional string comm = 2;
  optional uint64 clone_flags = 3;
  optional int32 oom_score_adj = 4;
}
message TaskRenameFtraceEvent {
  optional int32 pid = 1;
  optional string oldcomm = 2;
  optional string newcomm = 3;
  optional int32 oom_score_adj = 4;
}

// End of protos/perfetto/trace/ftrace/task.proto

// Begin of protos/perfetto/trace/ftrace/thermal.proto

message ThermalTemperatureFtraceEvent {
  optional int32 id = 1;
  optional int32 temp = 2;
  optional int32 temp_prev = 3;
  optional string thermal_zone = 4;
}
message CdevUpdateFtraceEvent {
  optional uint64 target = 1;
  optional string type = 2;
}

// End of protos/perfetto/trace/ftrace/thermal.proto

// Begin of protos/perfetto/trace/ftrace/vmscan.proto

message MmVmscanDirectReclaimBeginFtraceEvent {
  optional int32 order = 1;
  optional int32 may_writepage = 2;
  optional uint32 gfp_flags = 3;
}
message MmVmscanDirectReclaimEndFtraceEvent {
  optional uint64 nr_reclaimed = 1;
}
message MmVmscanKswapdWakeFtraceEvent {
  optional int32 nid = 1;
  optional int32 order = 2;
}
message MmVmscanKswapdSleepFtraceEvent {
  optional int32 nid = 1;
}

// End of protos/perfetto/trace/ftrace/vmscan.proto

// Begin of protos/perfetto/trace/ftrace/workqueue.proto

message WorkqueueActivateWorkFtraceEvent {
  optional uint64 work = 1;
}
message WorkqueueExecuteEndFtraceEvent {
  optional uint64 work = 1;
}
message WorkqueueExecuteStartFtraceEvent {
  optional uint64 work = 1;
  optional uint64 function = 2;
}
message WorkqueueQueueWorkFtraceEvent {
  optional uint64 work = 1;
  optional uint64 function = 2;
  optional uint64 workqueue = 3;
  optional uint32 req_cpu = 4;
  optional uint32 cpu = 5;
}

// End of protos/perfetto/trace/ftrace/workqueue.proto

// Begin of protos/perfetto/trace/ftrace/ftrace_event.proto

message FtraceEvent {
  // Nanoseconds since an epoch.
  // Epoch is configurable by writing into trace_clock.
  // By default this timestamp is CPU local.
  // TODO: Figure out a story for reconciling the various clocks.
  optional uint64 timestamp = 1;

  // Kernel pid (do not confuse with userspace pid aka tgid)
  optional uint32 pid = 2;

  oneof event {
    PrintFtraceEvent print = 3;
    SchedSwitchFtraceEvent sched_switch = 4;
    // removed field with id 5;
    // removed field with id 6;
    // removed field with id 7;
    // removed field with id 8;
    // removed field with id 9;
    // removed field with id 10;
    CpuFrequencyFtraceEvent cpu_frequency = 11;
    CpuFrequencyLimitsFtraceEvent cpu_frequency_limits = 12;
    CpuIdleFtraceEvent cpu_idle = 13;
    ClockEnableFtraceEvent clock_enable = 14;
    ClockDisableFtraceEvent clock_disable = 15;
    ClockSetRateFtraceEvent clock_set_rate = 16;
    SchedWakeupFtraceEvent sched_wakeup = 17;
    SchedBlockedReasonFtraceEvent sched_blocked_reason = 18;
    SchedCpuHotplugFtraceEvent sched_cpu_hotplug = 19;
    SchedWakingFtraceEvent sched_waking = 20;
    IpiEntryFtraceEvent ipi_entry = 21;
    IpiExitFtraceEvent ipi_exit = 22;
    IpiRaiseFtraceEvent ipi_raise = 23;
    SoftirqEntryFtraceEvent softirq_entry = 24;
    SoftirqExitFtraceEvent softirq_exit = 25;
    SoftirqRaiseFtraceEvent softirq_raise = 26;
    I2cReadFtraceEvent i2c_read = 27;
    I2cWriteFtraceEvent i2c_write = 28;
    I2cResultFtraceEvent i2c_result = 29;
    I2cReplyFtraceEvent i2c_reply = 30;
    SmbusReadFtraceEvent smbus_read = 31;
    SmbusWriteFtraceEvent smbus_write = 32;
    SmbusResultFtraceEvent smbus_result = 33;
    SmbusReplyFtraceEvent smbus_reply = 34;
    LowmemoryKillFtraceEvent lowmemory_kill = 35;
    IrqHandlerEntryFtraceEvent irq_handler_entry = 36;
    IrqHandlerExitFtraceEvent irq_handler_exit = 37;
    SyncPtFtraceEvent sync_pt = 38;
    SyncTimelineFtraceEvent sync_timeline = 39;
    SyncWaitFtraceEvent sync_wait = 40;
    Ext4DaWriteBeginFtraceEvent ext4_da_write_begin = 41;
    Ext4DaWriteEndFtraceEvent ext4_da_write_end = 42;
    Ext4SyncFileEnterFtraceEvent ext4_sync_file_enter = 43;
    Ext4SyncFileExitFtraceEvent ext4_sync_file_exit = 44;
    BlockRqIssueFtraceEvent block_rq_issue = 45;
    MmVmscanDirectReclaimBeginFtraceEvent mm_vmscan_direct_reclaim_begin = 46;
    MmVmscanDirectReclaimEndFtraceEvent mm_vmscan_direct_reclaim_end = 47;
    MmVmscanKswapdWakeFtraceEvent mm_vmscan_kswapd_wake = 48;
    MmVmscanKswapdSleepFtraceEvent mm_vmscan_kswapd_sleep = 49;
    BinderTransactionFtraceEvent binder_transaction = 50;
    BinderTransactionReceivedFtraceEvent binder_transaction_received = 51;
    BinderSetPriorityFtraceEvent binder_set_priority = 52;
    BinderLockFtraceEvent binder_lock = 53;
    BinderLockedFtraceEvent binder_locked = 54;
    BinderUnlockFtraceEvent binder_unlock = 55;
    WorkqueueActivateWorkFtraceEvent workqueue_activate_work = 56;
    WorkqueueExecuteEndFtraceEvent workqueue_execute_end = 57;
    WorkqueueExecuteStartFtraceEvent workqueue_execute_start = 58;
    WorkqueueQueueWorkFtraceEvent workqueue_queue_work = 59;
    RegulatorDisableFtraceEvent regulator_disable = 60;
    RegulatorDisableCompleteFtraceEvent regulator_disable_complete = 61;
    RegulatorEnableFtraceEvent regulator_enable = 62;
    RegulatorEnableCompleteFtraceEvent regulator_enable_complete = 63;
    RegulatorEnableDelayFtraceEvent regulator_enable_delay = 64;
    RegulatorSetVoltageFtraceEvent regulator_set_voltage = 65;
    RegulatorSetVoltageCompleteFtraceEvent regulator_set_voltage_complete = 66;
    CgroupAttachTaskFtraceEvent cgroup_attach_task = 67;
    CgroupMkdirFtraceEvent cgroup_mkdir = 68;
    CgroupRemountFtraceEvent cgroup_remount = 69;
    CgroupRmdirFtraceEvent cgroup_rmdir = 70;
    CgroupTransferTasksFtraceEvent cgroup_transfer_tasks = 71;
    CgroupDestroyRootFtraceEvent cgroup_destroy_root = 72;
    CgroupReleaseFtraceEvent cgroup_release = 73;
    CgroupRenameFtraceEvent cgroup_rename = 74;
    CgroupSetupRootFtraceEvent cgroup_setup_root = 75;
    MdpCmdKickoffFtraceEvent mdp_cmd_kickoff = 76;
    MdpCommitFtraceEvent mdp_commit = 77;
    MdpPerfSetOtFtraceEvent mdp_perf_set_ot = 78;
    MdpSsppChangeFtraceEvent mdp_sspp_change = 79;
    TracingMarkWriteFtraceEvent tracing_mark_write = 80;
    MdpCmdPingpongDoneFtraceEvent mdp_cmd_pingpong_done = 81;
    MdpCompareBwFtraceEvent mdp_compare_bw = 82;
    MdpPerfSetPanicLutsFtraceEvent mdp_perf_set_panic_luts = 83;
    MdpSsppSetFtraceEvent mdp_sspp_set = 84;
    MdpCmdReadptrDoneFtraceEvent mdp_cmd_readptr_done = 85;
    MdpMisrCrcFtraceEvent mdp_misr_crc = 86;
    MdpPerfSetQosLutsFtraceEvent mdp_perf_set_qos_luts = 87;
    MdpTraceCounterFtraceEvent mdp_trace_counter = 88;
    MdpCmdReleaseBwFtraceEvent mdp_cmd_release_bw = 89;
    MdpMixerUpdateFtraceEvent mdp_mixer_update = 90;
    MdpPerfSetWmLevelsFtraceEvent mdp_perf_set_wm_levels = 91;
    MdpVideoUnderrunDoneFtraceEvent mdp_video_underrun_done = 92;
    MdpCmdWaitPingpongFtraceEvent mdp_cmd_wait_pingpong = 93;
    MdpPerfPrefillCalcFtraceEvent mdp_perf_prefill_calc = 94;
    MdpPerfUpdateBusFtraceEvent mdp_perf_update_bus = 95;
    RotatorBwAoAsContextFtraceEvent rotator_bw_ao_as_context = 96;
    MmFilemapAddToPageCacheFtraceEvent mm_filemap_add_to_page_cache = 97;
    MmFilemapDeleteFromPageCacheFtraceEvent mm_filemap_delete_from_page_cache =
        98;
    MmCompactionBeginFtraceEvent mm_compaction_begin = 99;
    MmCompactionDeferCompactionFtraceEvent mm_compaction_defer_compaction = 100;
    MmCompactionDeferredFtraceEvent mm_compaction_deferred = 101;
    MmCompactionDeferResetFtraceEvent mm_compaction_defer_reset = 102;
    MmCompactionEndFtraceEvent mm_compaction_end = 103;
    MmCompactionFinishedFtraceEvent mm_compaction_finished = 104;
    MmCompactionIsolateFreepagesFtraceEvent mm_compaction_isolate_freepages =
        105;
    MmCompactionIsolateMigratepagesFtraceEvent
        mm_compaction_isolate_migratepages = 106;
    MmCompactionKcompactdSleepFtraceEvent mm_compaction_kcompactd_sleep = 107;
    MmCompactionKcompactdWakeFtraceEvent mm_compaction_kcompactd_wake = 108;
    MmCompactionMigratepagesFtraceEvent mm_compaction_migratepages = 109;
    MmCompactionSuitableFtraceEvent mm_compaction_suitable = 110;
    MmCompactionTryToCompactPagesFtraceEvent
        mm_compaction_try_to_compact_pages = 111;
    MmCompactionWakeupKcompactdFtraceEvent mm_compaction_wakeup_kcompactd = 112;
    SuspendResumeFtraceEvent suspend_resume = 113;
    SchedWakeupNewFtraceEvent sched_wakeup_new = 114;
    BlockBioBackmergeFtraceEvent block_bio_backmerge = 115;
    BlockBioBounceFtraceEvent block_bio_bounce = 116;
    BlockBioCompleteFtraceEvent block_bio_complete = 117;
    BlockBioFrontmergeFtraceEvent block_bio_frontmerge = 118;
    BlockBioQueueFtraceEvent block_bio_queue = 119;
    BlockBioRemapFtraceEvent block_bio_remap = 120;
    BlockDirtyBufferFtraceEvent block_dirty_buffer = 121;
    BlockGetrqFtraceEvent block_getrq = 122;
    BlockPlugFtraceEvent block_plug = 123;
    BlockRqAbortFtraceEvent block_rq_abort = 124;
    BlockRqCompleteFtraceEvent block_rq_complete = 125;
    BlockRqInsertFtraceEvent block_rq_insert = 126;
    // removed field with id 127;
    BlockRqRemapFtraceEvent block_rq_remap = 128;
    BlockRqRequeueFtraceEvent block_rq_requeue = 129;
    BlockSleeprqFtraceEvent block_sleeprq = 130;
    BlockSplitFtraceEvent block_split = 131;
    BlockTouchBufferFtraceEvent block_touch_buffer = 132;
    BlockUnplugFtraceEvent block_unplug = 133;
    Ext4AllocDaBlocksFtraceEvent ext4_alloc_da_blocks = 134;
    Ext4AllocateBlocksFtraceEvent ext4_allocate_blocks = 135;
    Ext4AllocateInodeFtraceEvent ext4_allocate_inode = 136;
    Ext4BeginOrderedTruncateFtraceEvent ext4_begin_ordered_truncate = 137;
    Ext4CollapseRangeFtraceEvent ext4_collapse_range = 138;
    Ext4DaReleaseSpaceFtraceEvent ext4_da_release_space = 139;
    Ext4DaReserveSpaceFtraceEvent ext4_da_reserve_space = 140;
    Ext4DaUpdateReserveSpaceFtraceEvent ext4_da_update_reserve_space = 141;
    Ext4DaWritePagesFtraceEvent ext4_da_write_pages = 142;
    Ext4DaWritePagesExtentFtraceEvent ext4_da_write_pages_extent = 143;
    Ext4DirectIOEnterFtraceEvent ext4_direct_IO_enter = 144;
    Ext4DirectIOExitFtraceEvent ext4_direct_IO_exit = 145;
    Ext4DiscardBlocksFtraceEvent ext4_discard_blocks = 146;
    Ext4DiscardPreallocationsFtraceEvent ext4_discard_preallocations = 147;
    Ext4DropInodeFtraceEvent ext4_drop_inode = 148;
    Ext4EsCacheExtentFtraceEvent ext4_es_cache_extent = 149;
    Ext4EsFindDelayedExtentRangeEnterFtraceEvent
        ext4_es_find_delayed_extent_range_enter = 150;
    Ext4EsFindDelayedExtentRangeExitFtraceEvent
        ext4_es_find_delayed_extent_range_exit = 151;
    Ext4EsInsertExtentFtraceEvent ext4_es_insert_extent = 152;
    Ext4EsLookupExtentEnterFtraceEvent ext4_es_lookup_extent_enter = 153;
    Ext4EsLookupExtentExitFtraceEvent ext4_es_lookup_extent_exit = 154;
    Ext4EsRemoveExtentFtraceEvent ext4_es_remove_extent = 155;
    Ext4EsShrinkFtraceEvent ext4_es_shrink = 156;
    Ext4EsShrinkCountFtraceEvent ext4_es_shrink_count = 157;
    Ext4EsShrinkScanEnterFtraceEvent ext4_es_shrink_scan_enter = 158;
    Ext4EsShrinkScanExitFtraceEvent ext4_es_shrink_scan_exit = 159;
    Ext4EvictInodeFtraceEvent ext4_evict_inode = 160;
    Ext4ExtConvertToInitializedEnterFtraceEvent
        ext4_ext_convert_to_initialized_enter = 161;
    Ext4ExtConvertToInitializedFastpathFtraceEvent
        ext4_ext_convert_to_initialized_fastpath = 162;
    Ext4ExtHandleUnwrittenExtentsFtraceEvent ext4_ext_handle_unwritten_extents =
        163;
    Ext4ExtInCacheFtraceEvent ext4_ext_in_cache = 164;
    Ext4ExtLoadExtentFtraceEvent ext4_ext_load_extent = 165;
    Ext4ExtMapBlocksEnterFtraceEvent ext4_ext_map_blocks_enter = 166;
    Ext4ExtMapBlocksExitFtraceEvent ext4_ext_map_blocks_exit = 167;
    Ext4ExtPutInCacheFtraceEvent ext4_ext_put_in_cache = 168;
    Ext4ExtRemoveSpaceFtraceEvent ext4_ext_remove_space = 169;
    Ext4ExtRemoveSpaceDoneFtraceEvent ext4_ext_remove_space_done = 170;
    Ext4ExtRmIdxFtraceEvent ext4_ext_rm_idx = 171;
    Ext4ExtRmLeafFtraceEvent ext4_ext_rm_leaf = 172;
    Ext4ExtShowExtentFtraceEvent ext4_ext_show_extent = 173;
    Ext4FallocateEnterFtraceEvent ext4_fallocate_enter = 174;
    Ext4FallocateExitFtraceEvent ext4_fallocate_exit = 175;
    Ext4FindDelallocRangeFtraceEvent ext4_find_delalloc_range = 176;
    Ext4ForgetFtraceEvent ext4_forget = 177;
    Ext4FreeBlocksFtraceEvent ext4_free_blocks = 178;
    Ext4FreeInodeFtraceEvent ext4_free_inode = 179;
    Ext4GetImpliedClusterAllocExitFtraceEvent
        ext4_get_implied_cluster_alloc_exit = 180;
    Ext4GetReservedClusterAllocFtraceEvent ext4_get_reserved_cluster_alloc =
        181;
    Ext4IndMapBlocksEnterFtraceEvent ext4_ind_map_blocks_enter = 182;
    Ext4IndMapBlocksExitFtraceEvent ext4_ind_map_blocks_exit = 183;
    Ext4InsertRangeFtraceEvent ext4_insert_range = 184;
    Ext4InvalidatepageFtraceEvent ext4_invalidatepage = 185;
    Ext4JournalStartFtraceEvent ext4_journal_start = 186;
    Ext4JournalStartReservedFtraceEvent ext4_journal_start_reserved = 187;
    Ext4JournalledInvalidatepageFtraceEvent ext4_journalled_invalidatepage =
        188;
    Ext4JournalledWriteEndFtraceEvent ext4_journalled_write_end = 189;
    Ext4LoadInodeFtraceEvent ext4_load_inode = 190;
    Ext4LoadInodeBitmapFtraceEvent ext4_load_inode_bitmap = 191;
    Ext4MarkInodeDirtyFtraceEvent ext4_mark_inode_dirty = 192;
    Ext4MbBitmapLoadFtraceEvent ext4_mb_bitmap_load = 193;
    Ext4MbBuddyBitmapLoadFtraceEvent ext4_mb_buddy_bitmap_load = 194;
    Ext4MbDiscardPreallocationsFtraceEvent ext4_mb_discard_preallocations = 195;
    Ext4MbNewGroupPaFtraceEvent ext4_mb_new_group_pa = 196;
    Ext4MbNewInodePaFtraceEvent ext4_mb_new_inode_pa = 197;
    Ext4MbReleaseGroupPaFtraceEvent ext4_mb_release_group_pa = 198;
    Ext4MbReleaseInodePaFtraceEvent ext4_mb_release_inode_pa = 199;
    Ext4MballocAllocFtraceEvent ext4_mballoc_alloc = 200;
    Ext4MballocDiscardFtraceEvent ext4_mballoc_discard = 201;
    Ext4MballocFreeFtraceEvent ext4_mballoc_free = 202;
    Ext4MballocPreallocFtraceEvent ext4_mballoc_prealloc = 203;
    Ext4OtherInodeUpdateTimeFtraceEvent ext4_other_inode_update_time = 204;
    Ext4PunchHoleFtraceEvent ext4_punch_hole = 205;
    Ext4ReadBlockBitmapLoadFtraceEvent ext4_read_block_bitmap_load = 206;
    Ext4ReadpageFtraceEvent ext4_readpage = 207;
    Ext4ReleasepageFtraceEvent ext4_releasepage = 208;
    Ext4RemoveBlocksFtraceEvent ext4_remove_blocks = 209;
    Ext4RequestBlocksFtraceEvent ext4_request_blocks = 210;
    Ext4RequestInodeFtraceEvent ext4_request_inode = 211;
    Ext4SyncFsFtraceEvent ext4_sync_fs = 212;
    Ext4TrimAllFreeFtraceEvent ext4_trim_all_free = 213;
    Ext4TrimExtentFtraceEvent ext4_trim_extent = 214;
    Ext4TruncateEnterFtraceEvent ext4_truncate_enter = 215;
    Ext4TruncateExitFtraceEvent ext4_truncate_exit = 216;
    Ext4UnlinkEnterFtraceEvent ext4_unlink_enter = 217;
    Ext4UnlinkExitFtraceEvent ext4_unlink_exit = 218;
    Ext4WriteBeginFtraceEvent ext4_write_begin = 219;
    // removed field with id 220;
    // removed field with id 221;
    // removed field with id 222;
    // removed field with id 223;
    // removed field with id 224;
    // removed field with id 225;
    // removed field with id 226;
    // removed field with id 227;
    // removed field with id 228;
    // removed field with id 229;
    Ext4WriteEndFtraceEvent ext4_write_end = 230;
    Ext4WritepageFtraceEvent ext4_writepage = 231;
    Ext4WritepagesFtraceEvent ext4_writepages = 232;
    Ext4WritepagesResultFtraceEvent ext4_writepages_result = 233;
    Ext4ZeroRangeFtraceEvent ext4_zero_range = 234;
    TaskNewtaskFtraceEvent task_newtask = 235;
    TaskRenameFtraceEvent task_rename = 236;
    SchedProcessExecFtraceEvent sched_process_exec = 237;
    SchedProcessExitFtraceEvent sched_process_exit = 238;
    SchedProcessForkFtraceEvent sched_process_fork = 239;
    SchedProcessFreeFtraceEvent sched_process_free = 240;
    SchedProcessHangFtraceEvent sched_process_hang = 241;
    SchedProcessWaitFtraceEvent sched_process_wait = 242;
    F2fsDoSubmitBioFtraceEvent f2fs_do_submit_bio = 243;
    F2fsEvictInodeFtraceEvent f2fs_evict_inode = 244;
    F2fsFallocateFtraceEvent f2fs_fallocate = 245;
    F2fsGetDataBlockFtraceEvent f2fs_get_data_block = 246;
    F2fsGetVictimFtraceEvent f2fs_get_victim = 247;
    F2fsIgetFtraceEvent f2fs_iget = 248;
    F2fsIgetExitFtraceEvent f2fs_iget_exit = 249;
    F2fsNewInodeFtraceEvent f2fs_new_inode = 250;
    F2fsReadpageFtraceEvent f2fs_readpage = 251;
    F2fsReserveNewBlockFtraceEvent f2fs_reserve_new_block = 252;
    F2fsSetPageDirtyFtraceEvent f2fs_set_page_dirty = 253;
    F2fsSubmitWritePageFtraceEvent f2fs_submit_write_page = 254;
    F2fsSyncFileEnterFtraceEvent f2fs_sync_file_enter = 255;
    F2fsSyncFileExitFtraceEvent f2fs_sync_file_exit = 256;
    F2fsSyncFsFtraceEvent f2fs_sync_fs = 257;
    F2fsTruncateFtraceEvent f2fs_truncate = 258;
    F2fsTruncateBlocksEnterFtraceEvent f2fs_truncate_blocks_enter = 259;
    F2fsTruncateBlocksExitFtraceEvent f2fs_truncate_blocks_exit = 260;
    F2fsTruncateDataBlocksRangeFtraceEvent f2fs_truncate_data_blocks_range =
        261;
    F2fsTruncateInodeBlocksEnterFtraceEvent f2fs_truncate_inode_blocks_enter =
        262;
    F2fsTruncateInodeBlocksExitFtraceEvent f2fs_truncate_inode_blocks_exit =
        263;
    F2fsTruncateNodeFtraceEvent f2fs_truncate_node = 264;
    F2fsTruncateNodesEnterFtraceEvent f2fs_truncate_nodes_enter = 265;
    F2fsTruncateNodesExitFtraceEvent f2fs_truncate_nodes_exit = 266;
    F2fsTruncatePartialNodesFtraceEvent f2fs_truncate_partial_nodes = 267;
    F2fsUnlinkEnterFtraceEvent f2fs_unlink_enter = 268;
    F2fsUnlinkExitFtraceEvent f2fs_unlink_exit = 269;
    F2fsVmPageMkwriteFtraceEvent f2fs_vm_page_mkwrite = 270;
    F2fsWriteBeginFtraceEvent f2fs_write_begin = 271;
    F2fsWriteCheckpointFtraceEvent f2fs_write_checkpoint = 272;
    F2fsWriteEndFtraceEvent f2fs_write_end = 273;
    AllocPagesIommuEndFtraceEvent alloc_pages_iommu_end = 274;
    AllocPagesIommuFailFtraceEvent alloc_pages_iommu_fail = 275;
    AllocPagesIommuStartFtraceEvent alloc_pages_iommu_start = 276;
    AllocPagesSysEndFtraceEvent alloc_pages_sys_end = 277;
    AllocPagesSysFailFtraceEvent alloc_pages_sys_fail = 278;
    AllocPagesSysStartFtraceEvent alloc_pages_sys_start = 279;
    DmaAllocContiguousRetryFtraceEvent dma_alloc_contiguous_retry = 280;
    IommuMapRangeFtraceEvent iommu_map_range = 281;
    IommuSecPtblMapRangeEndFtraceEvent iommu_sec_ptbl_map_range_end = 282;
    IommuSecPtblMapRangeStartFtraceEvent iommu_sec_ptbl_map_range_start = 283;
    IonAllocBufferEndFtraceEvent ion_alloc_buffer_end = 284;
    IonAllocBufferFailFtraceEvent ion_alloc_buffer_fail = 285;
    IonAllocBufferFallbackFtraceEvent ion_alloc_buffer_fallback = 286;
    IonAllocBufferStartFtraceEvent ion_alloc_buffer_start = 287;
    IonCpAllocRetryFtraceEvent ion_cp_alloc_retry = 288;
    IonCpSecureBufferEndFtraceEvent ion_cp_secure_buffer_end = 289;
    IonCpSecureBufferStartFtraceEvent ion_cp_secure_buffer_start = 290;
    IonPrefetchingFtraceEvent ion_prefetching = 291;
    IonSecureCmaAddToPoolEndFtraceEvent ion_secure_cma_add_to_pool_end = 292;
    IonSecureCmaAddToPoolStartFtraceEvent ion_secure_cma_add_to_pool_start =
        293;
    IonSecureCmaAllocateEndFtraceEvent ion_secure_cma_allocate_end = 294;
    IonSecureCmaAllocateStartFtraceEvent ion_secure_cma_allocate_start = 295;
    IonSecureCmaShrinkPoolEndFtraceEvent ion_secure_cma_shrink_pool_end = 296;
    IonSecureCmaShrinkPoolStartFtraceEvent ion_secure_cma_shrink_pool_start =
        297;
    KfreeFtraceEvent kfree = 298;
    KmallocFtraceEvent kmalloc = 299;
    KmallocNodeFtraceEvent kmalloc_node = 300;
    KmemCacheAllocFtraceEvent kmem_cache_alloc = 301;
    KmemCacheAllocNodeFtraceEvent kmem_cache_alloc_node = 302;
    KmemCacheFreeFtraceEvent kmem_cache_free = 303;
    MigratePagesEndFtraceEvent migrate_pages_end = 304;
    MigratePagesStartFtraceEvent migrate_pages_start = 305;
    MigrateRetryFtraceEvent migrate_retry = 306;
    MmPageAllocFtraceEvent mm_page_alloc = 307;
    MmPageAllocExtfragFtraceEvent mm_page_alloc_extfrag = 308;
    MmPageAllocZoneLockedFtraceEvent mm_page_alloc_zone_locked = 309;
    MmPageFreeFtraceEvent mm_page_free = 310;
    MmPageFreeBatchedFtraceEvent mm_page_free_batched = 311;
    MmPagePcpuDrainFtraceEvent mm_page_pcpu_drain = 312;
    RssStatFtraceEvent rss_stat = 313;
    IonHeapShrinkFtraceEvent ion_heap_shrink = 314;
    IonHeapGrowFtraceEvent ion_heap_grow = 315;
    FenceInitFtraceEvent fence_init = 316;
    FenceDestroyFtraceEvent fence_destroy = 317;
    FenceEnableSignalFtraceEvent fence_enable_signal = 318;
    FenceSignaledFtraceEvent fence_signaled = 319;
    ClkEnableFtraceEvent clk_enable = 320;
    ClkDisableFtraceEvent clk_disable = 321;
    ClkSetRateFtraceEvent clk_set_rate = 322;
    BinderTransactionAllocBufFtraceEvent binder_transaction_alloc_buf = 323;
    SignalDeliverFtraceEvent signal_deliver = 324;
    SignalGenerateFtraceEvent signal_generate = 325;
    OomScoreAdjUpdateFtraceEvent oom_score_adj_update = 326;
    GenericFtraceEvent generic = 327;
    MmEventRecordFtraceEvent mm_event_record = 328;
    SysEnterFtraceEvent sys_enter = 329;
    SysExitFtraceEvent sys_exit = 330;
    ZeroFtraceEvent zero = 331;
    GpuFrequencyFtraceEvent gpu_frequency = 332;
    SdeTracingMarkWriteFtraceEvent sde_tracing_mark_write = 333;
    MarkVictimFtraceEvent mark_victim = 334;
    IonStatFtraceEvent ion_stat = 335;
    IonBufferCreateFtraceEvent ion_buffer_create = 336;
    IonBufferDestroyFtraceEvent ion_buffer_destroy = 337;
    ScmCallStartFtraceEvent scm_call_start = 338;
    ScmCallEndFtraceEvent scm_call_end = 339;
    GpuMemTotalFtraceEvent gpu_mem_total = 340;
    ThermalTemperatureFtraceEvent thermal_temperature = 341;
    CdevUpdateFtraceEvent cdev_update = 342;
    CpuhpExitFtraceEvent cpuhp_exit = 343;
    CpuhpMultiEnterFtraceEvent cpuhp_multi_enter = 344;
    CpuhpEnterFtraceEvent cpuhp_enter = 345;
    CpuhpLatencyFtraceEvent cpuhp_latency = 346;
  }
}

// End of protos/perfetto/trace/ftrace/ftrace_event.proto

// Begin of protos/perfetto/trace/ftrace/ftrace_event_bundle.proto

// The result of tracing one or more ftrace data pages from a single per-cpu
// kernel ring buffer. If collating multiple pages' worth of events, all of
// them come from contiguous pages, with no kernel data loss in between.
message FtraceEventBundle {
  optional uint32 cpu = 1;
  repeated FtraceEvent event = 2;
  // Set to true if there was data loss between the last time we've read from
  // the corresponding per-cpu kernel buffer, and the earliest event recorded
  // in this bundle.
  optional bool lost_events = 3;

  // Optionally-enabled compact encoding of a batch of scheduling events. Only
  // a subset of events & their fields is recorded.
  // All fields (except comms) are stored in a structure-of-arrays form, one
  // entry in each repeated field per event.
  message CompactSched {
    // Interned table of unique strings for this bundle.
    repeated string intern_table = 5;

    // Delta-encoded timestamps across all sched_switch events within this
    // bundle. The first is absolute, each next one is relative to its
    // predecessor.
    repeated uint64 switch_timestamp = 1 [packed = true];
    repeated int64 switch_prev_state = 2 [packed = true];
    repeated int32 switch_next_pid = 3 [packed = true];
    repeated int32 switch_next_prio = 4 [packed = true];
    // One per event, index into |intern_table| corresponding to the
    // next_comm field of the event.
    repeated uint32 switch_next_comm_index = 6 [packed = true];

    // Delta-encoded timestamps across all sched_waking events within this
    // bundle. The first is absolute, each next one is relative to its
    // predecessor.
    repeated uint64 waking_timestamp = 7 [packed = true];
    repeated int32 waking_pid = 8 [packed = true];
    repeated int32 waking_target_cpu = 9 [packed = true];
    repeated int32 waking_prio = 10 [packed = true];
    // One per event, index into |intern_table| corresponding to the
    // comm field of the event.
    repeated uint32 waking_comm_index = 11 [packed = true];
  }
  optional CompactSched compact_sched = 4;
}

// End of protos/perfetto/trace/ftrace/ftrace_event_bundle.proto

// Begin of protos/perfetto/trace/ftrace/ftrace_stats.proto

// Per-CPU stats for the ftrace data source gathered from the kernel from
// /sys/kernel/debug/tracing/per_cpu/cpuX/stats.
message FtraceCpuStats {
  // CPU index.
  optional uint64 cpu = 1;

  // Number of entries still in the kernel buffer. Ideally this should be close
  // to zero, as events are consumed regularly and moved into the userspace
  // buffers (or file).
  optional uint64 entries = 2;

  // Number of events lost in kernel buffers due to overwriting of old events
  // before userspace had a chance to drain them.
  optional uint64 overrun = 3;

  // This should always be zero. If not the buffer size is way too small or
  // something went wrong with the tracer.
  optional uint64 commit_overrun = 4;

  // Bytes actually read (not overwritten).
  optional uint64 bytes_read = 5;

  // The timestamp for the oldest event still in the ring buffer.
  optional double oldest_event_ts = 6;

  // The current timestamp.
  optional double now_ts = 7;

  // If the kernel buffer has overwrite mode disabled, this will show the number
  // of new events that were lost because the buffer was full. This is similar
  // to |overrun| but only for the overwrite=false case.
  optional uint64 dropped_events = 8;

  // The number of events read.
  optional uint64 read_events = 9;
}

// Ftrace stats for all CPUs.
message FtraceStats {
  enum Phase {
    UNSPECIFIED = 0;
    START_OF_TRACE = 1;
    END_OF_TRACE = 2;
  }

  // Tells when stats were sampled. There should be one sample at the beginning
  // of the trace and one sample at the end.
  optional Phase phase = 1;

  // Per-CPU stats (one entry for each CPU).
  repeated FtraceCpuStats cpu_stats = 2;
}

// End of protos/perfetto/trace/ftrace/ftrace_stats.proto

// Begin of protos/perfetto/trace/gpu/gpu_counter_event.proto

message GpuCounterEvent {
  // The first trace packet of each session should include counter_spec.
  optional GpuCounterDescriptor counter_descriptor = 1;

  message GpuCounter {
    // required. Identifier for counter.
    optional uint32 counter_id = 1;
    // required. Value of the counter.
    oneof value {
      int64 int_value = 2;
      double double_value = 3;
    }
  }
  repeated GpuCounter counters = 2;

  // optional. Identifier for GPU in a multi-gpu device.
  optional int32 gpu_id = 3;
}

// End of protos/perfetto/trace/gpu/gpu_counter_event.proto

// Begin of protos/perfetto/trace/gpu/gpu_log.proto

// Message for logging events GPU data producer.
message GpuLog {
  enum Severity {
    LOG_SEVERITY_UNSPECIFIED = 0;
    LOG_SEVERITY_VERBOSE = 1;
    LOG_SEVERITY_DEBUG = 2;
    LOG_SEVERITY_INFO = 3;
    LOG_SEVERITY_WARNING = 4;
    LOG_SEVERITY_ERROR = 5;
  };
  optional Severity severity = 1;

  optional string tag = 2;

  optional string log_message = 3;
}

// End of protos/perfetto/trace/gpu/gpu_log.proto

// Begin of protos/perfetto/trace/gpu/gpu_render_stage_event.proto

// next id: 15
message GpuRenderStageEvent {
  // required. Unique ID for the event.
  optional uint64 event_id = 1;

  // optional. Duration of the event in nanoseconds. If unset, this is a
  // single time point event.
  optional uint64 duration = 2;

  // required. ID to a hardware queue description in the specifications.
  // InternedGpuRenderStageSpecification
  optional uint64 hw_queue_iid = 13;

  // required. ID to a render stage description in the specifications.
  // InternedGpuRenderStageSpecification
  optional uint64 stage_iid = 14;

  // optional. Identifier for GPU in a multi-gpu device.
  optional int32 gpu_id = 11;

  // required. Graphics context for the event.
  // For OpenGL, this is the GL context.
  // For Vulkan, this is the VkDevice.
  optional uint64 context = 5;

  // optional. The render target for this event.
  // For OpenGL, this is the GL frame buffer handle.
  // For Vulkan, this is the VkFrameBuffer handle.
  optional uint64 render_target_handle = 8;

  // optional. Submission ID generated by the UMD.
  // For OpenGL, the ID should map to an API submission (e.g., glFlush,
  // glFinish, eglSwapBufffers) event.  The set of submissions to the HW due
  // to a single API submission should share the same ID.
  // For Vulkan, it should map 1:1 with a vkQueueSubmit.
  optional uint32 submission_id = 10;

  // optional. Additional data for the user. This may include attributes for
  // the event like resource ids, shaders, etc.
  message ExtraData {
    optional string name = 1;
    optional string value = 2;
  }
  repeated ExtraData extra_data = 6;

  // VULKAN SPECIFICS

  // optional. The Vulkan render pass handle.
  optional uint64 render_pass_handle = 9;

  // optional. A bit mask representing which render subpasses contributed to
  // this render stage event.  Subpass index 0 is represented by setting the
  // LSB of the mask.  Additional mask can be added for subpass index greater
  // than 63.
  repeated uint64 render_subpass_index_mask = 15;

  // optional. The Vulkan command buffer handle.
  optional uint64 command_buffer_handle = 12;

  // DEPRECATED

  // Deprecated.  Use InternedGpuRenderStageSpecification instead.
  // The first trace packet of each session should include a Specifications
  // to enumerate *all* IDs that will be used. The timestamp of this packet
  // must be earlier than all other packets. Only one packet with Specifications
  // is expected.
  message Specifications {
    message ContextSpec {
      optional uint64 context = 1;
      optional int32 pid = 2;
    }
    optional ContextSpec context_spec = 1;

    message Description {
      optional string name = 1;
      optional string description = 2;
    }

    // Labels to categorize the hw Queue this event goes on.
    repeated Description hw_queue = 2;

    // Labels to categorize render stage(binning, render, compute etc).
    repeated Description stage = 3;
  }
  // Deprecated.  Use hw_queue_iid and stage_iid to refer to
  // InternedGpuRenderStageSpecification instead.
  optional Specifications specifications = 7 [deprecated = true];

  // Deprecated. Use hw_queue_iid instead;
  optional int32 hw_queue_id = 3 [deprecated = true];

  // Deprecated. Use stage_iid instead;
  optional int32 stage_id = 4 [deprecated = true];

  // Extension for vendor's custom proto.
  extensions 100;
}

// Interned data.

// The iid is the numeric value of either the GL Context or the VkDevice
// handle.
message InternedGraphicsContext {
  optional uint64 iid = 1;
  optional int32 pid = 2;
  enum Api {
    UNDEFINED = 0;
    OPEN_GL = 1;
    VULKAN = 2;
    OPEN_CL = 3;
  }
  optional Api api = 3;
}

message InternedGpuRenderStageSpecification {
  optional uint64 iid = 1;
  optional string name = 2;
  optional string description = 3;

  enum RenderStageCategory {
    OTHER = 0;
    GRAPHICS = 1;
    COMPUTE = 2;
  }
  optional RenderStageCategory category = 4;
}

// End of protos/perfetto/trace/gpu/gpu_render_stage_event.proto

// Begin of protos/perfetto/trace/gpu/vulkan_api_event.proto

// Message for recording the Vulkan call.
message VulkanApiEvent {
  oneof event {
    VkDebugUtilsObjectName vk_debug_utils_object_name = 1;
    VkQueueSubmit vk_queue_submit = 2;
  }

  // For recording vkSetDebugUtilsObjectNameEXT and
  // vkDebugMarkerSetObjectNameEXT
  message VkDebugUtilsObjectName {
    optional uint32 pid = 1;
    optional uint64 vk_device = 2;
    // VkObjectType.  Value must match
    // https://www.khronos.org/registry/vulkan/specs/1.1-extensions/man/html/VkObjectType.html.
    optional int32 object_type = 3;
    optional uint64 object = 4;
    optional string object_name = 5;
  }

  // For recording vkQueueSubmit call.
  message VkQueueSubmit {
    optional uint64 duration_ns = 1;
    optional uint32 pid = 2;
    optional uint32 tid = 3;
    optional uint64 vk_queue = 4;
    repeated uint64 vk_command_buffers = 5;
    // Submission ID.  An identifier unique to each vkQueueSubmit call.  This
    // submission_id must match GpuRenderStageEvent.submission_id if the
    // GpuRenderStageEvent is created due to this vkQueueSubmit.
    optional uint32 submission_id = 6;
  }
}

// End of protos/perfetto/trace/gpu/vulkan_api_event.proto

// Begin of protos/perfetto/trace/gpu/vulkan_memory_event.proto

// All the information that cannot be sent within a VulkanMemoryEvent message,
// are sent as annotations to the main memory event. One example is the
// properties of the object that consumes the allocated memory, for example, a
// buffer or an image.
// key_iid and string_iid are both interned strings. Original string value is
// stored in vulkan_memory_keys from
// protos/perfetto/trace/interned_data/interned_data.proto.
message VulkanMemoryEventAnnotation {
  optional uint64 key_iid = 1;
  oneof value {
    int64 int_value = 2;
    double double_value = 3;
    uint64 string_iid = 4;
  }
}

// Each VulkanMemoryEvent encompasses information regarding one single function
// call that results in reserving, binding or freeing host or GPU memory. There
// is a special message type, ANNOTATIONS, which is used to communicate
// information that are not directly related to a memory event, nonetheless are
// essential to understand the memory usage. An example is the size and memory
// types of the memory heaps.
//
// Next reserved id: 10 (up to 15).
// Next id: 21.
message VulkanMemoryEvent {
  enum Source {
    SOURCE_UNSPECIFIED = 0;
    SOURCE_DRIVER = 1;
    SOURCE_DEVICE = 2;
    SOURCE_DEVICE_MEMORY = 3;
    SOURCE_BUFFER = 4;
    SOURCE_IMAGE = 5;
  }

  enum Operation {
    OP_UNSPECIFIED = 0;

    // alloc, create
    OP_CREATE = 1;

    // free, destroy(non-bound)
    OP_DESTROY = 2;

    // bind buffer and image
    OP_BIND = 3;

    // destroy (bound)
    OP_DESTROY_BOUND = 4;

    // only annotations
    OP_ANNOTATIONS = 5;
  }

  enum AllocationScope {
    SCOPE_UNSPECIFIED = 0;
    SCOPE_COMMAND = 1;
    SCOPE_OBJECT = 2;
    SCOPE_CACHE = 3;
    SCOPE_DEVICE = 4;
    SCOPE_INSTANCE = 5;
  }

  optional Source source = 1;
  optional Operation operation = 2;
  optional int64 timestamp = 3;
  optional uint32 pid = 4;
  optional fixed64 memory_address = 5;
  optional uint64 memory_size = 6;
  // Interned string. Original string value is stored in function_names from
  // protos/perfetto/trace/interned_data/interned_data.proto.
  optional uint64 caller_iid = 7;
  optional AllocationScope allocation_scope = 8;
  // Extra related information, e.g., create configs, etc.
  repeated VulkanMemoryEventAnnotation annotations = 9;

  // Field IDs used for device memory (low sampling rate)
  optional fixed64 device = 16;
  optional fixed64 device_memory = 17;
  optional uint32 memory_type = 18;
  optional uint32 heap = 19;
  optional fixed64 object_handle = 20;
}

// End of protos/perfetto/trace/gpu/vulkan_memory_event.proto

// Begin of protos/perfetto/trace/profiling/profile_common.proto

// TODO(fmayer): Figure out naming thoroughout this file to get a
// nomenclature that works between Windows and Linux.

// The interning fields in this file can refer to 2 different intern tables,
// depending on the message they are used in. If the interned fields are present
// in ProfilePacket proto, then the intern tables included in the ProfilePacket
// should be used. If the intered fields are present in the
// StreamingProfilePacket proto, then the intern tables included in all of the
// previous InternedData message with same sequence ID should be used.
// TODO(fmayer): Move to the intern tables to a common location.
message InternedString {
  optional uint64 iid = 1;
  optional bytes str = 2;
}

// A symbol field that is emitted after the trace is written. These tables would
// be appended as the last packets in the trace that the profiler will use, so
// that the actual trace need not be rewritten to symbolize the profiles.
message ProfiledFrameSymbols {
  // Use the frame id as the interning key for the symbols.
  optional uint64 frame_iid = 1;

  // These are repeated because when inlining happens, multiple functions'
  // frames can be at a single address. Imagine function Foo calling the
  // std::vector<int> constructor, which gets inlined at 0xf00. We then get
  // both Foo and the std::vector<int> constructor when we symbolize the
  // address.

  // key to InternedString
  repeated uint64 function_name_id = 2;

  // key to InternedString
  repeated uint64 file_name_id = 3;

  repeated uint32 line_number = 4;
}

message Line {
  optional string function_name = 1;
  optional string source_file_name = 2;
  optional uint32 line_number = 3;
}

// Symbols for a given address in a module.
message AddressSymbols {
  optional uint64 address = 1;

  // Source lines that correspond to this address.
  //
  // These are repeated because when inlining happens, multiple functions'
  // frames can be at a single address. Imagine function Foo calling the
  // std::vector<int> constructor, which gets inlined at 0xf00. We then get
  // both Foo and the std::vector<int> constructor when we symbolize the
  // address.
  repeated Line lines = 2;
}

// Symbols for addresses seen in a module.
message ModuleSymbols {
  // Fully qualified path to the mapping.
  // E.g. /system/lib64/libc.so.
  optional string path = 1;

  // .note.gnu.build-id on Linux (not hex encoded).
  // uuid on MacOS.
  // Module GUID on Windows.
  optional string build_id = 2;
  repeated AddressSymbols address_symbols = 3;
}

message Mapping {
  // Interning key.
  optional uint64 iid = 1;

  // Interning key.
  optional uint64 build_id = 2;

  // The linker may create multiple memory mappings for the same shared
  // library.
  // This is so that the ELF header is mapped as read only, while the
  // executable memory is mapped as executable only.
  // The details of this depend on the linker, a possible mapping of an ELF
  // file is this:
  //         +----------------------+
  // ELF     |xxxxxxxxxyyyyyyyyyyyyy|
  //         +---------+------------+
  //         |         |
  //         | read    | executable
  //         v mapping v mapping
  //         +----------------------+
  // Memory  |xxxxxxxxx|yyyyyyyyyyyy|
  //         +------------------+---+
  //         ^         ^        ^
  //         +         +        +
  //       start     exact    relpc
  //       offset   offset    0x1800
  //       0x0000   0x1000
  //
  // exact_offset is the offset into the library file of this mapping.
  // start_offset is the offset into the library file of the first mapping
  // for that library. For native libraries (.so files) this should be 0.

  // This is not set on Android 10.
  optional uint64 exact_offset = 8;

  optional uint64 start_offset = 3;
  optional uint64 start = 4;
  optional uint64 end = 5;
  optional uint64 load_bias = 6;

  // E.g. ["system", "lib64", "libc.so"]
  // id of string.
  repeated uint64 path_string_ids = 7;
}

message Frame {
  // Interning key
  optional uint64 iid = 1;

  // E.g. "fopen"
  // id of string.
  optional uint64 function_name_id = 2;

  optional uint64 mapping_id = 3;
  optional uint64 rel_pc = 4;
}

message Callstack {
  optional uint64 iid = 1;
  // Frames of this callstack. Bottom frame first.
  repeated uint64 frame_ids = 2;
}

// End of protos/perfetto/trace/profiling/profile_common.proto

// Begin of protos/perfetto/trace/track_event/debug_annotation.proto

// Key/value annotations provided in untyped TRACE_EVENT macros. These
// annotations are intended for debug use and are not considered a stable API
// surface. As such, they should not be relied upon to implement (new) metrics.
//
// Next ID: 10.
message DebugAnnotation {
  message NestedValue {
    enum NestedType {
      // leaf value.
      UNSPECIFIED = 0;
      DICT = 1;
      ARRAY = 2;
    }
    optional NestedType nested_type = 1;

    repeated string dict_keys = 2;
    repeated NestedValue dict_values = 3;
    repeated NestedValue array_values = 4;
    optional int64 int_value = 5;
    optional double double_value = 6;
    optional bool bool_value = 7;
    optional string string_value = 8;
  }

  oneof name_field {
    // interned DebugAnnotationName.
    uint64 name_iid = 1;
    // non-interned variant.
    string name = 10;
  }

  oneof value {
    bool bool_value = 2;
    uint64 uint_value = 3;
    int64 int_value = 4;
    double double_value = 5;
    string string_value = 6;
    // Pointers are stored in a separate type as the JSON output treats them
    // differently from other uint64 values.
    uint64 pointer_value = 7;
    NestedValue nested_value = 8;

    // Legacy instrumentation may not support conversion of nested data to
    // NestedValue yet.
    string legacy_json_value = 9;
  }
}

// --------------------
// Interned data types:
// --------------------

message DebugAnnotationName {
  optional uint64 iid = 1;
  optional string name = 2;
}

// End of protos/perfetto/trace/track_event/debug_annotation.proto

// Begin of protos/perfetto/trace/track_event/log_message.proto

message LogMessage {
  // interned SourceLocation.
  optional uint64 source_location_iid = 1;
  // interned LogMessageBody.
  optional uint64 body_iid = 2;
}

// --------------------
// Interned data types:
// --------------------

message LogMessageBody {
  optional uint64 iid = 1;
  optional string body = 2;
}
// End of protos/perfetto/trace/track_event/log_message.proto

// Begin of protos/perfetto/trace/track_event/source_location.proto

// --------------------
// Interned data types:
// --------------------

message SourceLocation {
  optional uint64 iid = 1;

  // We intend to add a binary symbol version of this in the future.
  optional string file_name = 2;
  optional string function_name = 3;
  optional uint32 line_number = 4;
}

// End of protos/perfetto/trace/track_event/source_location.proto

// Begin of protos/perfetto/trace/track_event/chrome_compositor_scheduler_state.proto

// Describes Chrome's Compositor scheduler's current state and associated
// variables.
//
// These protos and enums were adapted from the corresponding original JSON
// trace event for the scheduler state. In contrast to the JSON, we use strongly
// typed enum values instead of strings for many fields, and
// microsecond-granularity timestamps.
//
// The original format was generated in JSON by the code at
// https://cs.chromium.org/chromium/src/cc/scheduler/scheduler.cc?l=870&rcl=5e15eabc9c0eec8daf94fdf78e93f13b6e3b63dd
//
// TODO(nuskos): Update link once we've switched to writing this proto.
//
// All non-delta-timestamps are absolute CLOCK_MONOTONIC timestamps.

enum ChromeCompositorSchedulerAction {
  CC_SCHEDULER_ACTION_UNSPECIFIED = 0;
  CC_SCHEDULER_ACTION_NONE = 1;
  CC_SCHEDULER_ACTION_SEND_BEGIN_MAIN_FRAME = 2;
  CC_SCHEDULER_ACTION_COMMIT = 3;
  CC_SCHEDULER_ACTION_ACTIVATE_SYNC_TREE = 4;
  CC_SCHEDULER_ACTION_DRAW_IF_POSSIBLE = 5;
  CC_SCHEDULER_ACTION_DRAW_FORCED = 6;
  CC_SCHEDULER_ACTION_DRAW_ABORT = 7;
  CC_SCHEDULER_ACTION_BEGIN_LAYER_TREE_FRAME_SINK_CREATION = 8;
  CC_SCHEDULER_ACTION_PREPARE_TILES = 9;
  CC_SCHEDULER_ACTION_INVALIDATE_LAYER_TREE_FRAME_SINK = 10;
  CC_SCHEDULER_ACTION_PERFORM_IMPL_SIDE_INVALIDATION = 11;
  CC_SCHEDULER_ACTION_NOTIFY_BEGIN_MAIN_FRAME_NOT_EXPECTED_UNTIL = 12;
  CC_SCHEDULER_ACTION_NOTIFY_BEGIN_MAIN_FRAME_NOT_EXPECTED_SOON = 13;
}

// Next id: 18
message ChromeCompositorSchedulerState {
  enum BeginImplFrameDeadlineMode {
    DEADLINE_MODE_UNSPECIFIED = 0;
    DEADLINE_MODE_NONE = 1;
    DEADLINE_MODE_IMMEDIATE = 2;
    DEADLINE_MODE_REGULAR = 3;
    DEADLINE_MODE_LATE = 4;
    DEADLINE_MODE_BLOCKED = 5;
  }
  optional ChromeCompositorStateMachine state_machine = 1;
  optional bool observing_begin_frame_source = 2;
  optional bool begin_impl_frame_deadline_task = 3;
  optional bool pending_begin_frame_task = 4;
  optional bool skipped_last_frame_missed_exceeded_deadline = 5;
  optional bool skipped_last_frame_to_reduce_latency = 6;
  optional ChromeCompositorSchedulerAction inside_action = 7;
  optional BeginImplFrameDeadlineMode deadline_mode = 8;
  optional int64 deadline_us = 9;
  optional int64 deadline_scheduled_at_us = 10;
  optional int64 now_us = 11;
  optional int64 now_to_deadline_delta_us = 12;
  optional int64 now_to_deadline_scheduled_at_delta_us = 13;
  optional BeginImplFrameArgs begin_impl_frame_args = 14;
  optional BeginFrameObserverState begin_frame_observer_state = 15;
  optional BeginFrameSourceState begin_frame_source_state = 16;
  optional CompositorTimingHistory compositor_timing_history = 17;
}

// Describes the current values stored in the Chrome Compositor state machine.
// Next id: 3
message ChromeCompositorStateMachine {
  // Next id: 6
  message MajorState {
    enum BeginImplFrameState {
      BEGIN_IMPL_FRAME_UNSPECIFIED = 0;
      BEGIN_IMPL_FRAME_IDLE = 1;
      BEGIN_IMPL_FRAME_INSIDE_BEGIN_FRAME = 2;
      BEGIN_IMPL_FRAME_INSIDE_DEADLINE = 3;
    }
    enum BeginMainFrameState {
      BEGIN_MAIN_FRAME_UNSPECIFIED = 0;
      BEGIN_MAIN_FRAME_IDLE = 1;
      BEGIN_MAIN_FRAME_SENT = 2;
      BEGIN_MAIN_FRAME_READY_TO_COMMIT = 3;
    }
    enum LayerTreeFrameSinkState {
      LAYER_TREE_FRAME_UNSPECIFIED = 0;
      LAYER_TREE_FRAME_NONE = 1;
      LAYER_TREE_FRAME_ACTIVE = 2;
      LAYER_TREE_FRAME_CREATING = 3;
      LAYER_TREE_FRAME_WAITING_FOR_FIRST_COMMIT = 4;
      LAYER_TREE_FRAME_WAITING_FOR_FIRST_ACTIVATION = 5;
    }
    enum ForcedRedrawOnTimeoutState {
      FORCED_REDRAW_UNSPECIFIED = 0;
      FORCED_REDRAW_IDLE = 1;
      FORCED_REDRAW_WAITING_FOR_COMMIT = 2;
      FORCED_REDRAW_WAITING_FOR_ACTIVATION = 3;
      FORCED_REDRAW_WAITING_FOR_DRAW = 4;
    }
    optional ChromeCompositorSchedulerAction next_action = 1;
    optional BeginImplFrameState begin_impl_frame_state = 2;
    optional BeginMainFrameState begin_main_frame_state = 3;
    optional LayerTreeFrameSinkState layer_tree_frame_sink_state = 4;
    optional ForcedRedrawOnTimeoutState forced_redraw_state = 5;
  }
  optional MajorState major_state = 1;

  // Next id: 47
  message MinorState {
    enum TreePriority {
      TREE_PRIORITY_UNSPECIFIED = 0;
      TREE_PRIORITY_SAME_PRIORITY_FOR_BOTH_TREES = 1;
      TREE_PRIORITY_SMOOTHNESS_TAKES_PRIORITY = 2;
      TREE_PRIORITY_NEW_CONTENT_TAKES_PRIORITY = 3;
    }
    enum ScrollHandlerState {
      SCROLL_HANDLER_UNSPECIFIED = 0;
      SCROLL_AFFECTS_SCROLL_HANDLER = 1;
      SCROLL_DOES_NOT_AFFECT_SCROLL_HANDLER = 2;
    }
    optional int32 commit_count = 1;
    optional int32 current_frame_number = 2;
    optional int32 last_frame_number_submit_performed = 3;
    optional int32 last_frame_number_draw_performed = 4;
    optional int32 last_frame_number_begin_main_frame_sent = 5;
    optional bool did_draw = 6;
    optional bool did_send_begin_main_frame_for_current_frame = 7;
    optional bool did_notify_begin_main_frame_not_expected_until = 8;
    optional bool did_notify_begin_main_frame_not_expected_soon = 9;
    optional bool wants_begin_main_frame_not_expected = 10;
    optional bool did_commit_during_frame = 11;
    optional bool did_invalidate_layer_tree_frame_sink = 12;
    optional bool did_perform_impl_side_invalidaion = 13;
    optional bool did_prepare_tiles = 14;
    optional int32 consecutive_checkerboard_animations = 15;
    optional int32 pending_submit_frames = 16;
    optional int32 submit_frames_with_current_layer_tree_frame_sink = 17;
    optional bool needs_redraw = 18;
    optional bool needs_prepare_tiles = 19;
    optional bool needs_begin_main_frame = 20;
    optional bool needs_one_begin_impl_frame = 21;
    optional bool visible = 22;
    optional bool begin_frame_source_paused = 23;
    optional bool can_draw = 24;
    optional bool resourceless_draw = 25;
    optional bool has_pending_tree = 26;
    optional bool pending_tree_is_ready_for_activation = 27;
    optional bool active_tree_needs_first_draw = 28;
    optional bool active_tree_is_ready_to_draw = 29;
    optional bool did_create_and_initialize_first_layer_tree_frame_sink = 30;
    optional TreePriority tree_priority = 31;
    optional ScrollHandlerState scroll_handler_state = 32;
    optional bool critical_begin_main_frame_to_activate_is_fast = 33;
    optional bool main_thread_missed_last_deadline = 34;
    optional bool skip_next_begin_main_frame_to_reduce_latency = 35;
    optional bool video_needs_begin_frames = 36;
    optional bool defer_begin_main_frame = 37;
    optional bool last_commit_had_no_updates = 38;
    optional bool did_draw_in_last_frame = 39;
    optional bool did_submit_in_last_frame = 40;
    optional bool needs_impl_side_invalidation = 41;
    optional bool current_pending_tree_is_impl_side = 42;
    optional bool previous_pending_tree_was_impl_side = 43;
    optional bool processing_animation_worklets_for_active_tree = 44;
    optional bool processing_animation_worklets_for_pending_tree = 45;
    optional bool processing_paint_worklets_for_pending_tree = 46;
  }
  optional MinorState minor_state = 2;
}

// Next id: 12
message BeginFrameArgs {
  // JSON format has a "type" field that was always just "BeginFrameArgs" we
  // drop this in the proto representation, and instead make the JSON format
  // "subtype" field become the type field.
  enum BeginFrameArgsType {
    BEGIN_FRAME_ARGS_TYPE_UNSPECIFIED = 0;
    BEGIN_FRAME_ARGS_TYPE_INVALID = 1;
    BEGIN_FRAME_ARGS_TYPE_NORMAL = 2;
    BEGIN_FRAME_ARGS_TYPE_MISSED = 3;
  }
  optional BeginFrameArgsType type = 1;
  optional uint64 source_id = 2;
  optional uint64 sequence_number = 3;
  optional int64 frame_time_us = 4;
  optional int64 deadline_us = 5;
  optional int64 interval_delta_us = 6;
  optional bool on_critical_path = 7;
  optional bool animate_only = 8;
  oneof created_from {
    // The interned SourceLocation.
    uint64 source_location_iid = 9;
    // The SourceLocation that this args was created from.
    // TODO(nuskos): Eventually we will support interning inside of
    // TypedArgument TraceEvents and then we shouldn't need this SourceLocation
    // since we can emit it as part of the InternedData message. When we can
    // remove this |source_location|.
    SourceLocation source_location = 10;
  }
}

// Next id: 7
message BeginImplFrameArgs {
  optional int64 updated_at_us = 1;
  optional int64 finished_at_us = 2;
  enum State {
    BEGIN_FRAME_FINISHED = 0;
    BEGIN_FRAME_USING = 1;
  }
  optional State state = 3;
  oneof args {
    // Only set if |state| is BEGIN_FRAME_FINISHED.
    BeginFrameArgs current_args = 4;
    // Only set if |state| is BEGIN_FRAME_USING.
    BeginFrameArgs last_args = 5;
  }
  message TimestampsInUs {
    optional int64 interval_delta = 1;
    optional int64 now_to_deadline_delta = 2;
    optional int64 frame_time_to_now_delta = 3;
    optional int64 frame_time_to_deadline_delta = 4;
    optional int64 now = 5;
    optional int64 frame_time = 6;
    optional int64 deadline = 7;
  }
  optional TimestampsInUs timestamps_in_us = 6;
}

message BeginFrameObserverState {
  optional int64 dropped_begin_frame_args = 1;
  optional BeginFrameArgs last_begin_frame_args = 2;
}

message BeginFrameSourceState {
  optional uint32 source_id = 1;
  optional bool paused = 2;
  optional uint32 num_observers = 3;
  optional BeginFrameArgs last_begin_frame_args = 4;
}

message CompositorTimingHistory {
  optional int64 begin_main_frame_queue_critical_estimate_delta_us = 1;
  optional int64 begin_main_frame_queue_not_critical_estimate_delta_us = 2;
  optional int64 begin_main_frame_start_to_ready_to_commit_estimate_delta_us =
      3;
  optional int64 commit_to_ready_to_activate_estimate_delta_us = 4;
  optional int64 prepare_tiles_estimate_delta_us = 5;
  optional int64 activate_estimate_delta_us = 6;
  optional int64 draw_estimate_delta_us = 7;
}

// End of protos/perfetto/trace/track_event/chrome_compositor_scheduler_state.proto

// Begin of protos/perfetto/trace/track_event/chrome_frame_reporter.proto

message ChromeFrameReporter {
  enum State {
    // The frame did not have any updates to present.
    STATE_NO_UPDATE_DESIRED = 0;

    // The frame presented all the desired updates (i.e. any updates requested
    // from both the compositor thread and main-threads were handled).
    STATE_PRESENTED_ALL = 1;

    // The frame was presented with some updates, but also missed some updates
    // (e.g. missed updates from the main-thread, but included updates from the
    // compositor thread).
    STATE_PRESENTED_PARTIAL = 2;

    // The frame was dropped, i.e. some updates were desired for the frame, but
    // was not presented.
    STATE_DROPPED = 3;
  };

  optional State state = 1;

  enum FrameDropReason {
    REASON_UNSPECIFIED = 0;

    // Frame was dropped by the display-compositor.
    // The display-compositor may drop a frame some times (e.g. the frame missed
    // the deadline, or was blocked on surface-sync, etc.)
    REASON_DISPLAY_COMPOSITOR = 1;

    // Frame was dropped because of the main-thread.
    // The main-thread may cause a frame to be dropped, e.g. if the main-thread
    // is running expensive javascript, or doing a lot of layout updates, etc.
    REASON_MAIN_THREAD = 2;

    // Frame was dropped by the client compositor.
    // The client compositor can drop some frames too (e.g. attempting to
    // recover latency, missing the deadline, etc.).
    REASON_CLIENT_COMPOSITOR = 3;
  };

  // The reason is set only if |state| is not |STATE_UPDATED_ALL|.
  optional FrameDropReason reason = 2;

  optional uint64 frame_source = 3;
  optional uint64 frame_sequence = 4;

  // If this is a droped frame (i.e. if |state| is set to |STATE_DROPPED| or
  // |STATE_PRESENTED_PARTIAL|), then indicates whether this frame impacts
  // smoothness.
  optional bool affects_smoothness = 5;
}

// End of protos/perfetto/trace/track_event/chrome_frame_reporter.proto

// Begin of protos/perfetto/trace/track_event/chrome_histogram_sample.proto

// An individual histogram sample logged via Chrome's UMA metrics system.
message ChromeHistogramSample {
  // MD5 hash of the metric name. Either |name_hash| or |name| or both
  // must be present.
  optional uint64 name_hash = 1;
  optional string name = 2;
  optional int64 sample = 3;
}

// End of protos/perfetto/trace/track_event/chrome_histogram_sample.proto

// Begin of protos/perfetto/trace/track_event/chrome_keyed_service.proto

// Details about one of Chrome's keyed services associated with the event.
message ChromeKeyedService {
  // Name of the service, e.g. "MediaRouter", "PreviewsService", etc. (in
  // Chrome, these are static strings known at compile time).
  optional string name = 1;
}

// End of protos/perfetto/trace/track_event/chrome_keyed_service.proto

// Begin of protos/perfetto/trace/track_event/chrome_latency_info.proto

message ChromeLatencyInfo {
  optional int64 trace_id = 1;

  // NEXT ID: 11
  // All step are optional but the enum is ordered (not by number) below in the
  // order we expect them to appear if they are emitted in trace in a blocking
  // fashion.
  enum Step {
    STEP_UNSPECIFIED = 0;
    // Emitted on the browser main thread.
    STEP_SEND_INPUT_EVENT_UI = 3;
    // Happens on the renderer's compositor.
    STEP_HANDLE_INPUT_EVENT_IMPL = 5;
    STEP_DID_HANDLE_INPUT_AND_OVERSCROLL = 8;
    // Occurs on the Renderer's main thread.
    STEP_HANDLE_INPUT_EVENT_MAIN = 4;
    STEP_MAIN_THREAD_SCROLL_UPDATE = 2;
    STEP_HANDLE_INPUT_EVENT_MAIN_COMMIT = 1;
    // Could be emitted on both the renderer's main OR compositor.
    STEP_HANDLED_INPUT_EVENT_MAIN_OR_IMPL = 9;
    // Optionally sometimes HANDLED_INPUT_EVENT_MAIN_OR_IMPL will proxy to the
    // renderer's compositor and this will be emitted.
    STEP_HANDLED_INPUT_EVENT_IMPL = 10;
    // Renderer's compositor.
    STEP_SWAP_BUFFERS = 6;
    // Happens on the VizCompositor in the GPU process.
    STEP_DRAW_AND_SWAP = 7;
  };

  optional Step step = 2;
  optional int32 frame_tree_node_id = 3;

  // This enum is a copy of LatencyComponentType enum in Chrome, located in
  // ui/latency/latency_info.h, modulo added UNKNOWN value per protobuf
  // practices.
  enum LatencyComponentType {
    COMPONENT_UNSPECIFIED = 0;
    COMPONENT_INPUT_EVENT_LATENCY_BEGIN_RWH = 1;
    COMPONENT_INPUT_EVENT_LATENCY_SCROLL_UPDATE_ORIGINAL = 2;
    COMPONENT_INPUT_EVENT_LATENCY_FIRST_SCROLL_UPDATE_ORIGINAL = 3;
    COMPONENT_INPUT_EVENT_LATENCY_ORIGINAL = 4;
    COMPONENT_INPUT_EVENT_LATENCY_UI = 5;
    COMPONENT_INPUT_EVENT_LATENCY_RENDERER_MAIN = 6;
    COMPONENT_INPUT_EVENT_LATENCY_RENDERING_SCHEDULED_MAIN = 7;
    COMPONENT_INPUT_EVENT_LATENCY_RENDERING_SCHEDULED_IMPL = 8;
    COMPONENT_INPUT_EVENT_LATENCY_SCROLL_UPDATE_LAST_EVENT = 9;
    COMPONENT_INPUT_EVENT_LATENCY_ACK_RWH = 10;
    COMPONENT_INPUT_EVENT_LATENCY_RENDERER_SWAP = 11;
    COMPONENT_DISPLAY_COMPOSITOR_RECEIVED_FRAME = 12;
    COMPONENT_INPUT_EVENT_GPU_SWAP_BUFFER = 13;
    COMPONENT_INPUT_EVENT_LATENCY_FRAME_SWAP = 14;
  }

  message ComponentInfo {
    optional LatencyComponentType component_type = 1;

    // Microsecond timestamp in CLOCK_MONOTONIC domain
    optional uint64 time_us = 2;
  };

  repeated ComponentInfo component_info = 4;
  optional bool is_coalesced = 5;
  optional int64 gesture_scroll_id = 6;
}

// End of protos/perfetto/trace/track_event/chrome_latency_info.proto

// Begin of protos/perfetto/trace/track_event/chrome_legacy_ipc.proto

// Details about a legacy Chrome IPC message that is either sent by the event.
// TODO(eseckler): Also use this message on the receiving side?
message ChromeLegacyIpc {
  enum MessageClass {
    CLASS_UNSPECIFIED = 0;
    CLASS_AUTOMATION = 1;
    CLASS_FRAME = 2;
    CLASS_PAGE = 3;
    CLASS_VIEW = 4;
    CLASS_WIDGET = 5;
    CLASS_INPUT = 6;
    CLASS_TEST = 7;
    CLASS_WORKER = 8;
    CLASS_NACL = 9;
    CLASS_GPU_CHANNEL = 10;
    CLASS_MEDIA = 11;
    CLASS_PPAPI = 12;
    CLASS_CHROME = 13;
    CLASS_DRAG = 14;
    CLASS_PRINT = 15;
    CLASS_EXTENSION = 16;
    CLASS_TEXT_INPUT_CLIENT = 17;
    CLASS_BLINK_TEST = 18;
    CLASS_ACCESSIBILITY = 19;
    CLASS_PRERENDER = 20;
    CLASS_CHROMOTING = 21;
    CLASS_BROWSER_PLUGIN = 22;
    CLASS_ANDROID_WEB_VIEW = 23;
    CLASS_NACL_HOST = 24;
    CLASS_ENCRYPTED_MEDIA = 25;
    CLASS_CAST = 26;
    CLASS_GIN_JAVA_BRIDGE = 27;
    CLASS_CHROME_UTILITY_PRINTING = 28;
    CLASS_OZONE_GPU = 29;
    CLASS_WEB_TEST = 30;
    CLASS_NETWORK_HINTS = 31;
    CLASS_EXTENSIONS_GUEST_VIEW = 32;
    CLASS_GUEST_VIEW = 33;
    CLASS_MEDIA_PLAYER_DELEGATE = 34;
    CLASS_EXTENSION_WORKER = 35;
    CLASS_SUBRESOURCE_FILTER = 36;
    CLASS_UNFREEZABLE_FRAME = 37;
  }

  // Corresponds to the message class type defined in Chrome's IPCMessageStart
  // enum, e.g. FrameMsgStart,
  optional MessageClass message_class = 1;

  // Line number of the message definition. See Chrome's IPC_MESSAGE_ID and
  // IPC_MESSAGE_START macros.
  optional uint32 message_line = 2;
}

// End of protos/perfetto/trace/track_event/chrome_legacy_ipc.proto

// Begin of protos/perfetto/trace/track_event/chrome_message_pump.proto

// Details about Chrome message pump events
message ChromeMessagePump {
  // True if there are sent messages in the queue.
  optional bool sent_messages_in_queue = 1;
}

// End of protos/perfetto/trace/track_event/chrome_message_pump.proto

// Begin of protos/perfetto/trace/track_event/chrome_user_event.proto

// Details about a UI interaction initiated by the user, such as opening or
// closing a tab or a context menu.
message ChromeUserEvent {
  // Name of the action, e.g. "NewTab", "ShowBookmarkManager", etc. (in
  // Chrome, these are usually static strings known at compile time, or
  // concatenations of multiple such static strings).
  optional string action = 1;

  // MD5 hash of the action string.
  optional uint64 action_hash = 2;
}

// End of protos/perfetto/trace/track_event/chrome_user_event.proto

// Begin of protos/perfetto/trace/track_event/task_execution.proto

// TrackEvent arguments describing the execution of a task.
message TaskExecution {
  // Source location that the task was posted from.
  // interned SourceLocation.
  optional uint64 posted_from_iid = 1;
}
// End of protos/perfetto/trace/track_event/task_execution.proto

// Begin of protos/perfetto/trace/track_event/track_event.proto

// NOTE: Full TrackEvent support in the client lib and chrome is WIP, thus these
// protos are still subject to change. Don't depend on them staying as they are.

// Trace events emitted by client instrumentation library (TRACE_EVENT macros),
// which describe activity on a track, such as a thread or asynchronous event
// track. The track is specified using separate TrackDescriptor messages and
// referred to via the track's UUID.
//
// A simple TrackEvent packet specifies a timestamp, category, name and type:
//   trace_packet {
//     timestamp: 1000
//     track_event {
//       categories: ["my_cat"]
//       name: "my_event"
//       type: TYPE_INSTANT
//      }
//    }
//
// To associate an event with a custom track (e.g. a thread), the track is
// defined in a separate packet and referred to from the TrackEvent by its UUID:
//   trace_packet {
//     track_descriptor {
//       track_uuid: 1234
//       name: "my_track"
//
//       // Optionally, associate the track with a thread.
//       thread_descriptor {
//         pid: 10
//         tid: 10
//         ..
//       }
//     }
//   }
//
// A pair of TYPE_SLICE_BEGIN and _END events form a slice on the track:
//   trace_packet {
//     timestamp: 1200
//     track_event {
//       track_uuid: 1234
//       categories: ["my_cat"]
//       name: "my_slice"
//       type: TYPE_SLICE_BEGIN
//     }
//   }
//   trace_packet {
//     timestamp: 1400
//     track_event {
//       track_uuid: 1234
//       type: TYPE_SLICE_END
//     }
//   }
//
// TrackEvents also support optimizations to reduce data repetition and encoded
// data size, e.g. through data interning (names, categories, ...) and delta
// encoding of timestamps/counters. For details, see the InternedData message.
// Further, default values for attributes of events on the same sequence (e.g.
// their default track association) can be emitted as part of a
// TrackEventDefaults message.
//
// Next reserved id: 13 (up to 15). Next id: 35.
message TrackEvent {
  // Names of categories of the event. In the client library, categories are a
  // way to turn groups of individual events on or off.
  // interned EventCategoryName.
  repeated uint64 category_iids = 3;
  // non-interned variant.
  repeated string categories = 22;

  // Optional name of the event for its display in trace viewer. May be left
  // unspecified for events with typed arguments.
  //
  // Note that metrics should not rely on event names, as they are prone to
  // changing. Instead, they should use typed arguments to identify the events
  // they are interested in.
  oneof name_field {
    // interned EventName.
    uint64 name_iid = 10;
    // non-interned variant.
    string name = 23;
  }

  // TODO(eseckler): Support using binary symbols for category/event names.

  // Type of the TrackEvent (required if |phase| in LegacyEvent is not set).
  enum Type {
    TYPE_UNSPECIFIED = 0;

    // Slice events are events that have a begin and end timestamp, i.e. a
    // duration. They can be nested similar to a callstack: If, on the same
    // track, event B begins after event A, but before A ends, B is a child
    // event of A and will be drawn as a nested event underneath A in the UI.
    // Note that child events should always end before their parents (e.g. B
    // before A).
    //
    // Each slice event is formed by a pair of BEGIN + END events. The END event
    // does not need to repeat any TrackEvent fields it has in common with its
    // corresponding BEGIN event. Arguments and debug annotations of the BEGIN +
    // END pair will be merged during trace import.
    //
    // Note that we deliberately chose not to support COMPLETE events (which
    // would specify a duration directly) since clients would need to delay
    // writing them until the slice is completed, which can result in reordered
    // events in the trace and loss of unfinished events at the end of a trace.
    TYPE_SLICE_BEGIN = 1;
    TYPE_SLICE_END = 2;

    // Instant events are nestable events without duration. They can be children
    // of slice events on the same track.
    TYPE_INSTANT = 3;

    // Event that provides a value for a counter track. |track_uuid| should
    // refer to a counter track and |counter_value| set to the new value. Note
    // that most other TrackEvent fields (e.g. categories, name, ..) are not
    // supported for TYPE_COUNTER events. See also CounterDescriptor.
    TYPE_COUNTER = 4;
  }
  optional Type type = 9;

  // Identifies the track of the event. The default value may be overridden
  // using TrackEventDefaults, e.g., to specify the track of the TraceWriter's
  // sequence (in most cases sequence = one thread). If no value is specified
  // here or in TrackEventDefaults, the TrackEvent will be associated with an
  // implicit trace-global track (uuid 0). See TrackDescriptor::uuid.
  optional uint64 track_uuid = 11;

  // A new value for a counter track. |track_uuid| should refer to a track with
  // a CounterDescriptor, and |type| should be TYPE_COUNTER. For a more
  // efficient encoding of counter values that are sampled at the beginning/end
  // of a slice, see |extra_counter_values| and |extra_counter_track_uuids|.
  // Counter values can optionally be encoded in as delta values (positive or
  // negative) on each packet sequence (see CounterIncrementalBase).
  optional int64 counter_value = 30;

  // To encode counter values more efficiently, we support attaching additional
  // counter values to a TrackEvent of any type. All values will share the same
  // timestamp specified in the TracePacket. The value at
  // extra_counter_values[N] is for the counter track referenced by
  // extra_counter_track_uuids[N].
  //
  // |extra_counter_track_uuids| may also be set via TrackEventDefaults. There
  // should always be equal or more uuids than values. It is valid to set more
  // uuids (e.g. via defaults) than values. If uuids are specified in
  // TrackEventDefaults and a TrackEvent, the TrackEvent uuids override the
  // default uuid list.
  //
  // For example, this allows snapshotting the thread time clock at each
  // thread-track BEGIN and END event to capture the cpu time delta of a slice.
  repeated uint64 extra_counter_track_uuids = 31;
  repeated int64 extra_counter_values = 12;

  // TODO(eseckler): Add flow event support.

  // ---------------------------------------------------------------------------
  // TrackEvent arguments:
  // ---------------------------------------------------------------------------

  // Unstable key/value annotations shown in the trace viewer but not intended
  // for metrics use.
  repeated DebugAnnotation debug_annotations = 4;

  // Typed event arguments:
  optional TaskExecution task_execution = 5;
  optional LogMessage log_message = 21;
  optional ChromeCompositorSchedulerState cc_scheduler_state = 24;
  optional ChromeUserEvent chrome_user_event = 25;
  optional ChromeKeyedService chrome_keyed_service = 26;
  optional ChromeLegacyIpc chrome_legacy_ipc = 27;
  optional ChromeHistogramSample chrome_histogram_sample = 28;
  optional ChromeLatencyInfo chrome_latency_info = 29;
  optional ChromeFrameReporter chrome_frame_reporter = 32;

  // This field is used only if the source location represents the function that
  // executes during this event.
  oneof source_location_field {
    // Non-interned field.
    SourceLocation source_location = 33;
    // TODO(ssid): The interned source locations are not parsed by trace
    // processor.
    // Interned field.
    uint64 source_location_iid = 34;
  }

  optional ChromeMessagePump chrome_message_pump = 35;

  // New argument types go here :)

  // Extension range for typed events defined externally.
  // See docs/design-docs/extensions.md for more details.
  //
  // Extension support is work-in-progress, in the future the way to reserve a
  // subrange for a particular project will be described here and in the design
  // document linked above.
  //
  // Contact <EMAIL> if you are interested in a subrange
  // for your project.

  // Extension range for future use.
  extensions 1000 to 9899;
  // Reserved for Perfetto unit and integration tests.
  extensions 9900 to 10000;

  // ---------------------------------------------------------------------------
  // Deprecated / legacy event fields, which will be removed in the future:
  // ---------------------------------------------------------------------------

  // Deprecated. Use the |timestamp| and |timestamp_clock_id| fields in
  // TracePacket instead.
  //
  // Timestamp in microseconds (usually CLOCK_MONOTONIC).
  oneof timestamp {
    // Delta timestamp value since the last TrackEvent or ThreadDescriptor. To
    // calculate the absolute timestamp value, sum up all delta values of the
    // preceding TrackEvents since the last ThreadDescriptor and add the sum to
    // the |reference_timestamp| in ThreadDescriptor. This value should always
    // be positive.
    int64 timestamp_delta_us = 1;
    // Absolute value (e.g. a manually specified timestamp in the macro).
    // This is a one-off value that does not affect delta timestamp computation
    // in subsequent TrackEvents.
    int64 timestamp_absolute_us = 16;
  }

  // Deprecated. Use |extra_counter_values| and |extra_counter_track_uuids| to
  // encode thread time instead.
  //
  // CPU time for the current thread (e.g., CLOCK_THREAD_CPUTIME_ID) in
  // microseconds.
  oneof thread_time {
    // Delta timestamp value since the last TrackEvent or ThreadDescriptor. To
    // calculate the absolute timestamp value, sum up all delta values of the
    // preceding TrackEvents since the last ThreadDescriptor and add the sum to
    // the |reference_timestamp| in ThreadDescriptor. This value should always
    // be positive.
    int64 thread_time_delta_us = 2;
    // This is a one-off absolute value that does not affect delta timestamp
    // computation in subsequent TrackEvents.
    int64 thread_time_absolute_us = 17;
  }

  // Deprecated. Use |extra_counter_values| and |extra_counter_track_uuids| to
  // encode thread instruction count instead.
  //
  // Value of the instruction counter for the current thread.
  oneof thread_instruction_count {
    // Same encoding as |thread_time| field above.
    int64 thread_instruction_count_delta = 8;
    int64 thread_instruction_count_absolute = 20;
  }

  // Apart from {category, time, thread time, tid, pid}, other legacy trace
  // event attributes are initially simply proxied for conversion to a JSON
  // trace. We intend to gradually transition these attributes to similar native
  // features in TrackEvent (e.g. async + flow events), or deprecate them
  // without replacement where transition is unsuitable.
  //
  // Next reserved id: 16 (up to 16).
  // Next id: 20.
  message LegacyEvent {
    // Deprecated, use TrackEvent::name(_iid) instead.
    // interned EventName.
    optional uint64 name_iid = 1;
    optional int32 phase = 2;
    optional int64 duration_us = 3;
    optional int64 thread_duration_us = 4;

    // Elapsed retired instruction count during the event.
    optional int64 thread_instruction_delta = 15;

    // used to be |flags|.
    reserved 5;

    oneof id {
      uint64 unscoped_id = 6;
      uint64 local_id = 10;
      uint64 global_id = 11;
    }
    // Additional optional scope for |id|.
    optional string id_scope = 7;

    // Consider the thread timestamps for async BEGIN/END event pairs as valid.
    optional bool use_async_tts = 9;

    // Idenfifies a flow. Flow events with the same bind_id are connected.
    optional uint64 bind_id = 8;
    // Use the enclosing slice as binding point for a flow end event instead of
    // the next slice. Flow start/step events always bind to the enclosing
    // slice.
    optional bool bind_to_enclosing = 12;

    enum FlowDirection {
      FLOW_UNSPECIFIED = 0;
      FLOW_IN = 1;
      FLOW_OUT = 2;
      FLOW_INOUT = 3;
    }
    optional FlowDirection flow_direction = 13;

    enum InstantEventScope {
      SCOPE_UNSPECIFIED = 0;
      SCOPE_GLOBAL = 1;
      SCOPE_PROCESS = 2;
      SCOPE_THREAD = 3;
    }
    optional InstantEventScope instant_event_scope = 14;

    // Override the pid/tid if the writer needs to emit events on behalf of
    // another process/thread. This should be the exception. Normally, the
    // pid+tid from ThreadDescriptor is used.
    optional int32 pid_override = 18;
    optional int32 tid_override = 19;
  }

  optional LegacyEvent legacy_event = 6;
}

// Default values for fields of all TrackEvents on the same packet sequence.
// Should be emitted as part of TracePacketDefaults whenever incremental state
// is cleared. It's defined here because field IDs should match those of the
// corresponding fields in TrackEvent.
message TrackEventDefaults {
  optional uint64 track_uuid = 11;
  repeated uint64 extra_counter_track_uuids = 31;

  // TODO(eseckler): Support default values for more TrackEvent fields.
}

// --------------------
// Interned data types:
// --------------------

message EventCategory {
  optional uint64 iid = 1;
  optional string name = 2;
}

message EventName {
  optional uint64 iid = 1;
  optional string name = 2;
}

// End of protos/perfetto/trace/track_event/track_event.proto

// Begin of protos/perfetto/trace/interned_data/interned_data.proto

// ------------------------------ DATA INTERNING: ------------------------------
// Interning indexes are built up gradually by adding the entries contained in
// each TracePacket of the same packet sequence (packets emitted by the same
// producer and TraceWriter, see |trusted_packet_sequence_id|). Thus, packets
// can only refer to interned data from other packets in the same sequence.
//
// The writer will emit new entries when it encounters new internable values
// that aren't yet in the index. Data in current and subsequent TracePackets can
// then refer to the entry by its position (interning ID, abbreviated "iid") in
// its index. An interning ID with value 0 is considered invalid (not set).
//
// Because of the incremental build-up, the interning index will miss data when
// TracePackets are lost, e.g. because a chunk was overridden in the central
// ring buffer. To avoid invalidation of the whole trace in such a case, the
// index is periodically reset (see SEQ_INCREMENTAL_STATE_CLEARED).
// When packet loss occurs, the reader will only lose interning data up to the
// next reset.
// -----------------------------------------------------------------------------

// Message that contains new entries for the interning indices of a packet
// sequence.
//
// The writer will usually emit new entries in the same TracePacket that first
// refers to them (since the last reset of interning state). They may also be
// emitted proactively in advance of referring to them in later packets.
//
// Next reserved id: 8 (up to 15).
// Next id: 25.
message InternedData {
  // TODO(eseckler): Replace iid fields inside interned messages with
  // map<iid, message> type fields in InternedData.

  // Each field's message type needs to specify an |iid| field, which is the ID
  // of the entry in the field's interning index. Each field constructs its own
  // index, thus interning IDs are scoped to the tracing session and field
  // (usually as a counter for efficient var-int encoding). It is illegal to
  // override entries in an index (using the same iid for two different values)
  // within the same tracing session, even after a reset of the emitted
  // interning state.
  repeated EventCategory event_categories = 1;
  repeated EventName event_names = 2;
  repeated DebugAnnotationName debug_annotation_names = 3;
  repeated SourceLocation source_locations = 4;
  repeated LogMessageBody log_message_body = 20;

  // Note: field IDs up to 15 should be used for frequent data only.

  // Build IDs of exectuable files.
  repeated InternedString build_ids = 16;
  // Paths to executable files.
  repeated InternedString mapping_paths = 17;
  // Paths to source files.
  repeated InternedString source_paths = 18;
  // Names of functions used in frames below.
  repeated InternedString function_names = 5;
  // Symbols that were added to this trace after the fact.
  repeated ProfiledFrameSymbols profiled_frame_symbols = 21;

  // Executable files mapped into processes.
  repeated Mapping mappings = 19;
  // Frames of callstacks of a program.
  repeated Frame frames = 6;
  // A callstack of a program.
  repeated Callstack callstacks = 7;

  // Additional Vulkan information sent in a VulkanMemoryEvent message
  repeated InternedString vulkan_memory_keys = 22;

  // Graphics context of a render stage event.  This represent the GL
  // context for an OpenGl app or the VkDevice for a Vulkan app.
  repeated InternedGraphicsContext graphics_contexts = 23;

  // Description of a GPU hardware queue or render stage.
  repeated InternedGpuRenderStageSpecification gpu_specifications = 24;
}

// End of protos/perfetto/trace/interned_data/interned_data.proto

// Begin of protos/perfetto/trace/memory_graph.proto

// Message definitions for app-reported memory breakdowns. At the moment, this
// is a Chrome-only tracing feature, historically known as 'memory-infra'. See
// https://chromium.googlesource.com/chromium/src/+/master/docs/memory-infra/ .
// This is unrelated to the native or java heap profilers (those protos live
// in //protos/perfetto/trace/profiling/).

message MemoryTrackerSnapshot {
  // Memory snapshot of a process. The snapshot contains memory data that is
  // from 2 different sources, namely system stats and instrumentation stats.
  // The system memory usage stats come from the OS based on standard API
  // available in the platform to query memory usage. The instrumentation stats
  // are added by instrumenting specific piece of code which tracks memory
  // allocations and deallocations made by a small sub-system within the
  // application.
  // The system stats of the global memory snapshot are recorded as part of
  // ProcessStats and SmapsPacket fields in trace packet with the same
  // timestamp.
  message ProcessSnapshot {
    // Process ID of the process
    optional uint64 pid = 1;

    // Memory dumps are represented as a graph of memory nodes which contain
    // statistics. To avoid double counting the same memory across different
    // nodes, edges are used to mark nodes that account for the same memory. See
    // this doc for examples of the usage:
    // https://docs.google.com/document/d/1WGQRJ1sjJrfVkNcgPVY6frm64UqPc94tsxUOXImZUZI

    // A single node in the memory graph.
    message MemoryNode {
      // Unique ID of the node across all processes involved in the global
      // memory dump. The ID is only unique within this particular global dump
      // identified by GlobalMemoryDumpPacket.global_dump_id.
      optional uint64 id = 1;

      // Absolute name is a unique name for the memory node within the process
      // with ProcessMemoryDump.pid. The name can contain multiple parts
      // separated by '/', which traces the edges of the node from the root
      // node.
      // Eg: "partition_allocator/array_buffers/buffer1" refers to the child
      // node "buffer1" in a graph structure of:
      //   root -> partition_allocator -> array_buffers -> buffer1.
      optional string absolute_name = 2;

      // A weak node means that the instrumentation that added the current node
      // is unsure about the existence of the actual memory. Unless a "strong"
      // (non-weak is default) node that has an edge to the current node exists
      // in the current global dump, the current node will be discarded.
      optional bool weak = 3;

      // Size of the node in bytes, used to compute the effective size of the
      // nodes without double counting.
      optional uint64 size_bytes = 4;

      // Entries in the memory node that contain statistics and additional
      // debuggable information about the memory. The size of the node is
      // tracked separately in the |size_bytes| field.
      message MemoryNodeEntry {
        optional string name = 1;

        enum Units {
          UNSPECIFIED = 0;
          BYTES = 1;
          COUNT = 2;
        }
        optional Units units = 2;

        // Contains either one of uint64 or string value.
        optional uint64 value_uint64 = 3;
        optional string value_string = 4;
      }
      repeated MemoryNodeEntry entries = 5;
    }
    repeated MemoryNode allocator_dumps = 2;

    // A directed edge that connects any 2 nodes in the graph above. These are
    // in addition to the inherent edges added due to the tree structure of the
    // node's absolute names.
    // Node with id |source_id| owns the node with id |target_id|, and has the
    // effect of attributing the memory usage of target to source. |importance|
    // is optional and relevant only for the cases of co-ownership, where it
    // acts as a z-index: the owner with the highest importance will be
    // attributed target's memory.
    message MemoryEdge {
      optional uint64 source_id = 1;
      optional uint64 target_id = 2;
      optional uint32 importance = 3;
      optional bool overridable = 4;
    }
    repeated MemoryEdge memory_edges = 3;
  }

  // Unique ID that represents the global memory dump.
  optional uint64 global_dump_id = 1;

  enum LevelOfDetail {
    DETAIL_FULL = 0;
    DETAIL_LIGHT = 1;
    DETAIL_BACKGROUND = 2;
  }
  optional LevelOfDetail level_of_detail = 2;

  repeated ProcessSnapshot process_memory_dumps = 3;
}

// End of protos/perfetto/trace/memory_graph.proto

// Begin of protos/perfetto/trace/perfetto/perfetto_metatrace.proto

// Used to trace the execution of perfetto itself.
message PerfettoMetatrace {
  // See base/metatrace_events.h for definitions.
  oneof record_type {
    uint32 event_id = 1;
    uint32 counter_id = 2;

    // For trace processor metatracing.
    string event_name = 8;
    string counter_name = 9;
  }
  message Arg {
    optional string key = 1;
    optional string value = 2;
  }

  // Only when using |event_id|.
  optional uint32 event_duration_ns = 3;

  // Only when using |counter_id|.
  optional int32 counter_value = 4;

  // ID of the thread that emitted the event.
  optional uint32 thread_id = 5;

  // If true the meta-tracing ring buffer had overruns and hence some data is
  // missing from this point.
  optional bool has_overruns = 6;

  // Args for the event.
  repeated Arg args = 7;
}

// End of protos/perfetto/trace/perfetto/perfetto_metatrace.proto

// Begin of protos/perfetto/trace/perfetto/tracing_service_event.proto

// Events emitted by the tracing service.
message TracingServiceEvent {
  oneof event_type {
    // When each of the following booleans are set to true, they report the
    // point in time (through TracePacket's timestamp) where the condition
    // they describe happened.
    // The order of the booleans below matches the timestamp ordering
    // they would generally be expected to have.

    // Emitted when we start tracing and specifically, this will be before any
    // producer is notified about the existence of this trace. This is always
    // emitted before the all_data_sources_started event. This event is also
    // guaranteed to be seen (byte-offset wise) before any data packets from
    // producers.
    bool tracing_started = 2;

    // Emitted after all data sources saw the start event and ACKed it.
    // This identifies the point in time when it's safe to assume that all data
    // sources have been recording events.
    bool all_data_sources_started = 1;

    // Emitted when all data sources have been flushed successfully or with an
    // error (including timeouts). This can generally happen many times over the
    // course of the trace.
    bool all_data_sources_flushed = 3;

    // Emitted when reading back the central tracing buffers has been completed.
    // If |write_into_file| is specified, this can happen many times over the
    // course of the trace.
    bool read_tracing_buffers_completed = 4;

    // Emitted after tracing has been disabled and specifically, this will be
    // after all packets from producers have been included in the central
    // tracing buffer.
    bool tracing_disabled = 5;
  }
}

// End of protos/perfetto/trace/perfetto/tracing_service_event.proto

// Begin of protos/perfetto/trace/power/battery_counters.proto

message BatteryCounters {
  // Battery capacity in microampere-hours(µAh). Also known as Coulomb counter.
  optional int64 charge_counter_uah = 1;

  // Remaining battery capacity percentage of total capacity
  optional float capacity_percent = 2;

  // Instantaneous battery current in microamperes(µA).
  // Positive values indicate net current entering the battery from a charge
  // source, negative values indicate net current discharging from the battery.
  optional int64 current_ua = 3;

  // Instantaneous battery current in microamperes(µA).
  optional int64 current_avg_ua = 4;
}

// End of protos/perfetto/trace/power/battery_counters.proto

// Begin of protos/perfetto/trace/power/power_rails.proto

message PowerRails {

  message RailDescriptor {
    // Index corresponding to the rail
    optional uint32 index = 1;

    // Name of the rail
    optional string rail_name = 2;

    // Name of the subsystem to which this rail belongs
    optional string subsys_name = 3;

    // Hardware sampling rate (Hz).
    optional uint32 sampling_rate = 4;
  }

  // This is only emitted at the beginning of the trace.
  repeated RailDescriptor rail_descriptor = 1;

  message EnergyData {
    // Index corresponding to RailDescriptor.index
    optional uint32 index = 1;

    // Time since device boot(CLOCK_BOOTTIME) in milli-seconds.
    optional uint64 timestamp_ms = 2;

    // Accumulated energy since device boot in microwatt-seconds (uWs).
    optional uint64 energy = 3;
  }

  repeated EnergyData energy_data = 2;
}

// End of protos/perfetto/trace/power/power_rails.proto

// Begin of protos/perfetto/trace/profiling/deobfuscation.proto

message ObfuscatedMember {
  // This is the obfuscated field name relative to the class containing the
  // ObfuscatedMember.
  optional string obfuscated_name = 1;
  // If this is fully qualified (i.e. contains a '.') this is the deobfuscated
  // field name including its class. Otherwise, this is this the unqualified
  // deobfuscated field name relative to the class containing this
  // ObfuscatedMember.
  optional string deobfuscated_name = 2;
}

message ObfuscatedClass {
  optional string obfuscated_name = 1;
  optional string deobfuscated_name = 2;
  // fields.
  repeated ObfuscatedMember obfuscated_members = 3;
  repeated ObfuscatedMember obfuscated_methods = 4;
}

message DeobfuscationMapping {
  optional string package_name = 1;
  optional int64 version_code = 2;
  repeated ObfuscatedClass obfuscated_classes = 3;
}
// End of protos/perfetto/trace/profiling/deobfuscation.proto

// Begin of protos/perfetto/trace/profiling/heap_graph.proto

message HeapGraphRoot {
  enum Type {
    ROOT_UNKNOWN = 0;
    ROOT_JNI_GLOBAL = 1;
    ROOT_JNI_LOCAL = 2;
    ROOT_JAVA_FRAME = 3;
    ROOT_NATIVE_STACK = 4;
    ROOT_STICKY_CLASS = 5;
    ROOT_THREAD_BLOCK = 6;
    ROOT_MONITOR_USED = 7;
    ROOT_THREAD_OBJECT = 8;
    ROOT_INTERNED_STRING = 9;
    ROOT_FINALIZING = 10;
    ROOT_DEBUGGER = 11;
    ROOT_REFERENCE_CLEANUP = 12;
    ROOT_VM_INTERNAL = 13;
    ROOT_JNI_MONITOR = 14;
  };
  // Objects retained by this root.
  repeated uint64 object_ids = 1 [packed = true];

  optional Type root_type = 2;
}

message HeapGraphType {
  enum Kind {
    KIND_UNKNOWN = 0;
    KIND_NORMAL = 1;
    KIND_NOREFERENCES = 2;
    KIND_STRING = 3;
    KIND_ARRAY = 4;
    KIND_CLASS = 5;
    KIND_CLASSLOADER = 6;
    KIND_DEXCACHE = 7;
    KIND_SOFT_REFERENCE = 8;
    KIND_WEAK_REFERENCE = 9;
    KIND_FINALIZER_REFERENCE = 10;
    KIND_PHANTOM_REFERENCE = 11;
  };
  // TODO(fmayer): Consider removing this and using the index in the repeaed
  // field to save space.
  optional uint64 id = 1;
  optional uint64 location_id = 2;
  optional string class_name = 3;
  // Size of objects of this type.
  optional uint64 object_size = 4;
  optional uint64 superclass_id = 5;
  // Indices for InternedData.field_names for the names of the fields of
  // instances of this class. This does NOT include the fields from
  // superclasses. The consumer of this data needs to walk all super
  // classes to get a full lists of fields. Objects always write the
  // fields in order of most specific class to the furthest up superclass.
  repeated uint64 reference_field_id = 6 [packed = true];
  optional Kind kind = 7;
}

message HeapGraphObject {
  optional uint64 id = 1;

  // Index for InternedData.types for the name of the type of this object.
  optional uint64 type_id = 2;

  // Bytes occupied by this objects.
  optional uint64 self_size = 3;

  // Indices for InternedData.field_names for the name of the field referring
  // to the object. For Android S+ and for instances of normal classes (e.g.
  // not instances of java.lang.Class or arrays), this is instead set in the
  // corresponding HeapGraphType, and this is left empty.
  repeated uint64 reference_field_id = 4 [packed = true];

  // Ids of the Object that is referred to.
  repeated uint64 reference_object_id = 5 [packed = true];
}

message HeapGraph {
  optional int32 pid = 1;

  // This contains all objects at the time this dump was taken. Some of these
  // will be live, some of those unreachable (garbage). To find the live
  // objects, the client needs to build the transitive closure of objects
  // reachable from |roots|.
  // All objects not contained within that transitive closure are garbage that
  // has not yet been collected.
  repeated HeapGraphObject objects = 2;

  // Roots at the time this dump was taken.
  // All live objects are reachable from the roots. All other objects are
  // garbage.
  repeated HeapGraphRoot roots = 7;

  // Types used in HeapGraphObjects.
  repeated HeapGraphType types = 9;

  reserved 3;

  // Field names for references in managed heap graph.
  repeated InternedString field_names = 4;

  // Paths of files used in managed heap graph.
  repeated InternedString location_names = 8;

  optional bool continued = 5;
  optional uint64 index = 6;
}

// End of protos/perfetto/trace/profiling/heap_graph.proto

// Begin of protos/perfetto/trace/profiling/profile_packet.proto

message ProfilePacket {
  // The following interning tables are only used in Android version Q.
  // In newer versions, these tables are in InternedData
  // (see protos/perfetto/trace/interned_data) and are shared across
  // multiple ProfilePackets.
  // For backwards compatibility, consumers need to first look up interned
  // data in the tables within the ProfilePacket, and then, if they are empty,
  // look up in the InternedData instead.
  repeated InternedString strings = 1;
  repeated Mapping mappings = 4;
  repeated Frame frames = 2;
  repeated Callstack callstacks = 3;

  // Next ID: 9
  message HeapSample {
    optional uint64 callstack_id = 1;
    // bytes allocated at this callstack.
    optional uint64 self_allocated = 2;
    // bytes allocated at this callstack that have been freed.
    optional uint64 self_freed = 3;
    // bytes allocated at this callstack but not used since the last
    // dump.
    // See documentation of idle_allocations in HeapprofdConfig for more
    // details.
    optional uint64 self_idle = 7;
    // Bytes allocated by this callstack but not freed at the time the malloc
    // heap usage of this process was maximal. This is only set if dump_at_max
    // is true in HeapprofdConfig. In that case, self_allocated, self_freed and
    // self_idle will not be set.
    optional uint64 self_max = 8;
    // Number of allocations that were sampled at this callstack but not freed
    // at the time the malloc heap usage of this process was maximal. This is
    // only set if dump_at_max is true in HeapprofdConfig. In that case,
    // self_allocated, self_freed and self_idle will not be set.
    optional uint64 self_max_count = 9;
    // timestamp [opt]
    optional uint64 timestamp = 4;
    // Number of allocations that were sampled at this callstack.
    optional uint64 alloc_count = 5;
    // Number of allocations that were sampled at this callstack that have been
    // freed.
    optional uint64 free_count = 6;
  }

  message Histogram {
    message Bucket {
      // This bucket counts values from the previous bucket's (or -infinity if
      // this is the first bucket) upper_limit (inclusive) to this upper_limit
      // (exclusive).
      optional uint64 upper_limit = 1;
      // This is the highest bucket. This is set instead of the upper_limit. Any
      // values larger or equal to the previous bucket's upper_limit are counted
      // in this bucket.
      optional bool max_bucket = 2;
      // Number of values that fall into this range.
      optional uint64 count = 3;
    }
    repeated Bucket buckets = 1;
  }

  message ProcessStats {
    optional uint64 unwinding_errors = 1;
    optional uint64 heap_samples = 2;
    optional uint64 map_reparses = 3;
    optional Histogram unwinding_time_us = 4;
    optional uint64 total_unwinding_time_us = 5;
    optional uint64 client_spinlock_blocked_us = 6;
  }

  repeated ProcessHeapSamples process_dumps = 5;
  message ProcessHeapSamples {
    optional uint64 pid = 1;

    // This process was profiled from startup.
    // If false, this process was already running when profiling started.
    optional bool from_startup = 3;

    // This process was not profiled because a concurrent session was active.
    // If this is true, samples will be empty.
    optional bool rejected_concurrent = 4;

    // This process disconnected while it was profiled.
    // If false, the process outlived the profiling session.
    optional bool disconnected = 6;

    // If disconnected, this disconnect was caused by the client overrunning
    // the buffer.
    optional bool buffer_overran = 7;

    // If disconnected, this disconnected was caused by the shared memory
    // buffer being corrupted. THIS IS ALWAYS A BUG IN HEAPPROFD OR CLIENT
    // MEMORY CORRUPTION.
    optional bool buffer_corrupted = 8;

    // If disconnected, this disconnect was caused by heapprofd exceeding
    // guardrails during this profiling session.
    optional bool hit_guardrail = 10;

    optional string heap_name = 11;

    // Timestamp of the state of the target process that this dump represents.
    // This can be different to the timestamp of the TracePackets for various
    // reasons:
    // * If disconnected is set above, this is the timestamp of last state
    //   heapprofd had of the process before it disconnected.
    // * Otherwise, if the rate of events produced by the process is high,
    //   heapprofd might be behind.
    //
    // TODO(fmayer): This is MONOTONIC_COARSE. Refactor ClockSnapshot::Clock
    //               to have a type enum that we can reuse here.
    optional uint64 timestamp = 9;

    // Metadata about heapprofd.
    optional ProcessStats stats = 5;

    repeated HeapSample samples = 2;
  }

  // If this is true, the next ProfilePacket in this package_sequence_id is a
  // continuation of this one.
  // To get all samples for a process, accummulate its
  // ProcessHeapSamples.samples until you see continued=false.
  optional bool continued = 6;

  // Index of this ProfilePacket on its package_sequence_id. Can be used
  // to detect dropped data.
  // Verify these are consecutive.
  optional uint64 index = 7;
}

// Message used to represent individual stack samples sampled at discrete
// points in time, rather than aggregated over an interval.
message StreamingProfilePacket {
  // Index into InternedData.callstacks
  repeated uint64 callstack_iid = 1;
  // TODO(eseckler): ThreadDescriptor-based timestamps are deprecated. Replace
  // this with ClockSnapshot-based delta encoding instead.
  repeated int64 timestamp_delta_us = 2;
  optional int32 process_priority = 3;
}

// Namespace for the contained enums.
message Profiling {
  enum CpuMode {
    MODE_UNKNOWN = 0;
    MODE_KERNEL = 1;
    MODE_USER = 2;
    // The following values aren't expected, but included for completeness:
    MODE_HYPERVISOR = 3;
    MODE_GUEST_KERNEL = 4;
    MODE_GUEST_USER = 5;
  }

  // Enumeration of libunwindstack's error codes.
  // NB: the integral representations of the two enums are different.
  enum StackUnwindError {
    UNWIND_ERROR_UNKNOWN = 0;
    UNWIND_ERROR_NONE = 1;
    UNWIND_ERROR_MEMORY_INVALID = 2;
    UNWIND_ERROR_UNWIND_INFO = 3;
    UNWIND_ERROR_UNSUPPORTED = 4;
    UNWIND_ERROR_INVALID_MAP = 5;
    UNWIND_ERROR_MAX_FRAMES_EXCEEDED = 6;
    UNWIND_ERROR_REPEATED_FRAME = 7;
    UNWIND_ERROR_INVALID_ELF = 8;
  }
}

// Individual performance sampling packet payload. Typically corresponds to a
// stack sample on a configration-dependent counter overflow.
// Timestamps are within the root packet (in the CLOCK_BOOTTIME domain).
// There are three distinct views of this message:
// * completely processed sample (callstack_iid set)
// * indication of kernel buffer data loss (kernel_records_lost set)
// * indication of skipped samples (sample_skipped_reason set)
message PerfSample {
  optional uint32 cpu = 1;
  optional uint32 pid = 2;
  optional uint32 tid = 3;

  // Execution state that the process was sampled at.
  optional Profiling.CpuMode cpu_mode = 5;

  // Unwound callstack. Might be partial, in which case a synthetic "error"
  // frame is appended, and |unwind_error| is set appropriately.
  optional uint64 callstack_iid = 4;

  // If set, stack unwinding was incomplete due to an error.
  // Unset values should be treated as UNWIND_ERROR_NONE.
  oneof optional_unwind_error { Profiling.StackUnwindError unwind_error = 16; };

  // If set, indicates that this message is not a sample, but rather an
  // indication of data loss in the ring buffer allocated for |cpu|. Such data
  // loss occurs when the kernel has insufficient ring buffer capacity to write
  // a record (which gets discarded). A record in this context is an individual
  // ring buffer entry, and counts more than just sample records.
  //
  // The |timestamp| of the packet corresponds to the time that the producer
  // wrote the packet for trace-sorting purposes alone, and should not be
  // interpreted relative to the sample timestamps. This field is sufficient to
  // detect that *some* kernel data loss happened within the trace, but not the
  // specific time bounds of that loss (which would require tracking precedessor
  // & successor timestamps, which is not deemed necessary at the moment).
  optional uint64 kernel_records_lost = 17;

  // If set, indicates that the profiler encountered a sample that was relevant,
  // but was skipped.
  enum SampleSkipReason {
    PROFILER_SKIP_UNKNOWN = 0;
    PROFILER_SKIP_READ_STAGE = 1;
    PROFILER_SKIP_UNWIND_STAGE = 2;
    PROFILER_SKIP_UNWIND_ENQUEUE = 3;
  }
  oneof optional_sample_skipped_reason {
    SampleSkipReason sample_skipped_reason = 18;
  };
}

// End of protos/perfetto/trace/profiling/profile_packet.proto

// Begin of protos/perfetto/trace/profiling/smaps.proto

message SmapsEntry {
  optional string path = 1;
  optional uint64 size_kb = 2;
  optional uint64 private_dirty_kb = 3;
  optional uint64 swap_kb = 4;

  // for field upload (instead of path).
  optional string file_name = 5;

  // TODO(crbug.com/1098746): Consider encoding this as incremental values.
  optional uint64 start_address = 6;
  optional uint64 module_timestamp = 7;
  optional string module_debugid = 8;
  optional string module_debug_path = 9;
  optional uint32 protection_flags = 10;

  optional uint64 private_clean_resident_kb = 11;
  optional uint64 shared_dirty_resident_kb = 12;
  optional uint64 shared_clean_resident_kb = 13;
  optional uint64 locked_kb = 14;
  optional uint64 proportional_resident_kb = 15;
};

message SmapsPacket {
  optional uint32 pid = 1;
  repeated SmapsEntry entries = 2;
};

// End of protos/perfetto/trace/profiling/smaps.proto

// Begin of protos/perfetto/trace/ps/process_stats.proto

// Per-process periodically sampled stats. These samples are wrapped in a
// dedicated message (as opposite to be fields in process_tree.proto) because
// they are dumped at a different rate than cmdline and thread list.
// Note: not all of these stats will be present in every ProcessStats message
// and sometimes processes may be missing . This is because counters are
// cached to reduce emission of counters which do not change.
message ProcessStats {
  // Per-thread periodically sampled stats.
  // Note: not all of these stats will be present in every message. See the note
  // for ProcessStats.
  message Thread {
    optional int32 tid = 1;

    // Pairs of frequency (represented as a (1-based) index to CpuInfo
    // frequencies) and time at that frequency (represented as a number of
    // ticks, see SystemInfo for the HZ (ticks / second) value to convert this
    // to time). Frequencies with zero ticks are never uploaded. Read from
    // /proc/tid/time_in_state.
    repeated uint32 cpu_freq_indices = 2;
    repeated uint64 cpu_freq_ticks = 3;
    // Whether all frequencies with non-zero ticks are present in
    // `cpu_freq_indices`. This marker is used to detect packets that describe
    // time_in_state exhaustively.
    optional bool cpu_freq_full = 4;
  }

  message Process {
    optional int32 pid = 1;

    // See /proc/[pid]/status in `man 5 proc` for a description of these fields.
    optional uint64 vm_size_kb = 2;
    optional uint64 vm_rss_kb = 3;
    optional uint64 rss_anon_kb = 4;
    optional uint64 rss_file_kb = 5;
    optional uint64 rss_shmem_kb = 6;
    optional uint64 vm_swap_kb = 7;
    optional uint64 vm_locked_kb = 8;
    optional uint64 vm_hwm_kb = 9;
    // When adding a new field remember to update kProcMemCounterSize in
    // the trace processor.

    optional int64 oom_score_adj = 10;

    repeated Thread threads = 11;

    // The peak resident set size is resettable in newer Posix kernels.
    // This field specifies if reset is supported and if the writer had reset
    // the peaks after each process stats recording.
    optional bool is_peak_rss_resettable = 12;

    // Private, shared and swap footprint of the process as measured by
    // Chrome. To know more about these metrics refer to:
    // https://docs.google.com/document/d/1_WmgE1F5WUrhwkPqJis3dWyOiUmQKvpXp5cd4w86TvA
    optional uint32 chrome_private_footprint_kb = 13;
    optional uint32 chrome_peak_resident_set_kb = 14;
  }
  repeated Process processes = 1;

  // The time at which we finish collecting this batch of samples;
  // the top-level packet timestamp is the time at which
  // we begin collection.
  // TODO(dancol): analysis might be improved by
  // time-bracketing each sample as well as the whole
  // ProcessStats, but doing that is probably gated on
  // a vdso for CLOCK_BOOTTIME.
  optional uint64 collection_end_timestamp = 2;
}

// End of protos/perfetto/trace/ps/process_stats.proto

// Begin of protos/perfetto/trace/ps/process_tree.proto

// Metadata about the processes and threads in the trace.
// Note: this proto was designed to be filled in by traced_probes and should
// only be populated with accurate information coming from the system. Other
// trace writers should prefer to fill ThreadDescriptor and ProcessDescriptor
// in TrackDescriptor.
message ProcessTree {
  // Representation of a thread.
  message Thread {
    // The thread id (as per gettid())
    optional int32 tid = 1;

    // Thread group id (i.e. the PID of the process, == TID of the main thread)
    optional int32 tgid = 3;

    // The name of the thread.
    optional string name = 2;
  }

  // Representation of a process.
  message Process {
    // The UNIX process ID, aka thread group ID (as per getpid()).
    optional int32 pid = 1;

    // The parent process ID, as per getppid().
    optional int32 ppid = 2;

    // The command line for the process, as per /proc/pid/cmdline.
    // If it is a kernel thread there will only be one cmdline field
    // and it will contain /proc/pid/comm.
    repeated string cmdline = 3;

    // No longer used as of Apr 2018, when the dedicated |threads| field was
    // introduced in ProcessTree.
    repeated Thread threads_deprecated = 4 [deprecated = true];

    // The uid for the process, as per /proc/pid/status.
    optional int32 uid = 5;
  }

  // List of processes and threads in the client. These lists are incremental
  // and not exhaustive. A process and its threads might show up separately in
  // different ProcessTree messages. A thread might event not show up at all, if
  // no sched_switch activity was detected, for instance:
  // #0 { processes: [{pid: 10, ...}], threads: [{pid: 11, tgid: 10}] }
  // #1 { threads: [{pid: 12, tgid: 10}] }
  // #2 { processes: [{pid: 20, ...}], threads: [{pid: 13, tgid: 10}] }
  repeated Process processes = 1;
  repeated Thread threads = 2;

  // The time at which we finish collecting this process tree;
  // the top-level packet timestamp is the time at which
  // we begin collection.
  optional uint64 collection_end_timestamp = 3;
}

// End of protos/perfetto/trace/ps/process_tree.proto

// Begin of protos/perfetto/trace/sys_stats/sys_stats.proto

// Various Linux system stat counters from /proc.
// The fields in this message can be reported at different rates and with
// different granularity. See sys_stats_config.proto.
message SysStats {
  // Counters from /proc/meminfo. Values are in KB.
  message MeminfoValue {
    optional MeminfoCounters key = 1;
    optional uint64 value = 2;
  };
  repeated MeminfoValue meminfo = 1;

  // Counter from /proc/vmstat. Units are often pages, not KB.
  message VmstatValue {
    optional VmstatCounters key = 1;
    optional uint64 value = 2;
  };
  repeated VmstatValue vmstat = 2;

  // Times in each mode, since boot. Unit: nanoseconds.
  message CpuTimes {
    optional uint32 cpu_id = 1;

    // Time spent in user mode.
    optional uint64 user_ns = 2;

    // Time spent in user mode (low prio).
    optional uint64 user_ice_ns = 3;

    // Time spent in system mode.
    optional uint64 system_mode_ns = 4;

    // Time spent in the idle task.
    optional uint64 idle_ns = 5;

    // Time spent waiting for I/O.
    optional uint64 io_wait_ns = 6;

    // Time spent servicing interrupts.
    optional uint64 irq_ns = 7;

    // Time spent servicing softirqs.
    optional uint64 softirq_ns = 8;
  }
  // One entry per cpu.
  repeated CpuTimes cpu_stat = 3;

  // Num processes forked since boot.
  // Populated only if FORK_COUNT in config.stat_counters.
  optional uint64 num_forks = 4;

  message InterruptCount {
    optional int32 irq = 1;
    optional uint64 count = 2;
  }

  // Number of interrupts, broken by IRQ number.
  // Populated only if IRQ_COUNTS in config.stat_counters.

  // Total num of irqs serviced since boot.
  optional uint64 num_irq_total = 5;
  repeated InterruptCount num_irq = 6;

  // Number of softirqs, broken by softirq number.
  // Populated only if SOFTIRQ_COUNTS in config.stat_counters.

  // Total num of softirqs since boot.
  optional uint64 num_softirq_total = 7;

  // Per-softirq count.
  repeated InterruptCount num_softirq = 8;

  // The time at which we finish collecting this set of samples;
  // the top-level packet timestamp is the time at which
  // we begin collection.
  optional uint64 collection_end_timestamp = 9;
}

// End of protos/perfetto/trace/sys_stats/sys_stats.proto

// Begin of protos/perfetto/trace/system_info.proto

message Utsname {
  optional string sysname = 1;
  optional string version = 2;
  optional string release = 3;
  optional string machine = 4;
}

message SystemInfo {
  optional Utsname utsname = 1;
  optional string android_build_fingerprint = 2;

  // Ticks per second - sysconf(_SC_CLK_TCK).
  optional int64 hz = 3;
}

// End of protos/perfetto/trace/system_info.proto

// Begin of protos/perfetto/trace/system_info/cpu_info.proto

// Information about CPUs from procfs and sysfs.
message CpuInfo {
  // Information about a single CPU.
  message Cpu {
    // Value of "Processor" field from /proc/cpuinfo for this CPU.
    // Example: "AArch64 Processor rev 12 (aarch64)"
    optional string processor = 1;

    // Frequencies from
    // /sys/devices/system/cpu/cpuX/cpufreq/scaling_available_frequencies
    // where X is the index of this CPU.
    repeated uint32 frequencies = 2;
  }

  // Describes available CPUs, one entry per CPU.
  repeated Cpu cpus = 1;
}

// End of protos/perfetto/trace/system_info/cpu_info.proto

// Begin of protos/perfetto/trace/test_event.proto

// Event used by testing code.
message TestEvent {
  // Arbitrary string used in tests.
  optional string str = 1;

  // The current value of the random number sequence used in tests.
  optional uint32 seq_value = 2;

  // Monotonically increased on each packet.
  optional uint64 counter = 3;

  // No more packets should follow (from the current sequence).
  optional bool is_last = 4;

  message TestPayload {
    repeated string str = 1;
    repeated TestPayload nested = 2;

    // When 0 this is the bottom-most nested message.
    optional uint32 remaining_nesting_depth = 3;
  }
  optional TestPayload payload = 5;
}

// End of protos/perfetto/trace/test_event.proto

// Begin of protos/perfetto/trace/trace_packet_defaults.proto

// Default values for TracePacket fields that hold for a particular TraceWriter
// packet sequence. This message contains a subset of the TracePacket fields
// with matching IDs. When provided, these fields define the default values
// that should be applied, at import time, to all TracePacket(s) with the same
// |trusted_packet_sequence_id|, unless otherwise specified in each packet.
//
// Should be reemitted whenever incremental state is cleared on the sequence.
message TracePacketDefaults {
  optional uint32 timestamp_clock_id = 58;

  // Default values for TrackEvents (e.g. default track).
  optional TrackEventDefaults track_event_defaults = 11;
}
// End of protos/perfetto/trace/trace_packet_defaults.proto

// Begin of protos/perfetto/trace/track_event/process_descriptor.proto

// Describes a process's attributes. Emitted as part of a TrackDescriptor,
// usually by the process's main thread.
//
// Next id: 7.
message ProcessDescriptor {
  optional int32 pid = 1;
  repeated string cmdline = 2;
  optional string process_name = 6;

  optional int32 process_priority = 5;

  // ---------------------------------------------------------------------------
  // Deprecated / legacy fields, which will be removed in the future:
  // ---------------------------------------------------------------------------

  // See chromium's content::ProcessType.
  enum ChromeProcessType {
    PROCESS_UNSPECIFIED = 0;
    PROCESS_BROWSER = 1;
    PROCESS_RENDERER = 2;
    PROCESS_UTILITY = 3;
    PROCESS_ZYGOTE = 4;
    PROCESS_SANDBOX_HELPER = 5;
    PROCESS_GPU = 6;
    PROCESS_PPAPI_PLUGIN = 7;
    PROCESS_PPAPI_BROKER = 8;
  }
  optional ChromeProcessType chrome_process_type = 4;

  // To support old UI. New UI should determine default sorting by process_type.
  optional int32 legacy_sort_index = 3;
}

// End of protos/perfetto/trace/track_event/process_descriptor.proto

// Begin of protos/perfetto/trace/track_event/thread_descriptor.proto

// Describes a thread's attributes. Emitted as part of a TrackDescriptor,
// usually by the thread's trace writer.
//
// Next id: 9.
message ThreadDescriptor {
  optional int32 pid = 1;
  optional int32 tid = 2;

  optional string thread_name = 5;

  // ---------------------------------------------------------------------------
  // Deprecated / legacy fields, which will be removed in the future:
  // ---------------------------------------------------------------------------

  enum ChromeThreadType {
    CHROME_THREAD_UNSPECIFIED = 0;

    CHROME_THREAD_MAIN = 1;
    CHROME_THREAD_IO = 2;

    // Scheduler:
    CHROME_THREAD_POOL_BG_WORKER = 3;
    CHROME_THREAD_POOL_FG_WORKER = 4;
    CHROME_THREAD_POOL_FB_BLOCKING = 5;
    CHROME_THREAD_POOL_BG_BLOCKING = 6;
    CHROME_THREAD_POOL_SERVICE = 7;

    // Compositor:
    CHROME_THREAD_COMPOSITOR = 8;
    CHROME_THREAD_VIZ_COMPOSITOR = 9;
    CHROME_THREAD_COMPOSITOR_WORKER = 10;

    // Renderer:
    CHROME_THREAD_SERVICE_WORKER = 11;

    // Tracing related threads:
    CHROME_THREAD_MEMORY_INFRA = 50;
    CHROME_THREAD_SAMPLING_PROFILER = 51;
  };
  optional ChromeThreadType chrome_thread_type = 4;

  // Deprecated. Use ClockSnapshot in combination with TracePacket's timestamp
  // and timestamp_clock_id fields instead.
  optional int64 reference_timestamp_us = 6;

  // Absolute reference values. Clock values in subsequent TrackEvents can be
  // encoded accumulatively and relative to these. This reduces their var-int
  // encoding size.
  // TODO(eseckler): Deprecated. Replace these with ClockSnapshot encoding.
  optional int64 reference_thread_time_us = 7;
  optional int64 reference_thread_instruction_count = 8;

  // To support old UI. New UI should determine default sorting by thread_type.
  optional int32 legacy_sort_index = 3;
}

// End of protos/perfetto/trace/track_event/thread_descriptor.proto

// Begin of protos/perfetto/trace/track_event/chrome_process_descriptor.proto

// Describes the attributes for a Chrome process. Must be paired with a
// ProcessDescriptor in the same TrackDescriptor.
//
// Next id: 4.
message ChromeProcessDescriptor {
  // See chromium's content::ProcessType.
  enum ProcessType {
    PROCESS_UNSPECIFIED = 0;
    PROCESS_BROWSER = 1;
    PROCESS_RENDERER = 2;
    PROCESS_UTILITY = 3;
    PROCESS_ZYGOTE = 4;
    PROCESS_SANDBOX_HELPER = 5;
    PROCESS_GPU = 6;
    PROCESS_PPAPI_PLUGIN = 7;
    PROCESS_PPAPI_BROKER = 8;
  }
  optional ProcessType process_type = 1;
  optional int32 process_priority = 2;

  // To support old UI. New UI should determine default sorting by process_type.
  optional int32 legacy_sort_index = 3;

  // Name of the hosting app for WebView. Used to match renderer processes to
  // their hosting apps.
  optional string host_app_package_name = 4;
}

// End of protos/perfetto/trace/track_event/chrome_process_descriptor.proto

// Begin of protos/perfetto/trace/track_event/chrome_thread_descriptor.proto

// Describes a Chrome thread's attributes. Emitted as part of a TrackDescriptor,
// usually by the thread's trace writer. Must be paired with a ThreadDescriptor
// in the same TrackDescriptor.
//
// Next id: 3.
message ChromeThreadDescriptor {
  enum ThreadType {
    THREAD_UNSPECIFIED = 0;

    THREAD_MAIN = 1;
    THREAD_IO = 2;

    // Scheduler:
    THREAD_POOL_BG_WORKER = 3;
    THREAD_POOL_FG_WORKER = 4;
    THREAD_POOL_BG_BLOCKING = 6;
    THREAD_POOL_FG_BLOCKING = 5;
    THREAD_POOL_SERVICE = 7;

    // Compositor:
    THREAD_COMPOSITOR = 8;
    THREAD_VIZ_COMPOSITOR = 9;
    THREAD_COMPOSITOR_WORKER = 10;

    // Renderer:
    THREAD_SERVICE_WORKER = 11;

    // Tracing related threads:
    THREAD_MEMORY_INFRA = 50;
    THREAD_SAMPLING_PROFILER = 51;
  };
  optional ThreadType thread_type = 1;

  // To support old UI. New UI should determine default sorting by thread_type.
  optional int32 legacy_sort_index = 2;
}

// End of protos/perfetto/trace/track_event/chrome_thread_descriptor.proto

// Begin of protos/perfetto/trace/track_event/counter_descriptor.proto

// Defines properties of a counter track, e.g. for built-in counters (thread
// time, instruction count, ..) or user-specified counters (e.g. memory usage of
// a specific app component).
//
// Counter tracks only support TYPE_COUNTER track events, which specify new
// values for the counter. For counters that require per-slice values, counter
// values can instead be provided in a more efficient encoding via TrackEvent's
// |extra_counter_track_uuids| and |extra_counter_values| fields. However,
// slice-type events cannot be emitted onto a counter track.
//
// Values for counters that are only emitted on a single packet sequence can
// optionally be delta-encoded, see |is_incremental|.
//
// Next id: 6.
message CounterDescriptor {
  // Built-in counters, usually with special meaning in the client library,
  // trace processor, legacy JSON format, or UI. Trace processor will infer a
  // track name from the enum value if none is provided in TrackDescriptor.
  enum BuiltinCounterType {
    COUNTER_UNSPECIFIED = 0;

    // Thread-scoped counters. The thread's track should be specified via
    // |parent_uuid| in the TrackDescriptor for such a counter.

    // implies UNIT_TIME_NS.
    COUNTER_THREAD_TIME_NS = 1;

    // implies UNIT_COUNT.
    COUNTER_THREAD_INSTRUCTION_COUNT = 2;
  }

  // Type of the values for the counters - to supply lower granularity units,
  // see also |unit_multiplier|.
  enum Unit {
    UNIT_UNSPECIFIED = 0;
    UNIT_TIME_NS = 1;
    UNIT_COUNT = 2;
    UNIT_SIZE_BYTES = 3;
    // TODO(eseckler): Support more units as necessary.
  }

  // For built-in counters (e.g. thread time). Custom user-specified counters
  // (e.g. those emitted by TRACE_COUNTER macros of the client library)
  // shouldn't set this, and instead provide a counter name via TrackDescriptor.
  optional BuiltinCounterType type = 1;

  // Names of categories of the counter (usually for user-specified counters).
  // In the client library, categories are a way to turn groups of individual
  // counters (or events) on or off.
  repeated string categories = 2;

  // Type of the counter's values. Built-in counters imply a value for this
  // field.
  optional Unit unit = 3;

  // Multiplication factor of this counter's values, e.g. to supply
  // COUNTER_THREAD_TIME_NS timestamps in microseconds instead.
  optional int64 unit_multiplier = 4;

  // Whether values for this counter are provided as delta values. Only
  // supported for counters that are emitted on a single packet-sequence (e.g.
  // thread time). Counter values in subsequent packets on the current packet
  // sequence will be interpreted as delta values from the sequence's most
  // recent value for the counter. When incremental state is cleared, the
  // counter value is considered to be reset to 0. Thus, the first value after
  // incremental state is cleared is effectively an absolute value.
  optional bool is_incremental = 5;

  // TODO(eseckler): Support arguments describing the counter (?).
  // repeated DebugAnnotation debug_annotations;
}

// End of protos/perfetto/trace/track_event/counter_descriptor.proto

// Begin of protos/perfetto/trace/track_event/track_descriptor.proto

// Defines a track for TrackEvents. Slices and instant events on the same track
// will be nested based on their timestamps, see TrackEvent::Type.
//
// A TrackDescriptor only needs to be emitted by one trace writer / producer and
// is valid for the entirety of the trace. To ensure the descriptor isn't lost
// when the ring buffer wraps, it should be reemitted whenever incremental state
// is cleared.
//
// As a fallback, TrackEvents emitted without an explicit track association will
// be associated with an implicit trace-global track (uuid = 0), see also
// |TrackEvent::track_uuid|. It is possible but not necessary to emit a
// TrackDescriptor for this implicit track.
//
// Next id: 9.
message TrackDescriptor {
  // Unique ID that identifies this track. This ID is global to the whole trace.
  // Producers should ensure that it is unlikely to clash with IDs emitted by
  // other producers. A value of 0 denotes the implicit trace-global track.
  //
  // For example, legacy TRACE_EVENT macros may use a hash involving the async
  // event id + id_scope, pid, and/or tid to compute this ID.
  optional uint64 uuid = 1;

  // A parent track reference can be used to describe relationships between
  // tracks. For example, to define an asynchronous track which is scoped to a
  // specific process, specify the uuid for that process's process track here.
  // Similarly, to associate a COUNTER_THREAD_TIME_NS counter track with a
  // thread, specify the uuid for that thread's thread track here.
  optional uint64 parent_uuid = 5;

  // Name of the track. Optional - if unspecified, it may be derived from the
  // process/thread name (process/thread tracks), the first event's name (async
  // tracks), or counter name (counter tracks).
  optional string name = 2;

  // Associate the track with a process, making it the process-global track.
  // There should only be one such track per process (usually for instant
  // events; trace processor uses this fact to detect pid reuse). If you need
  // more (e.g. for asynchronous events), create child tracks using parent_uuid.
  //
  // Trace processor will merge events on a process track with slice-type events
  // from other sources (e.g. ftrace) for the same process into a single
  // timeline view.
  optional ProcessDescriptor process = 3;
  optional ChromeProcessDescriptor chrome_process = 6;

  // Associate the track with a thread, indicating that the track's events
  // describe synchronous code execution on the thread. There should only be one
  // such track per thread (trace processor uses this fact to detect tid reuse).
  //
  // Trace processor will merge events on a thread track with slice-type events
  // from other sources (e.g. ftrace) for the same thread into a single timeline
  // view.
  optional ThreadDescriptor thread = 4;
  optional ChromeThreadDescriptor chrome_thread = 7;

  // Descriptor for a counter track. If set, the track will only support
  // TYPE_COUNTER TrackEvents (and values provided via TrackEvent's
  // |extra_counter_values|).
  optional CounterDescriptor counter = 8;
}

// End of protos/perfetto/trace/track_event/track_descriptor.proto

// Begin of protos/perfetto/trace/trigger.proto

// When a TracingSession receives a trigger it records the boot time nanoseconds
// in the TracePacket's timestamp field as well as the name of the producer that
// triggered it. We emit this data so filtering can be done on triggers received
// in the trace.
message Trigger {
  // Name of the trigger which was received.
  optional string trigger_name = 1;
  // The actual producer that activated |trigger|.
  optional string producer_name = 2;
  // The verified UID of the producer.
  optional int32 trusted_producer_uid = 3;
}

// End of protos/perfetto/trace/trigger.proto

// Begin of protos/perfetto/trace/trace_packet.proto

// TracePacket is the root object of a Perfeto trace.
// A Perfetto trace is a linear sequence of TracePacket(s).
//
// The tracing service guarantees that all TracePacket(s) written by a given
// TraceWriter are seen in-order, without gaps or duplicates. If, for any
// reason, a TraceWriter sequence becomes invalid, no more packets are returned
// to the Consumer (or written into the trace file).
// TracePacket(s) written by different TraceWriter(s), hence even different
// data sources, can be seen in arbitrary order.
// The consumer can re-establish a total order, if interested, using the packet
// timestamps, after having synchronized the different clocks onto a global
// clock.
//
// The tracing service is agnostic of the content of TracePacket, with the
// exception of few fields (e.g.. trusted_*, trace_config) that are written by
// the service itself.
//
// See the [Buffers and Dataflow](/docs/concepts/buffers.md) doc for details.
//
// Next reserved id: 13 (up to 15).
// Next id: 74.
message TracePacket {
  // The timestamp of the TracePacket.
  // By default this timestamps refers to the trace clock (CLOCK_BOOTTIME on
  // Android). It can be overridden using a different timestamp_clock_id.
  // The clock domain definition in ClockSnapshot can also override:
  // - The unit (default: 1ns).
  // - The absolute vs delta encoding (default: absolute timestamp).
  optional uint64 timestamp = 8;

  // Specifies the ID of the clock used for the TracePacket |timestamp|. Can be
  // one of the built-in types from ClockSnapshot::BuiltinClocks, or a
  // producer-defined clock id.
  // If unspecified and if no default per-sequence value has been provided via
  // TracePacketDefaults, it defaults to BuiltinClocks::BOOTTIME.
  optional uint32 timestamp_clock_id = 58;

  oneof data {
    ProcessTree process_tree = 2;
    ProcessStats process_stats = 9;
    InodeFileMap inode_file_map = 4;
    ChromeEventBundle chrome_events = 5;
    ClockSnapshot clock_snapshot = 6;
    SysStats sys_stats = 7;
    TrackEvent track_event = 11;

    // IDs up to 15 are reserved. They take only one byte to encode their
    // preamble so should be used for frequent events.

    TraceConfig trace_config = 33;
    FtraceStats ftrace_stats = 34;
    TraceStats trace_stats = 35;
    ProfilePacket profile_packet = 37;
    BatteryCounters battery = 38;
    PowerRails power_rails = 40;
    AndroidLogPacket android_log = 39;
    SystemInfo system_info = 45;
    Trigger trigger = 46;
    PackagesList packages_list = 47;
    ChromeBenchmarkMetadata chrome_benchmark_metadata = 48;
    PerfettoMetatrace perfetto_metatrace = 49;
    ChromeMetadataPacket chrome_metadata = 51;
    GpuCounterEvent gpu_counter_event = 52;
    GpuRenderStageEvent gpu_render_stage_event = 53;
    StreamingProfilePacket streaming_profile_packet = 54;
    HeapGraph heap_graph = 56;
    GraphicsFrameEvent graphics_frame_event = 57;
    VulkanMemoryEvent vulkan_memory_event = 62;
    GpuLog gpu_log = 63;
    VulkanApiEvent vulkan_api_event = 65;
    PerfSample perf_sample = 66;
    CpuInfo cpu_info = 67;
    SmapsPacket smaps_packet = 68;
    TracingServiceEvent service_event = 69;
    InitialDisplayState initial_display_state = 70;
    GpuMemTotalEvent gpu_mem_total_event = 71;
    MemoryTrackerSnapshot memory_tracker_snapshot = 73;

    // Only used in profile packets.
    ProfiledFrameSymbols profiled_frame_symbols = 55;
    ModuleSymbols module_symbols = 61;
    DeobfuscationMapping deobfuscation_mapping = 64;

    // Only used by TrackEvent.
    TrackDescriptor track_descriptor = 60;

    // Deprecated, use TrackDescriptor instead.
    ProcessDescriptor process_descriptor = 43;

    // Deprecated, use TrackDescriptor instead.
    ThreadDescriptor thread_descriptor = 44;

    // Events from the Linux kernel ftrace infrastructure.
    FtraceEventBundle ftrace_events = 1;

    // This field is emitted at periodic intervals (~10s) and
    // contains always the binary representation of the UUID
    // {82477a76-b28d-42ba-81dc-33326d57a079}. This is used to be able to
    // efficiently partition long traces without having to fully parse them.
    bytes synchronization_marker = 36;

    // Zero or more proto encoded trace packets compressed using deflate.
    // Each compressed_packets TracePacket (including the two field ids and
    // sizes) should be less than 512KB.
    bytes compressed_packets = 50;

    // Data sources can extend the trace proto with custom extension protos (see
    // docs/design-docs/extensions.md). When they do that, the descriptor of
    // their extension proto descriptor is serialized in this packet. This
    // allows trace_processor to deserialize extended messages using reflection
    // even if the extension proto is not checked in the Perfetto repo.
    ExtensionDescriptor extension_descriptor = 72;

    // This field is only used for testing.
    // In previous versions of this proto this field had the id *********
    // This caused many problems:
    // - protozero decoder does not handle field ids larger than 999.
    // - old versions of protoc produce Java bindings with syntax errors when
    //   the field id is large enough.
    TestEvent for_testing = 900;
  }

  // Trusted user id of the producer which generated this packet. Keep in sync
  // with TrustedPacket.trusted_uid.
  //
  // TODO(eseckler): Emit this field in a PacketSequenceDescriptor message
  // instead.
  oneof optional_trusted_uid { int32 trusted_uid = 3; };

  // Service-assigned identifier of the packet sequence this packet belongs to.
  // Uniquely identifies a producer + writer pair within the tracing session. A
  // value of zero denotes an invalid ID. Keep in sync with
  // TrustedPacket.trusted_packet_sequence_id.
  oneof optional_trusted_packet_sequence_id {
    uint32 trusted_packet_sequence_id = 10;
  }

  // Incrementally emitted interned data, valid only on the packet's sequence
  // (packets with the same |trusted_packet_sequence_id|). The writer will
  // usually emit new interned data in the same TracePacket that first refers to
  // it (since the last reset of interning state). It may also be emitted
  // proactively in advance of referring to them in later packets.
  optional InternedData interned_data = 12;

  enum SequenceFlags {
    SEQ_UNSPECIFIED = 0;

    // Set by the writer to indicate that it will re-emit any incremental data
    // for the packet's sequence before referring to it again. This includes
    // interned data as well as periodically emitted data like
    // Process/ThreadDescriptors. This flag only affects the current packet
    // sequence (see |trusted_packet_sequence_id|).
    //
    // When set, this TracePacket and subsequent TracePackets on the same
    // sequence will not refer to any incremental data emitted before this
    // TracePacket. For example, previously emitted interned data will be
    // re-emitted if it is referred to again.
    //
    // When the reader detects packet loss (|previous_packet_dropped|), it needs
    // to skip packets in the sequence until the next one with this flag set, to
    // ensure intact incremental data.
    SEQ_INCREMENTAL_STATE_CLEARED = 1;

    // This packet requires incremental state, such as TracePacketDefaults or
    // InternedData, to be parsed correctly. The trace reader should skip this
    // packet if incremental state is not valid on this sequence, i.e. if no
    // packet with the SEQ_INCREMENTAL_STATE_CLEARED flag has been seen on the
    // current |trusted_packet_sequence_id|.
    SEQ_NEEDS_INCREMENTAL_STATE = 2;
  };
  optional uint32 sequence_flags = 13;

  // DEPRECATED. Moved to SequenceFlags::SEQ_INCREMENTAL_STATE_CLEARED.
  optional bool incremental_state_cleared = 41;

  // Default values for fields of later TracePackets emitted on this packet's
  // sequence (TracePackets with the same |trusted_packet_sequence_id|).
  // It must be reemitted when incremental state is cleared (see
  // |incremental_state_cleared|).
  // Requires that any future packet emitted on the same sequence specifies
  // the SEQ_NEEDS_INCREMENTAL_STATE flag.
  // TracePacketDefaults always override the global defaults for any future
  // packet on this sequence (regardless of SEQ_NEEDS_INCREMENTAL_STATE).
  optional TracePacketDefaults trace_packet_defaults = 59;

  // Flag set by the service if, for the current packet sequence (see
  // |trusted_packet_sequence_id|), either:
  // * this is the first packet, or
  // * one or multiple packets were dropped since the last packet that the
  //   consumer read from the sequence. This can happen if chunks in the trace
  //   buffer are overridden before the consumer could read them when the trace
  //   is configured in ring buffer mode.
  //
  // When packet loss occurs, incrementally emitted data (including interned
  // data) on the sequence should be considered invalid up until the next packet
  // with SEQ_INCREMENTAL_STATE_CLEARED set.
  optional bool previous_packet_dropped = 42;
}

// End of protos/perfetto/trace/trace_packet.proto

// Begin of protos/perfetto/trace/trace.proto

message Trace {
  repeated TracePacket packet = 1;

  // Do NOT add any other field here. This is just a convenience wrapper for
  // the use case of a trace being saved to a file. There are other cases
  // (streaming) where TracePacket are directly streamed without being wrapped
  // in a Trace proto. Nothing should ever rely on the full trace, all the
  // logic should be based on TracePacket(s).
}

// End of protos/perfetto/trace/trace.proto

// Begin of protos/perfetto/trace/test_extensions.proto

// Extensions for TrackEvent used for integration testing. This proto file is
// compiled to descriptor and is used in tools/diff_test_trace_processor.py.
//
// See docs/design-docs/extensions.md for more details.
message TestExtension {
  extend TrackEvent {
    optional string string_extension_for_testing = 9900;
    repeated int32 int_extension_for_testing = 9901;
    optional string omitted_extension_for_testing = 9902;
    optional TestExtensionChild nested_message_extension_for_testing = 9903;
  }
}

message TestExtensionChild {
  optional string child_field_for_testing = 1;
}

// End of protos/perfetto/trace/test_extensions.proto
