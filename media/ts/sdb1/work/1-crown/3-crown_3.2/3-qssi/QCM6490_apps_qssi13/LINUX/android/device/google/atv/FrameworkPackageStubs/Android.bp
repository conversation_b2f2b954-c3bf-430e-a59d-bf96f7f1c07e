package {
    // See: http://go/android-license-faq
    // A large-scale-change added 'default_applicable_licenses' to import
    // all of the 'license_kinds' from "device_google_atv_license"
    // to get the below license kinds:
    //   SPDX-license-identifier-Apache-2.0
    default_applicable_licenses: ["device_google_atv_license"],
}

android_app {
    name: "TvFrameworkPackageStubs",

    srcs: ["src/**/*.java"],
    system_ext_specific: true,
    overrides: ["FrameworkPackageStubs"],
    platform_apis: true,
    certificate: "platform",
}
