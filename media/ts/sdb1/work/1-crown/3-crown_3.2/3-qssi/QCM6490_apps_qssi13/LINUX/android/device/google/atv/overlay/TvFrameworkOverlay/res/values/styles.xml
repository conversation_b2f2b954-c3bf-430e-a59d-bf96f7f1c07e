<?xml version="1.0" encoding="utf-8"?>
<!--
     Copyright (C) 2006 The Android Open Source Project

     Licensed under the Apache License, Version 2.0 (the "License");
     you may not use this file except in compliance with the License.
     You may obtain a copy of the License at

          http://www.apache.org/licenses/LICENSE-2.0

     Unless required by applicable law or agreed to in writing, software
     distributed under the License is distributed on an "AS IS" BASIS,
     WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
     See the License for the specific language governing permissions and
     limitations under the License.
-->

<resources>

    <style name="Animation" />

    <!-- Standard animations for wallpapers. -->
    <style name="Animation.Wallpaper">
        <item name="android:windowEnterAnimation">@anim/atv_wallpaper_enter</item>
        <item name="android:windowExitAnimation">@anim/atv_wallpaper_exit</item>
    </style>

    <!-- Standard animations for a full-screen window or activity. -->
    <style name="Animation.Activity">
        <item name="android:activityOpenEnterAnimation">@anim/atv_fade_in_top</item>
        <item name="android:activityOpenExitAnimation">@anim/atv_fade_in_bottom</item>
        <item name="android:activityCloseEnterAnimation">@anim/atv_fade_out_bottom</item>
        <item name="android:activityCloseExitAnimation">@anim/atv_fade_out_top</item>

        <item name="android:taskOpenEnterAnimation">@anim/atv_fade_in_top</item>
        <item name="android:taskOpenExitAnimation">@anim/atv_fade_in_bottom</item>
        <item name="android:taskCloseEnterAnimation">@anim/atv_fade_out_bottom</item>
        <item name="android:taskCloseExitAnimation">@anim/atv_fade_out_top</item>

        <item name="android:taskToFrontEnterAnimation">@anim/atv_fade_in_top</item>
        <item name="android:taskToFrontExitAnimation">@anim/atv_fade_in_bottom</item>
        <item name="android:taskToBackEnterAnimation">@anim/atv_fade_out_bottom</item>
        <item name="android:taskToBackExitAnimation">@anim/atv_fade_out_top</item>

        <item name="android:wallpaperOpenEnterAnimation">@anim/atv_fade_out_bottom</item>
        <item name="android:wallpaperOpenExitAnimation">@anim/atv_fade_out_top</item>
        <item name="android:wallpaperCloseEnterAnimation">@anim/atv_fade_in_top</item>
        <item name="android:wallpaperCloseExitAnimation">@anim/atv_fade_in_bottom</item>

        <item name="android:wallpaperIntraOpenEnterAnimation">@anim/atv_fade_in_top</item>
        <item name="android:wallpaperIntraOpenExitAnimation">@anim/atv_fade_in_bottom</item>
        <item name="android:wallpaperIntraCloseEnterAnimation">@anim/atv_fade_out_bottom</item>
        <item name="android:wallpaperIntraCloseExitAnimation">@anim/atv_fade_out_top</item>
    </style>

</resources>
