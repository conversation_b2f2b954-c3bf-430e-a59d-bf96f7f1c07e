<?xml version="1.0" encoding="utf-8"?>
<!-- Copyright (C) 2014 The Android Open Source Project

     Licensed under the Apache License, Version 2.0 (the "License");
     you may not use this file except in compliance with the License.
     You may obtain a copy of the License at

         http://www.apache.org/licenses/LICENSE-2.0

     Unless required by applicable law or agreed to in writing, software
     distributed under the License is distributed on an "AS IS" BASIS,
     WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
     See the License for the specific language governing permissions and
     limitations under the License.
-->

<resources>

    <!-- development/sdk_overlay enables voice. Reset to data-only. -->
    <bool name="config_voice_capable">false</bool>

    <!-- This device doesn't support quick settings and its associated APIs -->
    <bool name="config_quickSettingsSupported">false</bool>

    <!-- Do not restrict the UI resolution on the WM side; use the native resolution of the device's
         screen -->
    <integer name="config_maxUiWidth">0</integer>

    <!--
        This is used by the connectivity manager to decide which networks can coexist
        based on the hardware.

        ATV SDK is not designed to have all networks available on handheld devices.
        To date only specific networks (e.g. "mobile,0,..") are removed from defaults
        to comply with CTS, others are still kept for different compatibilities.

        See format details here: frameworks/base/core/res/res/values/config.xml.
    -->
    <string-array translatable="false" name="networkAttributes">
        <item>"wifi,1,1,1,-1,true"</item>
        <item>"mobile_mms,2,0,2,60000,true"</item>
        <item>"mobile_supl,3,0,2,60000,true"</item>
        <item>"mobile_dun,4,0,2,60000,true"</item>
        <item>"mobile_hipri,5,0,3,60000,true"</item>
        <item>"mobile_fota,10,0,2,60000,true"</item>
        <item>"mobile_ims,11,0,2,60000,true"</item>
        <item>"mobile_cbs,12,0,2,60000,true"</item>
        <item>"wifi_p2p,13,1,0,-1,true"</item>
        <item>"mobile_ia,14,0,2,-1,true"</item>
        <item>"mobile_emergency,15,0,2,-1,true"</item>
    </string-array>
</resources>
