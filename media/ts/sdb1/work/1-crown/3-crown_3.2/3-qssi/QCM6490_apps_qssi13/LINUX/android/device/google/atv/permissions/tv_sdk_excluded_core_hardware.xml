<?xml version="1.0" encoding="utf-8"?>
<!-- Copyright 2019 The Android Open Source Project

     Licensed under the Apache License, Version 2.0 (the "License");
     you may not use this file except in compliance with the License.
     You may obtain a copy of the License at

          http://www.apache.org/licenses/LICENSE-2.0

     Unless required by applicable law or agreed to in writing, software
     distributed under the License is distributed on an "AS IS" BASIS,
     WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
     See the License for the specific language governing permissions and
     limitations under the License.
-->

<!-- Excludes all non-default ATV SDK features to comply with CTS -->
<permissions>
    <!-- ATV SDK needs some basic camera features to enable video calling, but can exclude some -->
    <unavailable-feature name="android.hardware.camera.ar" />
    <unavailable-feature name="android.hardware.camera.level.full" />
    <unavailable-feature name="android.hardware.camera.capability.manual_sensor" />
    <unavailable-feature name="android.hardware.camera.capability.manual_post_processing" />

    <!-- ATV SDK is not designed to have telephony services by default -->
    <unavailable-feature name="android.hardware.telephony" />
    <unavailable-feature name="android.hardware.telephony.gsm" />
    <unavailable-feature name="android.software.telecom" />

    <!-- ATV SDK is not designed to have communication (VoIP, etc.) services by default -->
    <unavailable-feature name="android.software.connectionservice" />

    <!-- ATV SDK is not designed to have GPS support by default -->
    <unavailable-feature name="android.hardware.location.gps" />

    <!--
        ATV SDK supports only landscape orientation by default
        and to date it is not designed to be rotated.
    -->
    <unavailable-feature name="android.hardware.screen.portrait" />

    <!-- ATV SDK does not support a lock screen screen by default -->
    <unavailable-feature name="android.software.secure_lock_screen" />
</permissions>
