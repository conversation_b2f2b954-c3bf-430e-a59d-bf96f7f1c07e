#
# Copyright (C) 2014 The Android Open Source Project
#
# Licensed under the Apache License, Version 2.0 (the "License");
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#
#     http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.
#

# Override source.properties in development/build/sdk-android-${TARGET_CPU_ABI}.atree
${HOST_OUT}/device/google/atv/sdk/images_${TARGET_CPU_ABI}_source.properties  system-images/${PLATFORM_NAME}/${TARGET_CPU_ABI}/source.properties
device/google/atv/sdk/images_${TARGET_CPU_ABI}_hardware.ini                   system-images/${PLATFORM_NAME}/${TARGET_CPU_ABI}/hardware.ini

# devices.xml file with custom device definitions
device/google/atv/sdk/devices.xml   system-images/${PLATFORM_NAME}/${TARGET_CPU_ABI}/devices.xml
