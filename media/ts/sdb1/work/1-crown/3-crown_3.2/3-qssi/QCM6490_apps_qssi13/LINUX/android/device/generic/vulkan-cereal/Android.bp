// *** T<PERSON>IS PACKAGE HAS SPECIAL LICENSING CONDITIONS.  PLEASE
//     CONSULT THE <NAME_EMAIL> BEFORE
//     DEPENDING ON IT IN YOUR PROJECT. ***
package {
    default_applicable_licenses: ["device_generic_vulkan-cereal_license"],
}

// Added automatically by a large-scale-change that took the approach of
// 'apply every license found to every target'. While this makes sure we respect
// every license restriction, it may not be entirely correct.
//
// e.g. GPL in an MIT project might only apply to the contrib/ directory.
//
// Please consider splitting the single license below into multiple licenses,
// taking care not to lose any license_kind information, and overriding the
// default license using the 'licenses: [...]' property on targets as needed.
//
// For unused files, consider creating a 'fileGroup' with "//visibility:private"
// to attach the license to, and including a comment whether the files may be
// used in the current project.
// See: http://go/android-license-faq
license {
    name: "device_generic_vulkan-cereal_license",
    visibility: [":__subpackages__"],
    license_kinds: [
        "SPDX-license-identifier-Apache-2.0",
        "SPDX-license-identifier-BSD",
        "SPDX-license-identifier-CC-BY",
        "SPDX-license-identifier-GPL",
        "SPDX-license-identifier-GPL-2.0",
        "SPDX-license-identifier-GPL-3.0",
        "SPDX-license-identifier-ISC",
        "SPDX-license-identifier-LGPL",
        "SPDX-license-identifier-MIT",
        "SPDX-license-identifier-OFL", // by exception only
        "SPDX-license-identifier-Zlib",
        "legacy_by_exception_only", // by exception only
        "legacy_notice",
        "legacy_unencumbered",
    ],
    // large-scale-change unable to identify any license_text files
}

cc_library_headers {
    name: "gfxstream_headers",
    host_supported: true,
    export_include_dirs: [
        ".",
        "include",
        "base",
        "host-common",
        "snapshot",
        "stream-servers",
        "stream-servers/vulkan",
        "stream-servers/glestranslator/include",
        "stream-servers/OpenGLESDispatch",
    ],
    apex_available: [
        "//apex_available:platform",
        "com.android.virt",
    ],
}

cc_library_headers {
    name: "gfxstream_x11_headers",
    host_supported: true,
    export_include_dirs: [
        "stream-servers/apigen-codec-common",
    ],
    apex_available: [
        "//apex_available:platform",
        "com.android.virt",
    ],
}

cc_defaults {
    name: "gfxstream_defaults",
    cflags: ["-D_FILE_OFFSET_BITS=64",
             "-DVIRGL_RENDERER_UNSTABLE_APIS",
             "-Wno-unreachable-code-loop-increment",
             "-Wno-unused-parameter",
             "-Wno-unused-function",
             "-Wno-unused-variable",
             "-Wno-ignored-qualifiers",
             "-Wno-reorder-ctor",
             "-Wno-mismatched-tags",
             "-Wno-missing-field-initializers",
             "-Wno-implicit-fallthrough",
             "-Wno-unused-private-field",
             "-Wno-macro-redefined",
            ],
    header_libs: [
        "gfxstream_headers",
        "gfxstream_x11_headers",
    ],
    target: {
        host: {
            compile_multilib: "64",
            cflags: [
                "-U__ANDROID__",
                "-UANDROID",
            ]
        },
        android: {
            compile_multilib: "64",
            shared_libs: [ "libnativewindow", ],
        },
        linux_bionic_arm64: {
            enabled: true,
            cflags: [ "-Dandroidbionic", ],
        }
    },
    host_supported: true,

    apex_available: [
        "//apex_available:platform",
        "com.android.virt",
    ],
}
