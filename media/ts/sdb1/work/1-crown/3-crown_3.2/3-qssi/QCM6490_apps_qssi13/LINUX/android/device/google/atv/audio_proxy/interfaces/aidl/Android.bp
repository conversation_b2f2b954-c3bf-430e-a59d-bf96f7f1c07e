package {
    default_applicable_licenses: ["Android-Apache-2.0"],
}

aidl_interface {
    name: "device.google.atv.audio_proxy-aidl",
    vendor_available: true,
    system_ext_specific: true,
    srcs: [
        "device/google/atv/audio_proxy/*.aidl",
    ],
    imports: [
        "android.hardware.common-V2",
        "android.hardware.common.fmq-V1",
    ],
    stability: "vintf",
    backend: {
        ndk: {
            enabled: true,
        },
        java: {
            enabled: false,
        },
        cpp: {
            enabled: false,
        },
    },
    versions: ["1"],
}
