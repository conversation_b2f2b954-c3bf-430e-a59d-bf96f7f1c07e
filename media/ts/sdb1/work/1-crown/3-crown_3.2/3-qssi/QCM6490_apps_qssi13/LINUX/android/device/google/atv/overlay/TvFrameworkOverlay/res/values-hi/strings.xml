<?xml version="1.0" encoding="UTF-8"?>
<!--  Copyright (C) 2017 The Android Open Source Project

     Licensed under the Apache License, Version 2.0 (the "License");
     you may not use this file except in compliance with the License.
     You may obtain a copy of the License at

         http://www.apache.org/licenses/LICENSE-2.0

     Unless required by applicable law or agreed to in writing, software
     distributed under the License is distributed on an "AS IS" BASIS,
     WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
     See the License for the specific language governing permissions and
     limitations under the License.
 -->

<resources xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:xliff="urn:oasis:names:tc:xliff:document:1.2">
    <string name="accessibility_shortcut_multiple_service_warning" msgid="7133244216041378936">"<xliff:g id="SERVICE_0">%1$s</xliff:g> का इस्तेमाल करने के लिए, आपने \'वापस जाएं\' और \'नीचे जाएं\' बटन को एक साथ तीन सेकंड तक दबाकर रखा है.\n\n <xliff:g id="SERVICE_1">%1$s</xliff:g> को अभी चालू करने के लिए, \'वापस जाएं\' और \'नीचे जाएं\' बटन को फिर से तीन सेकंड तक दबाकर रखें. आप जब चाहें, इस शॉर्टकट का इस्तेमाल करके <xliff:g id="SERVICE_2">%1$s</xliff:g> को चालू या बंद कर सकते हैं.\n\n आप सेटिंग &gt; सिस्टम &gt; सुलभता पर जाकर, अपनी प्राथमिकताओं को बदल सकते हैं."</string>
    <string name="accessibility_shortcut_toogle_warning" msgid="6107141001991769734">"शॉर्टकट के चालू होने पर, \'वापस जाएं\' और \'नीचे जाएं\' बटन को एक साथ तीन सेकंड तक दबाने से, सुलभता सुविधा शुरू हाे जाएगी.\n\n मौजूदा सुलभता सुविधा:\n <xliff:g id="SERVICE_NAME">%1$s</xliff:g>\n\n आप \'सेटिंग और सुलभता\' में जाकर सुविधा में बदलाव कर सकते हैं."</string>
    <string name="accessibility_shortcut_enabling_service" msgid="955379455142747901">"\'वापस जाएं\' और \'नीचे जाएं\' बटन को दबाकर रखें. <xliff:g id="SERVICE_NAME">%1$s</xliff:g> को चालू किया गया."</string>
    <string name="accessibility_shortcut_disabling_service" msgid="1407311966343470931">"\'वापस जाएं\' और \'नीचे जाएं\' बटन को दबाकर रखें. <xliff:g id="SERVICE_NAME">%1$s</xliff:g> को बंद किया गया."</string>
    <string name="accessibility_shortcut_spoken_feedback" msgid="7263788823743141556">"<xliff:g id="SERVICE_0">%1$s</xliff:g> का इस्तेमाल करने के लिए, आपने \'वापस जाएं\' और \'नीचे जाएं\' बटन को एक साथ तीन सेकंड तक दबाकर रखा है. <xliff:g id="SERVICE_1">%1$s</xliff:g> को किसी भी समय चालू या बंद करने के लिए, \'वापस जाएं\' और \'नीचे जाएं\' बटन को फिर से तीन सेकंड तक दबाकर रखें. आप जब चाहें, इस शॉर्टकट का इस्तेमाल करके <xliff:g id="SERVICE_2">%1$s</xliff:g> को चालू या बंद कर सकते हैं."</string>
    <string name="accessibility_shortcut_single_service_warning" msgid="7941823324711523679">"<xliff:g id="SERVICE_0">%1$s</xliff:g> का इस्तेमाल करने के लिए, आपने \'वापस जाएं\' और \'नीचे जाएं\' बटन को एक साथ तीन सेकंड तक दबाकर रखा है.\n\n <xliff:g id="SERVICE_1">%1$s</xliff:g> को अभी चालू करने के लिए, \'वापस जाएं\' और \'नीचे जाएं\' बटन को फिर से तीन सेकंड तक दबाकर रखें.\n आप जब चाहें, इस शॉर्टकट का इस्तेमाल करके <xliff:g id="SERVICE_2">%1$s</xliff:g> को चालू या बंद कर सकते हैं.\n\n आप सेटिंग &gt; सिस्टम &gt; सुलभता पर जाकर, अपनी प्राथमिकताओं को बदल सकते हैं."</string>
    <string name="disable_accessibility_shortcut" msgid="4559312586447750126">"अभी नहीं"</string>
    <string name="leave_accessibility_shortcut_on" msgid="6807632291651241490">"अभी चालू करें"</string>
</resources>
