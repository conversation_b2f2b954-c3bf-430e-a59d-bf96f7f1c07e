TARGET_SYSTEM_PROP += device/qcom/qssi/thundercomm/system.prop

# Set firmware version from environment variable or default
PRODUCT_PROPERTY_OVERRIDES += ro.build.version.firmware=$(or $(HARDWARE_VERSION),V066)

# Add tc sepolicy
-include device/qcom/qssi/thundercomm/sepolicy/sepolicy.mk

PRODUCT_PACKAGES += CrownSettings

# FileEncrypt
PRODUCT_PACKAGES += FileEncrypt

# MonitorTool
PRODUCT_PACKAGES += MonitorTool

# add dash camera
PRODUCT_PACKAGES += ts-dash-camera

#Add tsnv function
PRODUCT_PACKAGES += \
       libtsnvwrapper \
       vendor.thundercomm.hardware.tsnv@1.0 \
       tsnvwrapper_test \
       nvftm

