<?xml version="1.0" encoding="utf-8"?>
<!--
  ~ Copyright (C) 2021 The Android Open Source Project
  ~
  ~ Licensed under the Apache License, Version 2.0 (the "License");
  ~ you may not use this file except in compliance with the License.
  ~ You may obtain a copy of the License at
  ~
  ~      http://www.apache.org/licenses/LICENSE-2.0
  ~
  ~ Unless required by applicable law or agreed to in writing, software
  ~ distributed under the License is distributed on an "AS IS" BASIS,
  ~ WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
  ~ See the License for the specific language governing permissions and
  ~ limitations under the License.
  -->
<FrameLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent">
  <LinearLayout
      android:layout_width="@dimen/progress_fragment_content_width"
      android:layout_height="wrap_content"
      android:orientation="horizontal"
      android:layoutDirection="locale"
      android:gravity="center_vertical"
      android:layout_gravity="center">

  <FrameLayout
      android:layout_width="wrap_content"
      android:layout_height="wrap_content"
      android:paddingHorizontal="@dimen/fullscreen_icon_horizontal_padding"
      android:paddingVertical="@dimen/fullscreen_icon_vertical_padding"
      android:clipToPadding="false"
      android:layout_marginEnd="@dimen/fullscreen_icon_margin_end"
      android:layout_gravity="center">
    <ImageView
        android:id="@+id/icon"
        android:layout_height="@dimen/fullscreen_remote_icon_size"
        android:layout_width="wrap_content"
        android:adjustViewBounds="true"
        android:scaleType="fitXY"
        android:src="@drawable/ic_atv_remote"
        android:background="@drawable/ic_atv_remote_elevation"
        android:elevation="@dimen/fullscreen_remote_icon_elevation"/>
  </FrameLayout>

    <LinearLayout
        android:orientation="vertical"
        android:gravity="start"
        android:layout_width="match_parent"
        android:layout_height="wrap_content">
      <TextView
          android:id="@+id/fullscreen_title"
          android:layout_width="match_parent"
          android:layout_height="wrap_content"
          android:gravity="center_vertical"
          android:text="@string/settings_bt_update"
          android:layout_marginBottom="@dimen/fullscreen_title_margin_bottom"
          style="@style/SettingsStyle.TextAppearanceLarge" />
      <TextView
          android:id="@+id/fullscreen_summary"
          android:layout_width="wrap_content"
          android:layout_height="wrap_content"
          android:text="@string/settings_bt_update_summary"
          style="@style/SettingsStyle.TextAppearanceMedium" />
      <LinearLayout
          android:orientation="horizontal"
          android:gravity="center_vertical"
          android:layout_width="wrap_content"
          android:layout_height="wrap_content"
          android:layout_marginTop="@dimen/fullscreen_progress_margin_top"
          android:layout_marginBottom="@dimen/fullscreen_progress_margin_bottom">
        <ProgressBar
            android:id="@+id/fullscreen_progressbar"
            android:layout_width="@dimen/fullscreen_progress_progressbar_width"
            android:layout_height="wrap_content"
            android:layout_centerVertical="true"
            android:visibility="invisible"
            style="@style/BtProgressBar" />
        <ImageView
            android:id="@+id/check_icon"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginStart="20dp"
            android:visibility="invisible"
            android:height="@dimen/fullscreen_notice_icon_size"
            android:width="@dimen/fullscreen_notice_icon_size"
            android:src="@drawable/ic_baseline_check_24dp" />

      </LinearLayout>
    </LinearLayout>
  </LinearLayout>
</FrameLayout>
