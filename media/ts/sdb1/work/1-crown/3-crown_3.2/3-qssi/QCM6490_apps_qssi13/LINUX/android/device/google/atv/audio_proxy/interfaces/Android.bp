package {
    // See: http://go/android-license-faq
    // A large-scale-change added 'default_applicable_licenses' to import
    // all of the 'license_kinds' from "device_google_atv_license"
    // to get the below license kinds:
    //   legacy_notice
    default_applicable_licenses: ["device_google_atv_license"],
}

hidl_package_root {
    name: "device.google.atv.audio_proxy",
    path: "device/google/atv/audio_proxy/interfaces",
}
