<?xml version="1.0" encoding="UTF-8"?>
<!--  Copyright (C) 2017 The Android Open Source Project

     Licensed under the Apache License, Version 2.0 (the "License");
     you may not use this file except in compliance with the License.
     You may obtain a copy of the License at

         http://www.apache.org/licenses/LICENSE-2.0

     Unless required by applicable law or agreed to in writing, software
     distributed under the License is distributed on an "AS IS" BASIS,
     WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
     See the License for the specific language governing permissions and
     limitations under the License.
 -->

<resources xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:xliff="urn:oasis:names:tc:xliff:document:1.2">
    <string name="accessibility_shortcut_multiple_service_warning" msgid="7133244216041378936">"‎‏‎‎‎‎‎‏‎‏‏‏‎‎‎‎‎‏‏‏‏‎‏‎‎‏‎‏‏‏‏‏‏‏‏‎‎‎‏‎‏‏‏‏‏‏‏‎‎‏‎‏‏‏‏‏‏‏‎‏‎‎‏‎‎‎‏‎‏‎‎‏‎‏‎‎‎‎‎‏‏‎‎‏‎‏‎‎‎‏‏‏‏‎‎‎‎You have held both the back and down buttons for three seconds to use ‎‏‎‎‏‏‎<xliff:g id="SERVICE_0">%1$s</xliff:g>‎‏‎‎‏‏‏‎.‎‏‎‎‏‏‎\n‎‏‎‎‏‏‏‎‎‏‎‎‏‏‎\n‎‏‎‎‏‏‏‎ To enable ‎‏‎‎‏‏‎<xliff:g id="SERVICE_1">%1$s</xliff:g>‎‏‎‎‏‏‏‎ now, hold the back and down buttons for three seconds again. Use this shortcut anytime to enable or disable ‎‏‎‎‏‏‎<xliff:g id="SERVICE_2">%1$s</xliff:g>‎‏‎‎‏‏‏‎.‎‏‎‎‏‏‎\n‎‏‎‎‏‏‏‎‎‏‎‎‏‏‎\n‎‏‎‎‏‏‏‎ You can adjust your preferences in Settings &gt; System &gt; Accessibility.‎‏‎‎‏‎"</string>
    <string name="accessibility_shortcut_toogle_warning" msgid="6107141001991769734">"‎‏‎‎‎‎‎‏‎‏‏‏‎‎‎‎‎‏‏‏‏‎‏‎‎‏‎‏‏‏‏‏‏‏‎‏‎‏‎‎‏‏‎‎‎‎‎‎‏‏‏‎‏‏‎‎‎‏‏‎‎‎‏‎‎‏‏‎‎‎‎‎‏‏‏‏‎‎‎‏‎‏‎‎‏‏‏‎‏‎‎‎‎‏‏‎‎When the shortcut is on, pressing both the back and down buttons for 3 seconds will start an accessibility feature.‎‏‎‎‏‏‎\n‎‏‎‎‏‏‏‎‎‏‎‎‏‏‎\n‎‏‎‎‏‏‏‎ Current accessibility feature:‎‏‎‎‏‏‎\n‎‏‎‎‏‏‏‎ ‎‏‎‎‏‏‎<xliff:g id="SERVICE_NAME">%1$s</xliff:g>‎‏‎‎‏‏‏‎‎‏‎‎‏‏‎\n‎‏‎‎‏‏‏‎‎‏‎‎‏‏‎\n‎‏‎‎‏‏‏‎ You can change the feature in Settings &gt; Accessibility.‎‏‎‎‏‎"</string>
    <string name="accessibility_shortcut_enabling_service" msgid="955379455142747901">"‎‏‎‎‎‎‎‏‎‏‏‏‎‎‎‎‎‏‏‏‏‎‏‎‎‏‎‏‏‏‏‎‎‏‏‎‏‎‏‎‎‎‎‏‎‎‎‏‏‎‎‎‎‏‎‎‎‏‏‎‏‎‏‏‏‏‎‎‏‎‏‏‏‏‏‎‏‎‏‎‏‏‏‏‎‏‏‏‏‏‏‎‏‎Held back and down buttons. ‎‏‎‎‏‏‎<xliff:g id="SERVICE_NAME">%1$s</xliff:g>‎‏‎‎‏‏‏‎ turned on.‎‏‎‎‏‎"</string>
    <string name="accessibility_shortcut_disabling_service" msgid="1407311966343470931">"‎‏‎‎‎‎‎‏‎‏‏‏‎‎‎‎‎‏‏‏‏‎‏‎‎‏‎‏‏‏‏‎‏‏‎‎‏‏‏‎‎‎‎‏‏‏‏‏‎‎‎‏‏‎‏‏‎‎‎‏‏‎‏‏‏‏‎‎‏‎‎‎‎‎‏‏‏‏‎‎‎‏‏‏‏‏‎‏‎‏‎‎‏‏‎Held back and down buttons. ‎‏‎‎‏‏‎<xliff:g id="SERVICE_NAME">%1$s</xliff:g>‎‏‎‎‏‏‏‎ turned off.‎‏‎‎‏‎"</string>
    <string name="accessibility_shortcut_spoken_feedback" msgid="7263788823743141556">"‎‏‎‎‎‎‎‏‎‏‏‏‎‎‎‎‎‏‏‏‏‎‏‎‎‏‎‏‏‏‏‏‏‏‏‎‎‏‎‎‏‏‎‎‏‏‏‎‎‎‏‎‏‎‎‏‎‏‏‏‎‎‏‏‎‎‏‏‎‎‏‎‏‎‏‎‏‎‎‏‏‎‎‏‎‏‏‎‏‎‏‏‎‏‎‎‎You have held both the back and down buttons for three seconds to use ‎‏‎‎‏‏‎<xliff:g id="SERVICE_0">%1$s</xliff:g>‎‏‎‎‏‏‏‎. To enable ‎‏‎‎‏‏‎<xliff:g id="SERVICE_1">%1$s</xliff:g>‎‏‎‎‏‏‏‎ now, hold the back and down buttons for three seconds again. Use this shortcut anytime to enable or disable ‎‏‎‎‏‏‎<xliff:g id="SERVICE_2">%1$s</xliff:g>‎‏‎‎‏‏‏‎.‎‏‎‎‏‎"</string>
    <string name="accessibility_shortcut_single_service_warning" msgid="7941823324711523679">"‎‏‎‎‎‎‎‏‎‏‏‏‎‎‎‎‎‏‏‏‏‎‏‎‎‏‎‏‏‏‏‏‏‏‏‎‏‏‏‎‎‎‏‏‎‏‏‏‎‎‎‎‎‏‏‎‎‎‏‏‏‏‎‎‎‏‏‏‏‎‏‎‎‏‎‏‎‏‏‏‏‎‎‏‎‏‎‏‎‏‎‏‏‏‏‏‎You have held both the back and down buttons for three seconds to use ‎‏‎‎‏‏‎<xliff:g id="SERVICE_0">%1$s</xliff:g>‎‏‎‎‏‏‏‎.‎‏‎‎‏‏‎\n‎‏‎‎‏‏‏‎‎‏‎‎‏‏‎\n‎‏‎‎‏‏‏‎ To enable ‎‏‎‎‏‏‎<xliff:g id="SERVICE_1">%1$s</xliff:g>‎‏‎‎‏‏‏‎ now, hold the back and down buttons for three seconds again.‎‏‎‎‏‏‎\n‎‏‎‎‏‏‏‎ Use this shortcut anytime to enable or disable ‎‏‎‎‏‏‎<xliff:g id="SERVICE_2">%1$s</xliff:g>‎‏‎‎‏‏‏‎.‎‏‎‎‏‏‎\n‎‏‎‎‏‏‏‎‎‏‎‎‏‏‎\n‎‏‎‎‏‏‏‎ You can adjust your preferences in Settings &gt; System &gt; Accessibility.‎‏‎‎‏‎"</string>
    <string name="disable_accessibility_shortcut" msgid="4559312586447750126">"‎‏‎‎‎‎‎‏‎‏‏‏‎‎‎‎‎‏‏‏‏‎‏‎‎‏‎‏‏‏‏‏‎‏‏‏‏‏‏‎‏‎‎‎‏‎‏‏‏‏‎‏‏‏‎‏‎‏‎‎‏‎‎‎‎‎‏‏‏‏‏‎‎‏‎‏‏‏‏‏‎‎‎‎‎‏‏‏‏‏‎‏‏‏‎‎Not now‎‏‎‎‏‎"</string>
    <string name="leave_accessibility_shortcut_on" msgid="6807632291651241490">"‎‏‎‎‎‎‎‏‎‏‏‏‎‎‎‎‎‏‏‏‏‎‏‎‎‏‎‏‏‏‏‏‏‏‎‏‏‏‏‎‎‏‏‏‏‎‎‏‏‎‎‏‎‎‎‏‏‎‎‎‎‎‎‎‎‎‏‏‏‎‎‏‎‏‎‏‏‎‎‏‏‏‎‎‎‏‏‎‎‎‎‏‎‎‏‎‎Turn on now‎‏‎‎‏‎"</string>
</resources>
