// Autogenerated by the ProtoZero compiler plugin. DO NOT EDIT.

#ifndef PERFETTO_PROTOS_TRACE_PACKET_PROTO_H_
#define PERFETTO_PROTOS_TRACE_PACKET_PROTO_H_

#include <stddef.h>
#include <stdint.h>

#include "perfetto/protozero/message.h"
#include "perfetto/protozero/packed_repeated_fields.h"
#include "perfetto/protozero/proto_decoder.h"
#include "perfetto/protozero/proto_utils.h"

namespace perfetto {
namespace protos {
namespace pbzero {

class AndroidLogPacket;
class BatteryCounters;
class ChromeBenchmarkMetadata;
class ChromeEventBundle;
class ChromeMetadataPacket;
class ClockSnapshot;
class CpuInfo;
class DeobfuscationMapping;
class ExtensionDescriptor;
class FtraceEventBundle;
class FtraceStats;
class GpuCounterEvent;
class GpuLog;
class GpuMemTotalEvent;
class GpuRenderStageEvent;
class GraphicsFrameEvent;
class HeapGraph;
class InitialDisplayState;
class InodeFileMap;
class InternedData;
class MemoryTrackerSnapshot;
class ModuleSymbols;
class PackagesList;
class PerfSample;
class PerfettoMetatrace;
class PowerRails;
class ProcessDescriptor;
class ProcessStats;
class ProcessTree;
class ProfilePacket;
class ProfiledFrameSymbols;
class SmapsPacket;
class StreamingProfilePacket;
class SysStats;
class SystemInfo;
class TestEvent;
class ThreadDescriptor;
class TraceConfig;
class TracePacketDefaults;
class TraceStats;
class TracingServiceEvent;
class TrackDescriptor;
class TrackEvent;
class Trigger;
class VulkanApiEvent;
class VulkanMemoryEvent;

enum TracePacket_SequenceFlags : int32_t {
  TracePacket_SequenceFlags_SEQ_UNSPECIFIED = 0,
  TracePacket_SequenceFlags_SEQ_INCREMENTAL_STATE_CLEARED = 1,
  TracePacket_SequenceFlags_SEQ_NEEDS_INCREMENTAL_STATE = 2,
};

const TracePacket_SequenceFlags TracePacket_SequenceFlags_MIN = TracePacket_SequenceFlags_SEQ_UNSPECIFIED;
const TracePacket_SequenceFlags TracePacket_SequenceFlags_MAX = TracePacket_SequenceFlags_SEQ_NEEDS_INCREMENTAL_STATE;

class TracePacket_Decoder : public ::protozero::TypedProtoDecoder</*MAX_FIELD_ID=*/900, /*HAS_NONPACKED_REPEATED_FIELDS=*/false> {
 public:
  TracePacket_Decoder(const uint8_t* data, size_t len) : TypedProtoDecoder(data, len) {}
  explicit TracePacket_Decoder(const std::string& raw) : TypedProtoDecoder(reinterpret_cast<const uint8_t*>(raw.data()), raw.size()) {}
  explicit TracePacket_Decoder(const ::protozero::ConstBytes& raw) : TypedProtoDecoder(raw.data, raw.size) {}
  bool has_timestamp() const { return at<8>().valid(); }
  uint64_t timestamp() const { return at<8>().as_uint64(); }
  bool has_timestamp_clock_id() const { return at<58>().valid(); }
  uint32_t timestamp_clock_id() const { return at<58>().as_uint32(); }
  bool has_process_tree() const { return at<2>().valid(); }
  ::protozero::ConstBytes process_tree() const { return at<2>().as_bytes(); }
  bool has_process_stats() const { return at<9>().valid(); }
  ::protozero::ConstBytes process_stats() const { return at<9>().as_bytes(); }
  bool has_inode_file_map() const { return at<4>().valid(); }
  ::protozero::ConstBytes inode_file_map() const { return at<4>().as_bytes(); }
  bool has_chrome_events() const { return at<5>().valid(); }
  ::protozero::ConstBytes chrome_events() const { return at<5>().as_bytes(); }
  bool has_clock_snapshot() const { return at<6>().valid(); }
  ::protozero::ConstBytes clock_snapshot() const { return at<6>().as_bytes(); }
  bool has_sys_stats() const { return at<7>().valid(); }
  ::protozero::ConstBytes sys_stats() const { return at<7>().as_bytes(); }
  bool has_track_event() const { return at<11>().valid(); }
  ::protozero::ConstBytes track_event() const { return at<11>().as_bytes(); }
  bool has_trace_config() const { return at<33>().valid(); }
  ::protozero::ConstBytes trace_config() const { return at<33>().as_bytes(); }
  bool has_ftrace_stats() const { return at<34>().valid(); }
  ::protozero::ConstBytes ftrace_stats() const { return at<34>().as_bytes(); }
  bool has_trace_stats() const { return at<35>().valid(); }
  ::protozero::ConstBytes trace_stats() const { return at<35>().as_bytes(); }
  bool has_profile_packet() const { return at<37>().valid(); }
  ::protozero::ConstBytes profile_packet() const { return at<37>().as_bytes(); }
  bool has_battery() const { return at<38>().valid(); }
  ::protozero::ConstBytes battery() const { return at<38>().as_bytes(); }
  bool has_power_rails() const { return at<40>().valid(); }
  ::protozero::ConstBytes power_rails() const { return at<40>().as_bytes(); }
  bool has_android_log() const { return at<39>().valid(); }
  ::protozero::ConstBytes android_log() const { return at<39>().as_bytes(); }
  bool has_system_info() const { return at<45>().valid(); }
  ::protozero::ConstBytes system_info() const { return at<45>().as_bytes(); }
  bool has_trigger() const { return at<46>().valid(); }
  ::protozero::ConstBytes trigger() const { return at<46>().as_bytes(); }
  bool has_packages_list() const { return at<47>().valid(); }
  ::protozero::ConstBytes packages_list() const { return at<47>().as_bytes(); }
  bool has_chrome_benchmark_metadata() const { return at<48>().valid(); }
  ::protozero::ConstBytes chrome_benchmark_metadata() const { return at<48>().as_bytes(); }
  bool has_perfetto_metatrace() const { return at<49>().valid(); }
  ::protozero::ConstBytes perfetto_metatrace() const { return at<49>().as_bytes(); }
  bool has_chrome_metadata() const { return at<51>().valid(); }
  ::protozero::ConstBytes chrome_metadata() const { return at<51>().as_bytes(); }
  bool has_gpu_counter_event() const { return at<52>().valid(); }
  ::protozero::ConstBytes gpu_counter_event() const { return at<52>().as_bytes(); }
  bool has_gpu_render_stage_event() const { return at<53>().valid(); }
  ::protozero::ConstBytes gpu_render_stage_event() const { return at<53>().as_bytes(); }
  bool has_streaming_profile_packet() const { return at<54>().valid(); }
  ::protozero::ConstBytes streaming_profile_packet() const { return at<54>().as_bytes(); }
  bool has_heap_graph() const { return at<56>().valid(); }
  ::protozero::ConstBytes heap_graph() const { return at<56>().as_bytes(); }
  bool has_graphics_frame_event() const { return at<57>().valid(); }
  ::protozero::ConstBytes graphics_frame_event() const { return at<57>().as_bytes(); }
  bool has_vulkan_memory_event() const { return at<62>().valid(); }
  ::protozero::ConstBytes vulkan_memory_event() const { return at<62>().as_bytes(); }
  bool has_gpu_log() const { return at<63>().valid(); }
  ::protozero::ConstBytes gpu_log() const { return at<63>().as_bytes(); }
  bool has_vulkan_api_event() const { return at<65>().valid(); }
  ::protozero::ConstBytes vulkan_api_event() const { return at<65>().as_bytes(); }
  bool has_perf_sample() const { return at<66>().valid(); }
  ::protozero::ConstBytes perf_sample() const { return at<66>().as_bytes(); }
  bool has_cpu_info() const { return at<67>().valid(); }
  ::protozero::ConstBytes cpu_info() const { return at<67>().as_bytes(); }
  bool has_smaps_packet() const { return at<68>().valid(); }
  ::protozero::ConstBytes smaps_packet() const { return at<68>().as_bytes(); }
  bool has_service_event() const { return at<69>().valid(); }
  ::protozero::ConstBytes service_event() const { return at<69>().as_bytes(); }
  bool has_initial_display_state() const { return at<70>().valid(); }
  ::protozero::ConstBytes initial_display_state() const { return at<70>().as_bytes(); }
  bool has_gpu_mem_total_event() const { return at<71>().valid(); }
  ::protozero::ConstBytes gpu_mem_total_event() const { return at<71>().as_bytes(); }
  bool has_memory_tracker_snapshot() const { return at<73>().valid(); }
  ::protozero::ConstBytes memory_tracker_snapshot() const { return at<73>().as_bytes(); }
  bool has_profiled_frame_symbols() const { return at<55>().valid(); }
  ::protozero::ConstBytes profiled_frame_symbols() const { return at<55>().as_bytes(); }
  bool has_module_symbols() const { return at<61>().valid(); }
  ::protozero::ConstBytes module_symbols() const { return at<61>().as_bytes(); }
  bool has_deobfuscation_mapping() const { return at<64>().valid(); }
  ::protozero::ConstBytes deobfuscation_mapping() const { return at<64>().as_bytes(); }
  bool has_track_descriptor() const { return at<60>().valid(); }
  ::protozero::ConstBytes track_descriptor() const { return at<60>().as_bytes(); }
  bool has_process_descriptor() const { return at<43>().valid(); }
  ::protozero::ConstBytes process_descriptor() const { return at<43>().as_bytes(); }
  bool has_thread_descriptor() const { return at<44>().valid(); }
  ::protozero::ConstBytes thread_descriptor() const { return at<44>().as_bytes(); }
  bool has_ftrace_events() const { return at<1>().valid(); }
  ::protozero::ConstBytes ftrace_events() const { return at<1>().as_bytes(); }
  bool has_synchronization_marker() const { return at<36>().valid(); }
  ::protozero::ConstBytes synchronization_marker() const { return at<36>().as_bytes(); }
  bool has_compressed_packets() const { return at<50>().valid(); }
  ::protozero::ConstBytes compressed_packets() const { return at<50>().as_bytes(); }
  bool has_extension_descriptor() const { return at<72>().valid(); }
  ::protozero::ConstBytes extension_descriptor() const { return at<72>().as_bytes(); }
  bool has_for_testing() const { return at<900>().valid(); }
  ::protozero::ConstBytes for_testing() const { return at<900>().as_bytes(); }
  bool has_trusted_uid() const { return at<3>().valid(); }
  int32_t trusted_uid() const { return at<3>().as_int32(); }
  bool has_trusted_packet_sequence_id() const { return at<10>().valid(); }
  uint32_t trusted_packet_sequence_id() const { return at<10>().as_uint32(); }
  bool has_interned_data() const { return at<12>().valid(); }
  ::protozero::ConstBytes interned_data() const { return at<12>().as_bytes(); }
  bool has_sequence_flags() const { return at<13>().valid(); }
  uint32_t sequence_flags() const { return at<13>().as_uint32(); }
  bool has_incremental_state_cleared() const { return at<41>().valid(); }
  bool incremental_state_cleared() const { return at<41>().as_bool(); }
  bool has_trace_packet_defaults() const { return at<59>().valid(); }
  ::protozero::ConstBytes trace_packet_defaults() const { return at<59>().as_bytes(); }
  bool has_previous_packet_dropped() const { return at<42>().valid(); }
  bool previous_packet_dropped() const { return at<42>().as_bool(); }
};

class TracePacket : public ::protozero::Message {
 public:
  using Decoder = TracePacket_Decoder;
  enum : int32_t {
    kTimestampFieldNumber = 8,
    kTimestampClockIdFieldNumber = 58,
    kProcessTreeFieldNumber = 2,
    kProcessStatsFieldNumber = 9,
    kInodeFileMapFieldNumber = 4,
    kChromeEventsFieldNumber = 5,
    kClockSnapshotFieldNumber = 6,
    kSysStatsFieldNumber = 7,
    kTrackEventFieldNumber = 11,
    kTraceConfigFieldNumber = 33,
    kFtraceStatsFieldNumber = 34,
    kTraceStatsFieldNumber = 35,
    kProfilePacketFieldNumber = 37,
    kBatteryFieldNumber = 38,
    kPowerRailsFieldNumber = 40,
    kAndroidLogFieldNumber = 39,
    kSystemInfoFieldNumber = 45,
    kTriggerFieldNumber = 46,
    kPackagesListFieldNumber = 47,
    kChromeBenchmarkMetadataFieldNumber = 48,
    kPerfettoMetatraceFieldNumber = 49,
    kChromeMetadataFieldNumber = 51,
    kGpuCounterEventFieldNumber = 52,
    kGpuRenderStageEventFieldNumber = 53,
    kStreamingProfilePacketFieldNumber = 54,
    kHeapGraphFieldNumber = 56,
    kGraphicsFrameEventFieldNumber = 57,
    kVulkanMemoryEventFieldNumber = 62,
    kGpuLogFieldNumber = 63,
    kVulkanApiEventFieldNumber = 65,
    kPerfSampleFieldNumber = 66,
    kCpuInfoFieldNumber = 67,
    kSmapsPacketFieldNumber = 68,
    kServiceEventFieldNumber = 69,
    kInitialDisplayStateFieldNumber = 70,
    kGpuMemTotalEventFieldNumber = 71,
    kMemoryTrackerSnapshotFieldNumber = 73,
    kProfiledFrameSymbolsFieldNumber = 55,
    kModuleSymbolsFieldNumber = 61,
    kDeobfuscationMappingFieldNumber = 64,
    kTrackDescriptorFieldNumber = 60,
    kProcessDescriptorFieldNumber = 43,
    kThreadDescriptorFieldNumber = 44,
    kFtraceEventsFieldNumber = 1,
    kSynchronizationMarkerFieldNumber = 36,
    kCompressedPacketsFieldNumber = 50,
    kExtensionDescriptorFieldNumber = 72,
    kForTestingFieldNumber = 900,
    kTrustedUidFieldNumber = 3,
    kTrustedPacketSequenceIdFieldNumber = 10,
    kInternedDataFieldNumber = 12,
    kSequenceFlagsFieldNumber = 13,
    kIncrementalStateClearedFieldNumber = 41,
    kTracePacketDefaultsFieldNumber = 59,
    kPreviousPacketDroppedFieldNumber = 42,
  };
  using SequenceFlags = ::perfetto::protos::pbzero::TracePacket_SequenceFlags;
  static const SequenceFlags SEQ_UNSPECIFIED = TracePacket_SequenceFlags_SEQ_UNSPECIFIED;
  static const SequenceFlags SEQ_INCREMENTAL_STATE_CLEARED = TracePacket_SequenceFlags_SEQ_INCREMENTAL_STATE_CLEARED;
  static const SequenceFlags SEQ_NEEDS_INCREMENTAL_STATE = TracePacket_SequenceFlags_SEQ_NEEDS_INCREMENTAL_STATE;
  void set_timestamp(uint64_t value) {
    AppendVarInt(8, value);
  }
  void set_timestamp_clock_id(uint32_t value) {
    AppendVarInt(58, value);
  }
  template <typename T = ProcessTree> T* set_process_tree() {
    return BeginNestedMessage<T>(2);
  }

  template <typename T = ProcessStats> T* set_process_stats() {
    return BeginNestedMessage<T>(9);
  }

  template <typename T = InodeFileMap> T* set_inode_file_map() {
    return BeginNestedMessage<T>(4);
  }

  template <typename T = ChromeEventBundle> T* set_chrome_events() {
    return BeginNestedMessage<T>(5);
  }

  template <typename T = ClockSnapshot> T* set_clock_snapshot() {
    return BeginNestedMessage<T>(6);
  }

  template <typename T = SysStats> T* set_sys_stats() {
    return BeginNestedMessage<T>(7);
  }

  template <typename T = TrackEvent> T* set_track_event() {
    return BeginNestedMessage<T>(11);
  }

  template <typename T = TraceConfig> T* set_trace_config() {
    return BeginNestedMessage<T>(33);
  }

  template <typename T = FtraceStats> T* set_ftrace_stats() {
    return BeginNestedMessage<T>(34);
  }

  template <typename T = TraceStats> T* set_trace_stats() {
    return BeginNestedMessage<T>(35);
  }

  template <typename T = ProfilePacket> T* set_profile_packet() {
    return BeginNestedMessage<T>(37);
  }

  template <typename T = BatteryCounters> T* set_battery() {
    return BeginNestedMessage<T>(38);
  }

  template <typename T = PowerRails> T* set_power_rails() {
    return BeginNestedMessage<T>(40);
  }

  template <typename T = AndroidLogPacket> T* set_android_log() {
    return BeginNestedMessage<T>(39);
  }

  template <typename T = SystemInfo> T* set_system_info() {
    return BeginNestedMessage<T>(45);
  }

  template <typename T = Trigger> T* set_trigger() {
    return BeginNestedMessage<T>(46);
  }

  template <typename T = PackagesList> T* set_packages_list() {
    return BeginNestedMessage<T>(47);
  }

  template <typename T = ChromeBenchmarkMetadata> T* set_chrome_benchmark_metadata() {
    return BeginNestedMessage<T>(48);
  }

  template <typename T = PerfettoMetatrace> T* set_perfetto_metatrace() {
    return BeginNestedMessage<T>(49);
  }

  template <typename T = ChromeMetadataPacket> T* set_chrome_metadata() {
    return BeginNestedMessage<T>(51);
  }

  template <typename T = GpuCounterEvent> T* set_gpu_counter_event() {
    return BeginNestedMessage<T>(52);
  }

  template <typename T = GpuRenderStageEvent> T* set_gpu_render_stage_event() {
    return BeginNestedMessage<T>(53);
  }

  template <typename T = StreamingProfilePacket> T* set_streaming_profile_packet() {
    return BeginNestedMessage<T>(54);
  }

  template <typename T = HeapGraph> T* set_heap_graph() {
    return BeginNestedMessage<T>(56);
  }

  template <typename T = GraphicsFrameEvent> T* set_graphics_frame_event() {
    return BeginNestedMessage<T>(57);
  }

  template <typename T = VulkanMemoryEvent> T* set_vulkan_memory_event() {
    return BeginNestedMessage<T>(62);
  }

  template <typename T = GpuLog> T* set_gpu_log() {
    return BeginNestedMessage<T>(63);
  }

  template <typename T = VulkanApiEvent> T* set_vulkan_api_event() {
    return BeginNestedMessage<T>(65);
  }

  template <typename T = PerfSample> T* set_perf_sample() {
    return BeginNestedMessage<T>(66);
  }

  template <typename T = CpuInfo> T* set_cpu_info() {
    return BeginNestedMessage<T>(67);
  }

  template <typename T = SmapsPacket> T* set_smaps_packet() {
    return BeginNestedMessage<T>(68);
  }

  template <typename T = TracingServiceEvent> T* set_service_event() {
    return BeginNestedMessage<T>(69);
  }

  template <typename T = InitialDisplayState> T* set_initial_display_state() {
    return BeginNestedMessage<T>(70);
  }

  template <typename T = GpuMemTotalEvent> T* set_gpu_mem_total_event() {
    return BeginNestedMessage<T>(71);
  }

  template <typename T = MemoryTrackerSnapshot> T* set_memory_tracker_snapshot() {
    return BeginNestedMessage<T>(73);
  }

  template <typename T = ProfiledFrameSymbols> T* set_profiled_frame_symbols() {
    return BeginNestedMessage<T>(55);
  }

  template <typename T = ModuleSymbols> T* set_module_symbols() {
    return BeginNestedMessage<T>(61);
  }

  template <typename T = DeobfuscationMapping> T* set_deobfuscation_mapping() {
    return BeginNestedMessage<T>(64);
  }

  template <typename T = TrackDescriptor> T* set_track_descriptor() {
    return BeginNestedMessage<T>(60);
  }

  template <typename T = ProcessDescriptor> T* set_process_descriptor() {
    return BeginNestedMessage<T>(43);
  }

  template <typename T = ThreadDescriptor> T* set_thread_descriptor() {
    return BeginNestedMessage<T>(44);
  }

  template <typename T = FtraceEventBundle> T* set_ftrace_events() {
    return BeginNestedMessage<T>(1);
  }

  void set_synchronization_marker(const std::string& value) {
    AppendBytes(36, value.data(), value.size());
  }
  void set_synchronization_marker(const uint8_t* data, size_t size) {
    AppendBytes(36, data, size);
  }
  void set_compressed_packets(const std::string& value) {
    AppendBytes(50, value.data(), value.size());
  }
  void set_compressed_packets(const uint8_t* data, size_t size) {
    AppendBytes(50, data, size);
  }
  template <typename T = ExtensionDescriptor> T* set_extension_descriptor() {
    return BeginNestedMessage<T>(72);
  }

  template <typename T = TestEvent> T* set_for_testing() {
    return BeginNestedMessage<T>(900);
  }

  void set_trusted_uid(int32_t value) {
    AppendVarInt(3, value);
  }
  void set_trusted_packet_sequence_id(uint32_t value) {
    AppendVarInt(10, value);
  }
  template <typename T = InternedData> T* set_interned_data() {
    return BeginNestedMessage<T>(12);
  }

  void set_sequence_flags(uint32_t value) {
    AppendVarInt(13, value);
  }
  void set_incremental_state_cleared(bool value) {
    AppendTinyVarInt(41, value);
  }
  template <typename T = TracePacketDefaults> T* set_trace_packet_defaults() {
    return BeginNestedMessage<T>(59);
  }

  void set_previous_packet_dropped(bool value) {
    AppendTinyVarInt(42, value);
  }
};

} // Namespace.
} // Namespace.
} // Namespace.
#endif  // Include guard.
