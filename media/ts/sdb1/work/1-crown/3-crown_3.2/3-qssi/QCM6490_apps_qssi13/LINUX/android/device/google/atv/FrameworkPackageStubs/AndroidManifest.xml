<?xml version="1.0" encoding="utf-8"?>
<manifest xmlns:android="http://schemas.android.com/apk/res/android"
    package="com.android.tv.frameworkpackagestubs"
    android:versionCode="1"
    android:versionName="1.0.1"
    >

    <uses-sdk
        android:minSdkVersion="26"
    />

    <uses-permission android:name="android.permission.QUERY_ALL_PACKAGES" />
    <uses-permission android:name="android.permission.SET_DEFAULT_ACCOUNT_FOR_CONTACTS"/>

    <application android:label="@string/app_name"
        android:theme="@android:style/Theme.Translucent.NoTitleBar.Fullscreen"
        >

        <!-- Browser stubs -->
        <activity android:name=".Stubs$BrowserStub"
            android:exported="true"
            android:label="@string/stub_name"
            android:excludeFromRecents="true"
            >

            <intent-filter android:priority="-1">
                <action android:name="android.intent.action.VIEW" />
                <category android:name="android.intent.category.BROWSABLE" />
                <category android:name="android.intent.category.DEFAULT" />
                <data android:scheme="http" />
                <data android:scheme="https" />
                <data android:scheme="file" />
            </intent-filter>

            <intent-filter android:priority="-1">
                <action android:name="android.speech.action.VOICE_SEARCH_RESULTS" />
                <category android:name="android.intent.category.DEFAULT" />
            </intent-filter>

            <!-- For viewing saved web archives. -->
            <intent-filter android:priority="-1">
                <action android:name="android.intent.action.VIEW" />
                <category android:name="android.intent.category.BROWSABLE" />
                <category android:name="android.intent.category.DEFAULT" />
                <data android:scheme="http" />
                <data android:scheme="https" />
                <data android:scheme="file" />
                <data android:mimeType="application/x-webarchive-xml"/>
            </intent-filter>

            <!-- For viewing PDF files, which we do not support in this release. -->
            <intent-filter android:priority="-1">
                <action android:name="android.intent.action.VIEW" />
                <category android:name="android.intent.category.BROWSABLE" />
                <category android:name="android.intent.category.DEFAULT" />
                <data android:scheme="http" />
                <data android:scheme="https" />
                <data android:scheme="file" />
                <data android:mimeType="application/pdf"/>
            </intent-filter>

            <intent-filter android:priority="-1">
                <action android:name="android.intent.action.WEB_SEARCH" />
                <category android:name="android.intent.category.DEFAULT" />
            </intent-filter>
        </activity>

        <provider android:name="com.android.browser.provider.BrowserProvider2"
                  android:exported="true"
                  android:authorities="com.android.browser;browser"
                  android:readPermission="com.android.browser.permission.READ_HISTORY_BOOKMARKS"
                  android:writePermission="com.android.browser.permission.WRITE_HISTORY_BOOKMARKS">
                  <!--
            <path-permission android:path="/bookmarks/search_suggest_query"
                    android:readPermission="android.permission.GLOBAL_SEARCH" />
                    -->
        </provider>

        <!-- Calendar package stubs -->
        <activity android:name=".Stubs$CalendarStub"
            android:exported="true"
            android:label="@string/stub_name"
            android:excludeFromRecents="true"
            >
            <meta-data android:name="com.android.tv.stub" android:value="true" />
            <intent-filter android:priority="-1">
                <action android:name="android.intent.action.VIEW" />
                <category android:name="android.intent.category.DEFAULT" />
                <data android:mimeType="time/epoch" />
                <data android:host="com.android.calendar" />
                <data android:scheme="content"/>
            </intent-filter>
            <intent-filter android:priority="-1">
                <action android:name="android.intent.action.VIEW" />
                <category android:name="android.intent.category.DEFAULT" />
                <data android:mimeType="vnd.android.cursor.item/event" />
            </intent-filter>
            <intent-filter android:priority="-1">
                <action android:name="android.intent.action.EDIT" />
                <category android:name="android.intent.category.DEFAULT" />
                <data android:mimeType="vnd.android.cursor.item/event" />
            </intent-filter>
                <intent-filter android:priority="-1">
                <action android:name="android.intent.action.EDIT" />
                <category android:name="android.intent.category.DEFAULT" />
                <data android:mimeType="vnd.android.cursor.dir/event" />
            </intent-filter>
        </activity>

        <!-- Contacts package stubs.

             Copied all intent filters from all activities
             (except when action was MAIN) in
             ../packages/apps/Contacts/AndroidManifest.xml

             -->
        <activity android:name=".Stubs$ContactsStub"
            android:exported="true"
            android:label="@string/stub_name"
            android:excludeFromRecents="true"
            android:visibleToInstantApps="true"
            >

            <meta-data android:name="com.android.tv.stub" android:value="true" />

            <intent-filter android:priority="-1">
                <action android:name="com.android.phone.action.TOUCH_DIALER" />
                <category android:name="android.intent.category.DEFAULT" />
                <category android:name="android.intent.category.TAB" />
            </intent-filter>

            <intent-filter android:priority="-1">
                <action android:name="com.android.phone.action.RECENT_CALLS" />
                <category android:name="android.intent.category.DEFAULT" />
                <category android:name="android.intent.category.TAB" />
            </intent-filter>

            <intent-filter android:priority="-1">
                <action android:name="android.intent.action.DIAL" />
                <category android:name="android.intent.category.DEFAULT" />
                <category android:name="android.intent.category.BROWSABLE" />
                <data android:mimeType="vnd.android.cursor.item/phone" />
                <data android:mimeType="vnd.android.cursor.item/person" />
            </intent-filter>

            <intent-filter android:priority="-1">
                <action android:name="android.intent.action.DIAL" />
                <category android:name="android.intent.category.DEFAULT" />
                <category android:name="android.intent.category.BROWSABLE" />
                <data android:scheme="voicemail" />
            </intent-filter>

            <intent-filter android:priority="-1">
                <action android:name="android.intent.action.DIAL" />
                <category android:name="android.intent.category.DEFAULT" />
            </intent-filter>

            <intent-filter android:priority="-1">
                <action android:name="android.intent.action.VIEW" />
                <action android:name="android.intent.action.DIAL" />
                <category android:name="android.intent.category.DEFAULT" />
                <category android:name="android.intent.category.BROWSABLE" />
                <data android:scheme="tel" />
            </intent-filter>

            <intent-filter android:priority="-1">
                <action android:name="android.intent.action.VIEW" />
                <category android:name="android.intent.category.DEFAULT" />
                <category android:name="android.intent.category.BROWSABLE" />
                <data android:mimeType="vnd.android.cursor.dir/calls" />
            </intent-filter>

            <intent-filter android:priority="-1">
                <action android:name="android.intent.action.CALL_BUTTON" />
                <category android:name="android.intent.category.DEFAULT" />
                <category android:name="android.intent.category.BROWSABLE" />
            </intent-filter>

            <intent-filter android:priority="-1">
                <action android:name="android.intent.action.DIAL" />
                <category android:name="android.intent.category.DEFAULT" />
                <category android:name="android.intent.category.BROWSABLE" />
                <data android:mimeType="vnd.android.cursor.item/phone" />
                <data android:mimeType="vnd.android.cursor.item/person" />
            </intent-filter>

            <intent-filter android:priority="-1">
                <action android:name="android.intent.action.DIAL" />
                <category android:name="android.intent.category.DEFAULT" />
                <category android:name="android.intent.category.BROWSABLE" />
                <data android:scheme="voicemail" />
            </intent-filter>

            <intent-filter android:priority="-1">
                <action android:name="android.intent.action.DIAL" />
                <category android:name="android.intent.category.DEFAULT" />
            </intent-filter>

            <intent-filter android:priority="-1">
                <action android:name="android.intent.action.VIEW" />
                <action android:name="android.intent.action.DIAL" />
                <category android:name="android.intent.category.DEFAULT" />
                <category android:name="android.intent.category.BROWSABLE" />
                <data android:scheme="tel" />
            </intent-filter>

            <intent-filter android:priority="-1">
                <action android:name="android.intent.action.VIEW" />
                <category android:name="android.intent.category.DEFAULT" />
                <category android:name="android.intent.category.BROWSABLE" />
                <data android:mimeType="vnd.android.cursor.dir/calls" />
            </intent-filter>

            <intent-filter android:priority="-1">
                <action android:name="android.intent.action.CALL_BUTTON" />
                <category android:name="android.intent.category.DEFAULT" />
                <category android:name="android.intent.category.BROWSABLE" />
            </intent-filter>

            <intent-filter android:priority="-1">
                <action android:name="android.intent.action.VIEW" />
                <category android:name="android.intent.category.DEFAULT" />
                <data android:mimeType="vnd.android.cursor.dir/person" />
                <data android:mimeType="vnd.android.cursor.dir/contact" />
                <data android:mimeType="vnd.android.cursor.item/person" />
                <data android:mimeType="vnd.android.cursor.item/contact" />
                <data android:mimeType="vnd.android.cursor.item/raw_contact" />
            </intent-filter>

            <intent-filter android:priority="-1">
                <action android:name="com.android.contacts.action.LIST_DEFAULT" />
                <category android:name="android.intent.category.DEFAULT" />
                <category android:name="android.intent.category.TAB" />
            </intent-filter>

            <intent-filter android:priority="-1">
                <action android:name="com.android.contacts.action.LIST_CONTACTS" />
                <category android:name="android.intent.category.DEFAULT" />
                <category android:name="android.intent.category.TAB" />
            </intent-filter>

            <intent-filter android:priority="-1">
                <action android:name="com.android.contacts.action.LIST_ALL_CONTACTS" />
                <category android:name="android.intent.category.DEFAULT" />
                <category android:name="android.intent.category.TAB" />
            </intent-filter>

            <intent-filter android:priority="-1">
                <action android:name="com.android.contacts.action.LIST_CONTACTS_WITH_PHONES" />
                <category android:name="android.intent.category.DEFAULT" />
                <category android:name="android.intent.category.TAB" />
            </intent-filter>

            <intent-filter android:priority="-1">
                <action android:name="com.android.contacts.action.LIST_STARRED" />
                <category android:name="android.intent.category.DEFAULT" />
                <category android:name="android.intent.category.TAB" />
            </intent-filter>

            <intent-filter android:priority="-1">
                <action android:name="com.android.contacts.action.LIST_FREQUENT" />
                <category android:name="android.intent.category.DEFAULT" />
                <category android:name="android.intent.category.TAB" />
            </intent-filter>

            <intent-filter android:priority="-1">
                <action android:name="com.android.contacts.action.LIST_STREQUENT" />
                <category android:name="android.intent.category.DEFAULT" />
                <category android:name="android.intent.category.TAB" />
            </intent-filter>

            <intent-filter android:priority="-1">
                <action android:name="android.intent.action.SEARCH" />
                <category android:name="android.intent.category.DEFAULT" />
                <data android:mimeType="vnd.android.cursor.dir/contact" />
            </intent-filter>

            <intent-filter android:priority="-1">
                <action android:name="android.intent.action.SEARCH" />
                <category android:name="android.intent.category.DEFAULT" />
            </intent-filter>

            <intent-filter android:priority="-1">
                <action android:name="com.android.contacts.action.FILTER_CONTACTS" />
                <category android:name="android.intent.category.DEFAULT" />
                <data android:mimeType="vnd.android.cursor.dir/contact" />
            </intent-filter>

            <intent-filter android:priority="-1">
                <action android:name="android.intent.action.INSERT_OR_EDIT" />
                <category android:name="android.intent.category.DEFAULT" />
                <data android:mimeType="vnd.android.cursor.item/person" />
                <data android:mimeType="vnd.android.cursor.item/contact" />
                <data android:mimeType="vnd.android.cursor.item/raw_contact" />
            </intent-filter>

            <intent-filter android:priority="-1">
                <action android:name="android.intent.action.PICK" />
                <category android:name="android.intent.category.DEFAULT" />
                <data android:mimeType="vnd.android.cursor.dir/contact" />
                <data android:mimeType="vnd.android.cursor.dir/person" />
                <data android:mimeType="vnd.android.cursor.dir/phone_v2" />
                <data android:mimeType="vnd.android.cursor.dir/phone" />
                <data android:mimeType="vnd.android.cursor.dir/postal-address_v2" />
                <data android:mimeType="vnd.android.cursor.dir/postal-address" />
            </intent-filter>

            <intent-filter android:priority="-1">
                <action android:name="android.intent.action.GET_CONTENT" />
                <category android:name="android.intent.category.DEFAULT" />
                <data android:mimeType="vnd.android.cursor.item/contact" />
                <data android:mimeType="vnd.android.cursor.item/person" />
                <data android:mimeType="vnd.android.cursor.item/phone_v2" />
                <data android:mimeType="vnd.android.cursor.item/phone" />
                <data android:mimeType="vnd.android.cursor.item/postal-address_v2" />
                <data android:mimeType="vnd.android.cursor.item/postal-address" />
            </intent-filter>

            <intent-filter android:priority="-1">
                <action android:name="com.android.contacts.action.JOIN_CONTACT" />
                <category android:name="android.intent.category.DEFAULT" />
            </intent-filter>

            <intent-filter android:priority="-1">
                <action android:name="com.android.contacts.action.SHOW_OR_CREATE_CONTACT" />
                <category android:name="android.intent.category.DEFAULT" />
                <data android:scheme="mailto" />
                <data android:scheme="tel" />
            </intent-filter>

            <intent-filter android:priority="-1">
                <action android:name="com.android.contacts.action.QUICK_CONTACT" />
                <category android:name="android.intent.category.DEFAULT" />
                <data android:mimeType="vnd.android.cursor.item/contact" />
                <data android:mimeType="vnd.android.cursor.item/person" />
            </intent-filter>

            <intent-filter android:priority="-1">
                <action android:name="android.intent.action.VIEW" />
                <category android:name="android.intent.category.DEFAULT" />
                <data android:mimeType="vnd.android.cursor.item/calls" />
            </intent-filter>

            <intent-filter android:priority="-1">
                <action android:name="android.intent.action.EDIT" />
                <category android:name="android.intent.category.DEFAULT" />
                <data android:mimeType="vnd.android.cursor.item/person" />
                <data android:mimeType="vnd.android.cursor.item/contact" />
                <data android:mimeType="vnd.android.cursor.item/raw_contact" />
            </intent-filter>

            <intent-filter android:priority="-1">
                <action android:name="android.intent.action.INSERT" />
                <category android:name="android.intent.category.DEFAULT" />
                <data android:mimeType="vnd.android.cursor.dir/person" />
                <data android:mimeType="vnd.android.cursor.dir/contact" />
                <data android:mimeType="vnd.android.cursor.dir/raw_contact" />
            </intent-filter>

            <intent-filter android:priority="-1">
                <action android:name="android.intent.action.VIEW" />
                <data android:mimeType="text/directory" />
                <data android:mimeType="text/vcard" />
                <data android:mimeType="text/x-vcard" />
                <category android:name="android.intent.category.DEFAULT" />
            </intent-filter>

            <intent-filter android:priority="-1">
                <action android:name="android.provider.action.SET_DEFAULT_ACCOUNT"/>
                <category android:name="android.intent.category.DEFAULT"/>
            </intent-filter>

        </activity>

        <!-- Email package stubs.

             Copied all intent filters from all activities
             (except when action was MAIN) in
             ../packages/apps/Email/AndroidManifest.xml

             -->
        <activity android:name=".Stubs$EmailStub"
            android:exported="true"
            android:label="@string/stub_name"
            android:excludeFromRecents="true"
            >

            <meta-data android:name="com.android.tv.stub" android:value="true" />

            <intent-filter android:priority="-1">
                <action
                    android:name="com.android.email.CREATE_ACCOUNT" />
                <category
                    android:name="android.intent.category.DEFAULT" />
            </intent-filter>

            <intent-filter android:priority="-1">
                <action
                    android:name="com.android.email.activity.setup.ACCOUNT_MANAGER_ENTRY" />
                <category
                    android:name="android.intent.category.DEFAULT" />
            </intent-filter>

            <intent-filter android:priority="-1">
                <action
                    android:name="android.intent.action.VIEW" />
                <data
                    android:mimeType="application/eml" />
                <data
                    android:mimeType="message/rfc822" />
                <category
                    android:name="android.intent.category.DEFAULT" />
            </intent-filter>

            <intent-filter android:priority="-1">
                <action
                    android:name="android.intent.action.VIEW" />
                <action
                    android:name="android.intent.action.SENDTO" />
                <data
                    android:scheme="mailto" />
                <category
                    android:name="android.intent.category.DEFAULT" />
                <category
                    android:name="android.intent.category.BROWSABLE" />
            </intent-filter>

            <intent-filter android:priority="-1">
                <action
                    android:name="android.intent.action.SEND" />
                <data
                    android:mimeType="*/*" />
                <category
                    android:name="android.intent.category.DEFAULT" />
            </intent-filter>

            <intent-filter android:priority="-1">
                <action
                    android:name="android.intent.action.SEND_MULTIPLE" />
                <data
                    android:mimeType="*/*" />
                <category
                    android:name="android.intent.category.DEFAULT" />
            </intent-filter>

                <intent-filter android:priority="-1">
                <action
                    android:name="com.android.email.intent.action.REPLY" />
            </intent-filter>

        </activity>

        <!-- Documents package stubs.

             Copied all intent filters from all activities
             (except when action was MAIN) in
             ../packages/apps/DocumentsUi/AndroidManifest.xml

             -->
        <activity android:name=".Stubs$DocumentsStub"
            android:exported="true"
            android:label="@string/stub_name"
            android:excludeFromRecents="true"
            android:visibleToInstantApps="true"
        >
            <intent-filter android:priority="99">
                <action android:name="android.intent.action.CREATE_DOCUMENT" />
                <category android:name="android.intent.category.DEFAULT" />
                <category android:name="android.intent.category.OPENABLE" />
                <data android:mimeType="*/*" />
            </intent-filter>
            <intent-filter android:priority="99">
                <action android:name="android.intent.action.GET_CONTENT" />
                <category android:name="android.intent.category.DEFAULT" />
                <category android:name="android.intent.category.OPENABLE" />
                <data android:mimeType="*/*" />
            </intent-filter>
            <intent-filter>
                <action android:name="android.provider.action.MANAGE_ROOT" />
                <category android:name="android.intent.category.DEFAULT" />
                <data android:mimeType="vnd.android.document/root" />
            </intent-filter>
            <intent-filter android:priority="99">
                <action android:name="android.intent.action.OPEN_DOCUMENT" />
                <category android:name="android.intent.category.DEFAULT" />
                <category android:name="android.intent.category.OPENABLE" />
                <data android:mimeType="*/*" />
            </intent-filter>
            <intent-filter android:priority="99">
                <action android:name="android.intent.action.OPEN_DOCUMENT_TREE" />
                <category android:name="android.intent.category.DEFAULT" />
            </intent-filter>
        </activity>

        <!-- Music / media stubs. -->
        <activity android:name=".Stubs$MediaStub"
            android:exported="true"
            android:label="@string/stub_name"
            android:excludeFromRecents="true"
            >
            <intent-filter android:priority="-1">
                <action android:name="android.intent.action.VIEW" />
                <category android:name="android.intent.category.DEFAULT" />
                <data android:scheme="http" />
                <data android:scheme="https" />
                <data android:scheme="content" />
                <data android:scheme="file" />
                <data android:mimeType="audio/*"/>
                <data android:mimeType="application/ogg"/>
                <data android:mimeType="application/x-ogg"/>
                <data android:mimeType="application/itunes"/>
            </intent-filter>
            <intent-filter android:priority="-1">
                <action android:name="android.intent.action.VIEW" />
                <category android:name="android.intent.category.DEFAULT" />
                <category android:name="android.intent.category.BROWSABLE" />
                <data android:scheme="http" />
                <data android:scheme="https" />
                <data android:scheme="content" />
                <data android:scheme="file" />
                <data android:mimeType="video/mpeg4" />
                <data android:mimeType="video/mp4" />
                <data android:mimeType="video/3gp" />
                <data android:mimeType="video/3gpp" />
                <data android:mimeType="video/3gpp2" />
                <data android:mimeType="video/webm" />
                <data android:mimeType="video/avi" />
                <data android:mimeType="application/sdp" />
            </intent-filter>
            <intent-filter android:priority="-1">
                <action android:name="android.intent.action.VIEW" />
                <category android:name="android.intent.category.DEFAULT" />
                <category android:name="android.intent.category.BROWSABLE" />
                <data android:scheme="" />
                <data android:scheme="http" />
                <data android:scheme="https" />
                <data android:scheme="content" />
                <data android:scheme="file" />
                <data android:mimeType="image/*" />
            </intent-filter>
            <intent-filter android:priority="-1">
                <action android:name="android.intent.action.PICK" />
                <action android:name="android.intent.action.VIEW" />
                <category android:name="android.intent.category.DEFAULT" />
                <data android:mimeType="vnd.android.cursor.dir/image" />
            </intent-filter>
            <intent-filter android:priority="-1">
                <action android:name="android.intent.action.PICK" />
                <category android:name="android.intent.category.DEFAULT" />
                <data android:mimeType="vnd.android.cursor.dir/artistalbum"/>
            </intent-filter>
            <intent-filter android:priority="-1">
                <action android:name="android.intent.action.PICK" />
                <category android:name="android.intent.category.DEFAULT" />
                <data android:mimeType="vnd.android.cursor.dir/album"/>
            </intent-filter>
            <intent-filter android:priority="-1">
                <action android:name="android.intent.action.PICK" />
                <category android:name="android.intent.category.DEFAULT" />
                <data android:mimeType="vnd.android.cursor.dir/nowplaying"/>
            </intent-filter>
            <intent-filter android:priority="-1">
                <action android:name="android.intent.action.EDIT" />
                <action android:name="android.intent.action.PICK" />
                <category android:name="android.intent.category.DEFAULT" />
                <data android:mimeType="vnd.android.cursor.dir/track"/>
            </intent-filter>
            <intent-filter android:priority="-1">
                <action android:name="android.intent.action.SEARCH" />
                <action android:name="android.intent.action.MEDIA_SEARCH" />
                <category android:name="android.intent.category.DEFAULT" />
            </intent-filter>
            <intent-filter android:priority="-1">
                <action android:name="android.intent.action.PICK" />
                <category android:name="android.intent.category.DEFAULT" />
                <data android:mimeType="vnd.android.cursor.dir/playlist"/>
            </intent-filter>
            <intent-filter android:priority="-1">
                <action android:name="android.intent.action.VIEW" />
                <category android:name="android.intent.category.DEFAULT" />
                <data android:mimeType="vnd.android.cursor.dir/playlist"/>
            </intent-filter>
            <intent-filter android:priority="-1">
                <action android:name="android.intent.action.PICK" />
                <action android:name="android.intent.action.VIEW" />
                <category android:name="android.intent.category.DEFAULT" />
                <data android:mimeType="vnd.android.cursor.dir/video"/>
            </intent-filter>
            <intent-filter android:priority="-1">
                <action android:name="android.intent.action.GET_CONTENT" />
                <category android:name="android.intent.category.DEFAULT" />
                <category android:name="android.intent.category.OPENABLE" />
                <data android:mimeType="audio/*"/>
                <data android:mimeType="application/ogg"/>
                <data android:mimeType="application/x-ogg"/>
            </intent-filter>
            <intent-filter android:priority="-1">
                <action android:name="android.intent.action.PICK" />
                <category android:name="android.intent.category.DEFAULT" />
                <category android:name="android.intent.category.OPENABLE" />
                <data android:mimeType="vnd.android.cursor.dir/audio"/>
            </intent-filter>
            <!-- Ringtone stub -->
            <intent-filter android:priority="-1">
                <action android:name="android.intent.action.RINGTONE_PICKER" />
                <category android:name="android.intent.category.DEFAULT" />
            </intent-filter>
            <!-- Downloads -->
            <intent-filter android:priority="-1">
                <action android:name="android.intent.action.VIEW_DOWNLOADS" />
                <category android:name="android.intent.category.DEFAULT" />
            </intent-filter>
        </activity>

        <!-- Settings package stubs. -->
        <activity android:name=".Stubs$SettingsStub"
            android:exported="true"
            android:label="@string/stub_name"
            android:excludeFromRecents="true"
            >
            <intent-filter android:priority="-1">
                <action android:name="android.settings.ACCESSIBILITY_SETTINGS" />
                <category android:name="android.intent.category.DEFAULT" />
            </intent-filter>
            <intent-filter android:priority="-1">
                <action android:name="android.settings.USER_DICTIONARY_SETTINGS" />
                <category android:name="android.intent.category.DEFAULT" />
            </intent-filter>
            <intent-filter android:priority="-1">
                <action android:name="android.settings.ENTERPRISE_PRIVACY_SETTINGS" />
                <category android:name="android.intent.category.DEFAULT" />
            </intent-filter>
            <intent-filter android:priority="-1">
                <action android:name="android.settings.REQUEST_MANAGE_MEDIA" />
                <category android:name="android.intent.category.DEFAULT" />
            </intent-filter>
        </activity>

        <activity android:name=".Stubs$SettingsPrivacyStub"
            android:exported="true"
            android:label="@string/stub_name"
            android:excludeFromRecents="true"
            >
            <intent-filter android:priority="-1">
                <action android:name="android.settings.PRIVACY_SETTINGS" />
                <category android:name="android.intent.category.DEFAULT" />
                <category android:name="android.intent.category.VOICE_LAUNCH" />
            </intent-filter>
        </activity>

        <!-- Clock package stubs. -->
        <activity android:name=".Stub$ClockStub"
            android:exported="true"
            android:label="@string/stub_name"
            android:excludeFromRecents="true"
            >
            <intent-filter android:priority="-1">
                <action android:name="android.intent.action.DISMISS_ALARM" />
                <category android:name="android.intent.category.DEFAULT" />
            </intent-filter>
            <intent-filter android:priority="-1">
                <action android:name="android.intent.action.SET_ALARM" />
                <category android:name="android.intent.category.DEFAULT" />
            </intent-filter>
            <intent-filter android:priority="-1">
                <action android:name="android.intent.action.SHOW_ALARMS" />
                <category android:name="android.intent.category.DEFAULT" />
            </intent-filter>
            <intent-filter android:priority="-1">
                <action android:name="android.intent.action.SET_TIMER" />
                <category android:name="android.intent.category.DEFAULT" />
            </intent-filter>
            <intent-filter android:priority="-1">
                <action android:name="android.intent.action.SNOOZE_ALARM" />
                <category android:name="android.intent.category.DEFAULT" />
            </intent-filter>
        </activity>
    </application>

</manifest>
