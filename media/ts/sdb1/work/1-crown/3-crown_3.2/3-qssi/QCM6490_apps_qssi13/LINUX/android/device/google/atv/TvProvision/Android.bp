package {
    // See: http://go/android-license-faq
    // A large-scale-change added 'default_applicable_licenses' to import
    // all of the 'license_kinds' from "device_google_atv_license"
    // to get the below license kinds:
    //   SPDX-license-identifier-Apache-2.0
    default_applicable_licenses: ["device_google_atv_license"],
}

prebuilt_etc {
    name: "privapp_whitelist_com.android.tv.provision",
    product_specific: true,
    sub_dir: "permissions",
    src: "com.android.tv.provision.xml",
    filename_from_src: true,
}

android_app {
    name: "TvProvision",
    srcs: ["**/*.java"],
    product_specific: true,
    sdk_version: "system_current",
    certificate: "platform",
    privileged: true,
    overrides: ["SdkSetup"],
    required: ["privapp_whitelist_com.android.tv.provision"],
    optimize: {
        proguard_flags_files: ["proguard.flags"],
    },
}
