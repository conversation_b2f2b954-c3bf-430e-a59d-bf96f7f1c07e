<?xml version="1.0" encoding="utf-8"?>
<!--
  ~ Copyright (C) 2021 The Android Open Source Project
  ~
  ~ Licensed under the Apache License, Version 2.0 (the "License");
  ~ you may not use this file except in compliance with the License.
  ~ You may obtain a copy of the License at
  ~
  ~      http://www.apache.org/licenses/LICENSE-2.0
  ~
  ~ Unless required by applicable law or agreed to in writing, software
  ~ distributed under the License is distributed on an "AS IS" BASIS,
  ~ WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
  ~ See the License for the specific language governing permissions and
  ~ limitations under the License.
  -->

<FrameLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="@dimen/lb_preference_category_height_compact"
    android:clipToPadding="false"
    android:paddingStart="@dimen/lb_preference_item_padding_start"
    android:paddingEnd="@dimen/lb_preference_item_padding_end">

  <TextView
      android:id="@android:id/title"
      android:layout_width="wrap_content"
      android:layout_height="wrap_content"
      android:layout_gravity="start|center_vertical"
      android:fontFamily="sans-serif-medium"
      android:textColor="?android:attr/colorAccent"
      android:textSize="@dimen/lb_preference_category_text_size"/>
</FrameLayout>