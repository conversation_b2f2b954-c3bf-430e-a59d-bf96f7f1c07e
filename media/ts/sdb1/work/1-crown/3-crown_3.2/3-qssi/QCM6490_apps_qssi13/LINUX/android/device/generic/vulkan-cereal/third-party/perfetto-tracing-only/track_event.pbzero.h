// Autogenerated by the ProtoZero compiler plugin. DO NOT EDIT.

#ifndef PERFETTO_PROTOS_TRACK_EVENT_PROTO_H_
#define PERFETTO_PROTOS_TRACK_EVENT_PROTO_H_

#include <stddef.h>
#include <stdint.h>

#include "perfetto/protozero/message.h"
#include "perfetto/protozero/packed_repeated_fields.h"
#include "perfetto/protozero/proto_decoder.h"
#include "perfetto/protozero/proto_utils.h"

namespace perfetto {
namespace protos {
namespace pbzero {

class ChromeCompositorSchedulerState;
class ChromeFrameReporter;
class ChromeHistogramSample;
class ChromeKeyedService;
class ChromeLatencyInfo;
class ChromeLegacyIpc;
class ChromeMessagePump;
class ChromeUserEvent;
class DebugAnnotation;
class LogMessage;
class SourceLocation;
class TaskExecution;
class TrackEvent_LegacyEvent;
enum TrackEvent_LegacyEvent_FlowDirection : int32_t;
enum TrackEvent_LegacyEvent_InstantEventScope : int32_t;
enum TrackEvent_Type : int32_t;

enum TrackEvent_Type : int32_t {
  TrackEvent_Type_TYPE_UNSPECIFIED = 0,
  TrackEvent_Type_TYPE_SLICE_BEGIN = 1,
  TrackEvent_Type_TYPE_SLICE_END = 2,
  TrackEvent_Type_TYPE_INSTANT = 3,
  TrackEvent_Type_TYPE_COUNTER = 4,
};

const TrackEvent_Type TrackEvent_Type_MIN = TrackEvent_Type_TYPE_UNSPECIFIED;
const TrackEvent_Type TrackEvent_Type_MAX = TrackEvent_Type_TYPE_COUNTER;

enum TrackEvent_LegacyEvent_FlowDirection : int32_t {
  TrackEvent_LegacyEvent_FlowDirection_FLOW_UNSPECIFIED = 0,
  TrackEvent_LegacyEvent_FlowDirection_FLOW_IN = 1,
  TrackEvent_LegacyEvent_FlowDirection_FLOW_OUT = 2,
  TrackEvent_LegacyEvent_FlowDirection_FLOW_INOUT = 3,
};

const TrackEvent_LegacyEvent_FlowDirection TrackEvent_LegacyEvent_FlowDirection_MIN = TrackEvent_LegacyEvent_FlowDirection_FLOW_UNSPECIFIED;
const TrackEvent_LegacyEvent_FlowDirection TrackEvent_LegacyEvent_FlowDirection_MAX = TrackEvent_LegacyEvent_FlowDirection_FLOW_INOUT;

enum TrackEvent_LegacyEvent_InstantEventScope : int32_t {
  TrackEvent_LegacyEvent_InstantEventScope_SCOPE_UNSPECIFIED = 0,
  TrackEvent_LegacyEvent_InstantEventScope_SCOPE_GLOBAL = 1,
  TrackEvent_LegacyEvent_InstantEventScope_SCOPE_PROCESS = 2,
  TrackEvent_LegacyEvent_InstantEventScope_SCOPE_THREAD = 3,
};

const TrackEvent_LegacyEvent_InstantEventScope TrackEvent_LegacyEvent_InstantEventScope_MIN = TrackEvent_LegacyEvent_InstantEventScope_SCOPE_UNSPECIFIED;
const TrackEvent_LegacyEvent_InstantEventScope TrackEvent_LegacyEvent_InstantEventScope_MAX = TrackEvent_LegacyEvent_InstantEventScope_SCOPE_THREAD;

class EventName_Decoder : public ::protozero::TypedProtoDecoder</*MAX_FIELD_ID=*/2, /*HAS_NONPACKED_REPEATED_FIELDS=*/false> {
 public:
  EventName_Decoder(const uint8_t* data, size_t len) : TypedProtoDecoder(data, len) {}
  explicit EventName_Decoder(const std::string& raw) : TypedProtoDecoder(reinterpret_cast<const uint8_t*>(raw.data()), raw.size()) {}
  explicit EventName_Decoder(const ::protozero::ConstBytes& raw) : TypedProtoDecoder(raw.data, raw.size) {}
  bool has_iid() const { return at<1>().valid(); }
  uint64_t iid() const { return at<1>().as_uint64(); }
  bool has_name() const { return at<2>().valid(); }
  ::protozero::ConstChars name() const { return at<2>().as_string(); }
};

class EventName : public ::protozero::Message {
 public:
  using Decoder = EventName_Decoder;
  enum : int32_t {
    kIidFieldNumber = 1,
    kNameFieldNumber = 2,
  };
  void set_iid(uint64_t value) {
    AppendVarInt(1, value);
  }
  void set_name(const std::string& value) {
    AppendBytes(2, value.data(), value.size());
  }
  void set_name(const char* data, size_t size) {
    AppendBytes(2, data, size);
  }
};

class EventCategory_Decoder : public ::protozero::TypedProtoDecoder</*MAX_FIELD_ID=*/2, /*HAS_NONPACKED_REPEATED_FIELDS=*/false> {
 public:
  EventCategory_Decoder(const uint8_t* data, size_t len) : TypedProtoDecoder(data, len) {}
  explicit EventCategory_Decoder(const std::string& raw) : TypedProtoDecoder(reinterpret_cast<const uint8_t*>(raw.data()), raw.size()) {}
  explicit EventCategory_Decoder(const ::protozero::ConstBytes& raw) : TypedProtoDecoder(raw.data, raw.size) {}
  bool has_iid() const { return at<1>().valid(); }
  uint64_t iid() const { return at<1>().as_uint64(); }
  bool has_name() const { return at<2>().valid(); }
  ::protozero::ConstChars name() const { return at<2>().as_string(); }
};

class EventCategory : public ::protozero::Message {
 public:
  using Decoder = EventCategory_Decoder;
  enum : int32_t {
    kIidFieldNumber = 1,
    kNameFieldNumber = 2,
  };
  void set_iid(uint64_t value) {
    AppendVarInt(1, value);
  }
  void set_name(const std::string& value) {
    AppendBytes(2, value.data(), value.size());
  }
  void set_name(const char* data, size_t size) {
    AppendBytes(2, data, size);
  }
};

class TrackEventDefaults_Decoder : public ::protozero::TypedProtoDecoder</*MAX_FIELD_ID=*/31, /*HAS_NONPACKED_REPEATED_FIELDS=*/true> {
 public:
  TrackEventDefaults_Decoder(const uint8_t* data, size_t len) : TypedProtoDecoder(data, len) {}
  explicit TrackEventDefaults_Decoder(const std::string& raw) : TypedProtoDecoder(reinterpret_cast<const uint8_t*>(raw.data()), raw.size()) {}
  explicit TrackEventDefaults_Decoder(const ::protozero::ConstBytes& raw) : TypedProtoDecoder(raw.data, raw.size) {}
  bool has_track_uuid() const { return at<11>().valid(); }
  uint64_t track_uuid() const { return at<11>().as_uint64(); }
  bool has_extra_counter_track_uuids() const { return at<31>().valid(); }
  ::protozero::RepeatedFieldIterator<uint64_t> extra_counter_track_uuids() const { return GetRepeated<uint64_t>(31); }
};

class TrackEventDefaults : public ::protozero::Message {
 public:
  using Decoder = TrackEventDefaults_Decoder;
  enum : int32_t {
    kTrackUuidFieldNumber = 11,
    kExtraCounterTrackUuidsFieldNumber = 31,
  };
  void set_track_uuid(uint64_t value) {
    AppendVarInt(11, value);
  }
  void add_extra_counter_track_uuids(uint64_t value) {
    AppendVarInt(31, value);
  }
};

class TrackEvent_Decoder : public ::protozero::TypedProtoDecoder</*MAX_FIELD_ID=*/35, /*HAS_NONPACKED_REPEATED_FIELDS=*/true> {
 public:
  TrackEvent_Decoder(const uint8_t* data, size_t len) : TypedProtoDecoder(data, len) {}
  explicit TrackEvent_Decoder(const std::string& raw) : TypedProtoDecoder(reinterpret_cast<const uint8_t*>(raw.data()), raw.size()) {}
  explicit TrackEvent_Decoder(const ::protozero::ConstBytes& raw) : TypedProtoDecoder(raw.data, raw.size) {}
  bool has_category_iids() const { return at<3>().valid(); }
  ::protozero::RepeatedFieldIterator<uint64_t> category_iids() const { return GetRepeated<uint64_t>(3); }
  bool has_categories() const { return at<22>().valid(); }
  ::protozero::RepeatedFieldIterator<::protozero::ConstChars> categories() const { return GetRepeated<::protozero::ConstChars>(22); }
  bool has_name_iid() const { return at<10>().valid(); }
  uint64_t name_iid() const { return at<10>().as_uint64(); }
  bool has_name() const { return at<23>().valid(); }
  ::protozero::ConstChars name() const { return at<23>().as_string(); }
  bool has_type() const { return at<9>().valid(); }
  int32_t type() const { return at<9>().as_int32(); }
  bool has_track_uuid() const { return at<11>().valid(); }
  uint64_t track_uuid() const { return at<11>().as_uint64(); }
  bool has_counter_value() const { return at<30>().valid(); }
  int64_t counter_value() const { return at<30>().as_int64(); }
  bool has_extra_counter_track_uuids() const { return at<31>().valid(); }
  ::protozero::RepeatedFieldIterator<uint64_t> extra_counter_track_uuids() const { return GetRepeated<uint64_t>(31); }
  bool has_extra_counter_values() const { return at<12>().valid(); }
  ::protozero::RepeatedFieldIterator<int64_t> extra_counter_values() const { return GetRepeated<int64_t>(12); }
  bool has_debug_annotations() const { return at<4>().valid(); }
  ::protozero::RepeatedFieldIterator<::protozero::ConstBytes> debug_annotations() const { return GetRepeated<::protozero::ConstBytes>(4); }
  bool has_task_execution() const { return at<5>().valid(); }
  ::protozero::ConstBytes task_execution() const { return at<5>().as_bytes(); }
  bool has_log_message() const { return at<21>().valid(); }
  ::protozero::ConstBytes log_message() const { return at<21>().as_bytes(); }
  bool has_cc_scheduler_state() const { return at<24>().valid(); }
  ::protozero::ConstBytes cc_scheduler_state() const { return at<24>().as_bytes(); }
  bool has_chrome_user_event() const { return at<25>().valid(); }
  ::protozero::ConstBytes chrome_user_event() const { return at<25>().as_bytes(); }
  bool has_chrome_keyed_service() const { return at<26>().valid(); }
  ::protozero::ConstBytes chrome_keyed_service() const { return at<26>().as_bytes(); }
  bool has_chrome_legacy_ipc() const { return at<27>().valid(); }
  ::protozero::ConstBytes chrome_legacy_ipc() const { return at<27>().as_bytes(); }
  bool has_chrome_histogram_sample() const { return at<28>().valid(); }
  ::protozero::ConstBytes chrome_histogram_sample() const { return at<28>().as_bytes(); }
  bool has_chrome_latency_info() const { return at<29>().valid(); }
  ::protozero::ConstBytes chrome_latency_info() const { return at<29>().as_bytes(); }
  bool has_chrome_frame_reporter() const { return at<32>().valid(); }
  ::protozero::ConstBytes chrome_frame_reporter() const { return at<32>().as_bytes(); }
  bool has_source_location() const { return at<33>().valid(); }
  ::protozero::ConstBytes source_location() const { return at<33>().as_bytes(); }
  bool has_source_location_iid() const { return at<34>().valid(); }
  uint64_t source_location_iid() const { return at<34>().as_uint64(); }
  bool has_chrome_message_pump() const { return at<35>().valid(); }
  ::protozero::ConstBytes chrome_message_pump() const { return at<35>().as_bytes(); }
  bool has_timestamp_delta_us() const { return at<1>().valid(); }
  int64_t timestamp_delta_us() const { return at<1>().as_int64(); }
  bool has_timestamp_absolute_us() const { return at<16>().valid(); }
  int64_t timestamp_absolute_us() const { return at<16>().as_int64(); }
  bool has_thread_time_delta_us() const { return at<2>().valid(); }
  int64_t thread_time_delta_us() const { return at<2>().as_int64(); }
  bool has_thread_time_absolute_us() const { return at<17>().valid(); }
  int64_t thread_time_absolute_us() const { return at<17>().as_int64(); }
  bool has_thread_instruction_count_delta() const { return at<8>().valid(); }
  int64_t thread_instruction_count_delta() const { return at<8>().as_int64(); }
  bool has_thread_instruction_count_absolute() const { return at<20>().valid(); }
  int64_t thread_instruction_count_absolute() const { return at<20>().as_int64(); }
  bool has_legacy_event() const { return at<6>().valid(); }
  ::protozero::ConstBytes legacy_event() const { return at<6>().as_bytes(); }
};

class TrackEvent : public ::protozero::Message {
 public:
  using Decoder = TrackEvent_Decoder;
  enum : int32_t {
    kCategoryIidsFieldNumber = 3,
    kCategoriesFieldNumber = 22,
    kNameIidFieldNumber = 10,
    kNameFieldNumber = 23,
    kTypeFieldNumber = 9,
    kTrackUuidFieldNumber = 11,
    kCounterValueFieldNumber = 30,
    kExtraCounterTrackUuidsFieldNumber = 31,
    kExtraCounterValuesFieldNumber = 12,
    kDebugAnnotationsFieldNumber = 4,
    kTaskExecutionFieldNumber = 5,
    kLogMessageFieldNumber = 21,
    kCcSchedulerStateFieldNumber = 24,
    kChromeUserEventFieldNumber = 25,
    kChromeKeyedServiceFieldNumber = 26,
    kChromeLegacyIpcFieldNumber = 27,
    kChromeHistogramSampleFieldNumber = 28,
    kChromeLatencyInfoFieldNumber = 29,
    kChromeFrameReporterFieldNumber = 32,
    kSourceLocationFieldNumber = 33,
    kSourceLocationIidFieldNumber = 34,
    kChromeMessagePumpFieldNumber = 35,
    kTimestampDeltaUsFieldNumber = 1,
    kTimestampAbsoluteUsFieldNumber = 16,
    kThreadTimeDeltaUsFieldNumber = 2,
    kThreadTimeAbsoluteUsFieldNumber = 17,
    kThreadInstructionCountDeltaFieldNumber = 8,
    kThreadInstructionCountAbsoluteFieldNumber = 20,
    kLegacyEventFieldNumber = 6,
  };
  using LegacyEvent = ::perfetto::protos::pbzero::TrackEvent_LegacyEvent;
  using Type = ::perfetto::protos::pbzero::TrackEvent_Type;
  static const Type TYPE_UNSPECIFIED = TrackEvent_Type_TYPE_UNSPECIFIED;
  static const Type TYPE_SLICE_BEGIN = TrackEvent_Type_TYPE_SLICE_BEGIN;
  static const Type TYPE_SLICE_END = TrackEvent_Type_TYPE_SLICE_END;
  static const Type TYPE_INSTANT = TrackEvent_Type_TYPE_INSTANT;
  static const Type TYPE_COUNTER = TrackEvent_Type_TYPE_COUNTER;
  void add_category_iids(uint64_t value) {
    AppendVarInt(3, value);
  }
  void add_categories(const std::string& value) {
    AppendBytes(22, value.data(), value.size());
  }
  void add_categories(const char* data, size_t size) {
    AppendBytes(22, data, size);
  }
  void set_name_iid(uint64_t value) {
    AppendVarInt(10, value);
  }
  void set_name(const std::string& value) {
    AppendBytes(23, value.data(), value.size());
  }
  void set_name(const char* data, size_t size) {
    AppendBytes(23, data, size);
  }
  void set_type(::perfetto::protos::pbzero::TrackEvent_Type value) {
    AppendTinyVarInt(9, value);
  }
  void set_track_uuid(uint64_t value) {
    AppendVarInt(11, value);
  }
  void set_counter_value(int64_t value) {
    AppendVarInt(30, value);
  }
  void add_extra_counter_track_uuids(uint64_t value) {
    AppendVarInt(31, value);
  }
  void add_extra_counter_values(int64_t value) {
    AppendVarInt(12, value);
  }
  template <typename T = DebugAnnotation> T* add_debug_annotations() {
    return BeginNestedMessage<T>(4);
  }

  template <typename T = TaskExecution> T* set_task_execution() {
    return BeginNestedMessage<T>(5);
  }

  template <typename T = LogMessage> T* set_log_message() {
    return BeginNestedMessage<T>(21);
  }

  template <typename T = ChromeCompositorSchedulerState> T* set_cc_scheduler_state() {
    return BeginNestedMessage<T>(24);
  }

  template <typename T = ChromeUserEvent> T* set_chrome_user_event() {
    return BeginNestedMessage<T>(25);
  }

  template <typename T = ChromeKeyedService> T* set_chrome_keyed_service() {
    return BeginNestedMessage<T>(26);
  }

  template <typename T = ChromeLegacyIpc> T* set_chrome_legacy_ipc() {
    return BeginNestedMessage<T>(27);
  }

  template <typename T = ChromeHistogramSample> T* set_chrome_histogram_sample() {
    return BeginNestedMessage<T>(28);
  }

  template <typename T = ChromeLatencyInfo> T* set_chrome_latency_info() {
    return BeginNestedMessage<T>(29);
  }

  template <typename T = ChromeFrameReporter> T* set_chrome_frame_reporter() {
    return BeginNestedMessage<T>(32);
  }

  template <typename T = SourceLocation> T* set_source_location() {
    return BeginNestedMessage<T>(33);
  }

  void set_source_location_iid(uint64_t value) {
    AppendVarInt(34, value);
  }
  template <typename T = ChromeMessagePump> T* set_chrome_message_pump() {
    return BeginNestedMessage<T>(35);
  }

  void set_timestamp_delta_us(int64_t value) {
    AppendVarInt(1, value);
  }
  void set_timestamp_absolute_us(int64_t value) {
    AppendVarInt(16, value);
  }
  void set_thread_time_delta_us(int64_t value) {
    AppendVarInt(2, value);
  }
  void set_thread_time_absolute_us(int64_t value) {
    AppendVarInt(17, value);
  }
  void set_thread_instruction_count_delta(int64_t value) {
    AppendVarInt(8, value);
  }
  void set_thread_instruction_count_absolute(int64_t value) {
    AppendVarInt(20, value);
  }
  template <typename T = TrackEvent_LegacyEvent> T* set_legacy_event() {
    return BeginNestedMessage<T>(6);
  }

};

class TrackEvent_LegacyEvent_Decoder : public ::protozero::TypedProtoDecoder</*MAX_FIELD_ID=*/19, /*HAS_NONPACKED_REPEATED_FIELDS=*/false> {
 public:
  TrackEvent_LegacyEvent_Decoder(const uint8_t* data, size_t len) : TypedProtoDecoder(data, len) {}
  explicit TrackEvent_LegacyEvent_Decoder(const std::string& raw) : TypedProtoDecoder(reinterpret_cast<const uint8_t*>(raw.data()), raw.size()) {}
  explicit TrackEvent_LegacyEvent_Decoder(const ::protozero::ConstBytes& raw) : TypedProtoDecoder(raw.data, raw.size) {}
  bool has_name_iid() const { return at<1>().valid(); }
  uint64_t name_iid() const { return at<1>().as_uint64(); }
  bool has_phase() const { return at<2>().valid(); }
  int32_t phase() const { return at<2>().as_int32(); }
  bool has_duration_us() const { return at<3>().valid(); }
  int64_t duration_us() const { return at<3>().as_int64(); }
  bool has_thread_duration_us() const { return at<4>().valid(); }
  int64_t thread_duration_us() const { return at<4>().as_int64(); }
  bool has_thread_instruction_delta() const { return at<15>().valid(); }
  int64_t thread_instruction_delta() const { return at<15>().as_int64(); }
  bool has_unscoped_id() const { return at<6>().valid(); }
  uint64_t unscoped_id() const { return at<6>().as_uint64(); }
  bool has_local_id() const { return at<10>().valid(); }
  uint64_t local_id() const { return at<10>().as_uint64(); }
  bool has_global_id() const { return at<11>().valid(); }
  uint64_t global_id() const { return at<11>().as_uint64(); }
  bool has_id_scope() const { return at<7>().valid(); }
  ::protozero::ConstChars id_scope() const { return at<7>().as_string(); }
  bool has_use_async_tts() const { return at<9>().valid(); }
  bool use_async_tts() const { return at<9>().as_bool(); }
  bool has_bind_id() const { return at<8>().valid(); }
  uint64_t bind_id() const { return at<8>().as_uint64(); }
  bool has_bind_to_enclosing() const { return at<12>().valid(); }
  bool bind_to_enclosing() const { return at<12>().as_bool(); }
  bool has_flow_direction() const { return at<13>().valid(); }
  int32_t flow_direction() const { return at<13>().as_int32(); }
  bool has_instant_event_scope() const { return at<14>().valid(); }
  int32_t instant_event_scope() const { return at<14>().as_int32(); }
  bool has_pid_override() const { return at<18>().valid(); }
  int32_t pid_override() const { return at<18>().as_int32(); }
  bool has_tid_override() const { return at<19>().valid(); }
  int32_t tid_override() const { return at<19>().as_int32(); }
};

class TrackEvent_LegacyEvent : public ::protozero::Message {
 public:
  using Decoder = TrackEvent_LegacyEvent_Decoder;
  enum : int32_t {
    kNameIidFieldNumber = 1,
    kPhaseFieldNumber = 2,
    kDurationUsFieldNumber = 3,
    kThreadDurationUsFieldNumber = 4,
    kThreadInstructionDeltaFieldNumber = 15,
    kUnscopedIdFieldNumber = 6,
    kLocalIdFieldNumber = 10,
    kGlobalIdFieldNumber = 11,
    kIdScopeFieldNumber = 7,
    kUseAsyncTtsFieldNumber = 9,
    kBindIdFieldNumber = 8,
    kBindToEnclosingFieldNumber = 12,
    kFlowDirectionFieldNumber = 13,
    kInstantEventScopeFieldNumber = 14,
    kPidOverrideFieldNumber = 18,
    kTidOverrideFieldNumber = 19,
  };
  using FlowDirection = ::perfetto::protos::pbzero::TrackEvent_LegacyEvent_FlowDirection;
  using InstantEventScope = ::perfetto::protos::pbzero::TrackEvent_LegacyEvent_InstantEventScope;
  static const FlowDirection FLOW_UNSPECIFIED = TrackEvent_LegacyEvent_FlowDirection_FLOW_UNSPECIFIED;
  static const FlowDirection FLOW_IN = TrackEvent_LegacyEvent_FlowDirection_FLOW_IN;
  static const FlowDirection FLOW_OUT = TrackEvent_LegacyEvent_FlowDirection_FLOW_OUT;
  static const FlowDirection FLOW_INOUT = TrackEvent_LegacyEvent_FlowDirection_FLOW_INOUT;
  static const InstantEventScope SCOPE_UNSPECIFIED = TrackEvent_LegacyEvent_InstantEventScope_SCOPE_UNSPECIFIED;
  static const InstantEventScope SCOPE_GLOBAL = TrackEvent_LegacyEvent_InstantEventScope_SCOPE_GLOBAL;
  static const InstantEventScope SCOPE_PROCESS = TrackEvent_LegacyEvent_InstantEventScope_SCOPE_PROCESS;
  static const InstantEventScope SCOPE_THREAD = TrackEvent_LegacyEvent_InstantEventScope_SCOPE_THREAD;
  void set_name_iid(uint64_t value) {
    AppendVarInt(1, value);
  }
  void set_phase(int32_t value) {
    AppendVarInt(2, value);
  }
  void set_duration_us(int64_t value) {
    AppendVarInt(3, value);
  }
  void set_thread_duration_us(int64_t value) {
    AppendVarInt(4, value);
  }
  void set_thread_instruction_delta(int64_t value) {
    AppendVarInt(15, value);
  }
  void set_unscoped_id(uint64_t value) {
    AppendVarInt(6, value);
  }
  void set_local_id(uint64_t value) {
    AppendVarInt(10, value);
  }
  void set_global_id(uint64_t value) {
    AppendVarInt(11, value);
  }
  void set_id_scope(const std::string& value) {
    AppendBytes(7, value.data(), value.size());
  }
  void set_id_scope(const char* data, size_t size) {
    AppendBytes(7, data, size);
  }
  void set_use_async_tts(bool value) {
    AppendTinyVarInt(9, value);
  }
  void set_bind_id(uint64_t value) {
    AppendVarInt(8, value);
  }
  void set_bind_to_enclosing(bool value) {
    AppendTinyVarInt(12, value);
  }
  void set_flow_direction(::perfetto::protos::pbzero::TrackEvent_LegacyEvent_FlowDirection value) {
    AppendTinyVarInt(13, value);
  }
  void set_instant_event_scope(::perfetto::protos::pbzero::TrackEvent_LegacyEvent_InstantEventScope value) {
    AppendTinyVarInt(14, value);
  }
  void set_pid_override(int32_t value) {
    AppendVarInt(18, value);
  }
  void set_tid_override(int32_t value) {
    AppendVarInt(19, value);
  }
};

} // Namespace.
} // Namespace.
} // Namespace.
#endif  // Include guard.
