# Copyright (C) 2017 The Android Open Source Project
#
# Licensed under the Apache License, Version 2.0 (the "License");
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#
#      http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.

source_set("protozero") {
  public_deps = [ "../base" ]
  sources = [
    "contiguous_memory_range.h",
    "copyable_ptr.h",
    "cpp_message_obj.h",
    "field.h",
    "message.h",
    "message_arena.h",
    "message_handle.h",
    "packed_repeated_fields.h",
    "proto_decoder.h",
    "proto_utils.h",
    "root_message.h",
    "scattered_heap_buffer.h",
    "scattered_stream_null_delegate.h",
    "scattered_stream_writer.h",
    "static_buffer.h",
  ]
}
