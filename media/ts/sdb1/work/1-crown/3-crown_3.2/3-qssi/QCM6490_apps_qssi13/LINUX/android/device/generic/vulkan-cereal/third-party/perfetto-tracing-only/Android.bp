package {
    // See: http://go/android-license-faq
    // A large-scale-change added 'default_applicable_licenses' to import
    // all of the 'license_kinds' from "device_generic_vulkan-cereal_license"
    // to get the below license kinds:
    //   SPDX-license-identifier-Apache-2.0
    default_applicable_licenses: ["device_generic_vulkan-cereal_license"],
}

cc_library_static {
    name: "perfetto-tracing-only",
    defaults: [ "gfxstream_defaults" ],
    srcs: [ "perfetto-tracing-only.cpp" ],
    whole_static_libs: [
        "perfetto-libprotozero",
    ],
    export_include_dirs: [
        ".",
    ],
}
