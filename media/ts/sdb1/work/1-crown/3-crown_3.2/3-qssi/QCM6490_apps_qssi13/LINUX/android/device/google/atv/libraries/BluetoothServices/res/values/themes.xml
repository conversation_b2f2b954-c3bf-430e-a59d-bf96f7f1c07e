<?xml version="1.0" encoding="utf-8"?>
<!--
  ~ Copyright (C) 2021 The Android Open Source Project
  ~
  ~ Licensed under the Apache License, Version 2.0 (the "License");
  ~ you may not use this file except in compliance with the License.
  ~ You may obtain a copy of the License at
  ~
  ~      http://www.apache.org/licenses/LICENSE-2.0
  ~
  ~ Unless required by applicable law or agreed to in writing, software
  ~ distributed under the License is distributed on an "AS IS" BASIS,
  ~ WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
  ~ See the License for the specific language governing permissions and
  ~ limitations under the License.
  -->
<resources>
  <style name="Theme.Settings.SidePanel" parent="@style/Theme.Leanback">
    <item name="android:windowBackground">@android:color/transparent</item>
    <item name="android:windowIsTranslucent">true</item>
    <item name="android:fontFamily">sans-serif</item>
    <item name="android:colorPrimary">@color/settings_screen_title_color</item>
    <item name="android:colorAccent">@color/settings_category_text_color</item>
    <item name="preferenceTheme">@style/PreferenceThemeOverlayLeanback</item>
  </style>

  <style name="Theme.Settings.SidePanelDim" parent="@style/Theme.Leanback">
    <item name="android:windowBackground">@android:color/transparent</item>
    <item name="android:windowIsTranslucent">true</item>
    <item name="android:backgroundDimAmount">.8</item>
    <item name="android:backgroundDimEnabled">true</item>
    <item name="android:fontFamily">sans-serif</item>
    <item name="android:colorPrimary">@color/settings_screen_title_color</item>
    <item name="android:colorAccent">@color/settings_category_text_color</item>
    <item name="preferenceTheme">@style/PreferenceThemeOverlayLeanback</item>
  </style>

  <style name="SettingsStyle.TextAppearanceMedium" parent="@android:style/TextAppearance.Holo.Medium">
    <item name="android:textSize">@dimen/guidance_description_footer_text_size</item>
    <item name="android:fontFamily">sans-serif</item>
  </style>
  <style name="SettingsStyle.TextAppearanceLarge" parent="@android:style/TextAppearance.Holo.Large">
    <item name="android:textSize">@dimen/guidance_title_text_size</item>
    <item name="android:fontFamily">sans-serif</item>
  </style>
  <style name="SettingsStyle.TextAppearanceSmall" parent="@android:style/TextAppearance.Holo.Medium">
    <item name="android:textSize">@dimen/guidance_description_hint_text_size</item>
    <item name="android:fontFamily">sans-serif</item>
  </style>

  <style name="ResponseGuidedStepTheme" parent="Theme.Leanback.GuidedStep">
    <item name="guidanceTitleStyle">@style/ResponseGuidanceTitleStyle</item>
  </style>

</resources>
