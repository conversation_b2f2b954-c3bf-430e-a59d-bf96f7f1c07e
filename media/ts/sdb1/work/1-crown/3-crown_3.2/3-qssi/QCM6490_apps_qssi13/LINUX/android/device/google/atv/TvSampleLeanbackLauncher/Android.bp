package {
    // See: http://go/android-license-faq
    // A large-scale-change added 'default_applicable_licenses' to import
    // all of the 'license_kinds' from "device_google_atv_license"
    // to get the below license kinds:
    //   SPDX-license-identifier-Apache-2.0
    default_applicable_licenses: ["device_google_atv_license"],
}

prebuilt_etc {
    name: "privapp_whitelist_com.example.sampleleanbacklauncher",
    product_specific: true,
    sub_dir: "permissions",
    src: "com.example.sampleleanbacklauncher.xml",
    filename_from_src: true,
}

android_app_import {
  name: "TvSampleLeanbackLauncher",
  apk: "TvSampleLeanbackLauncher.apk",
  presigned: true,
  privileged: true,
  product_specific: true,
  required: ["privapp_whitelist_com.example.sampleleanbacklauncher"],
  dex_preopt: {
    enabled: false,
  },
}
