// Copyright (C) 2020 The Android Open Source Project
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//      http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

package {
    // See: http://go/android-license-faq
    // A large-scale-change added 'default_applicable_licenses' to import
    // all of the 'license_kinds' from "device_google_atv_license"
    // to get the below license kinds:
    //   SPDX-license-identifier-Apache-2.0
    default_applicable_licenses: ["device_google_atv_license"],
}

cc_library_static {
  name: "audio_proxy_service_util",
  vendor_available: true,
  host_supported: true,
  srcs: [
    "RingBufferUtil.cpp",
    "ServiceConfig.cpp",
  ],
  shared_libs: [
    "libbase",
  ],
}

cc_defaults {
  name: "audio_proxy_service_defaults",
  vendor: true,
  relative_install_path: "hw",

  srcs: [
    "AudioProxyImpl.cpp",
    "BusOutputStream.cpp",
    "BusStreamProvider.cpp",
    "DeviceImpl.cpp",
    "DevicesFactoryImpl.cpp",
    "DummyBusOutputStream.cpp",
    "RemoteBusOutputStream.cpp",
    "StreamOutImpl.cpp",
    "WriteThread.cpp",
    "main.cpp",
  ],

  shared_libs: [
    "device.google.atv.audio_proxy-aidl-V1-ndk",
    "libbase",
    "libbinder_ndk",
    "libhidlbase",
    "libcutils",
    "libfmq",
    "libutils",
  ],

  static_libs: [
    "audio_proxy_service_util",
  ],

  header_libs: [
    "libaudio_system_headers",
  ],

  cflags: [
    "-include ../common/AudioProxyVersionMacro.h",
    "-Werror",
    "-Wthread-safety",
    "-Wno-unused-parameter",
  ],
}

cc_binary {
  name: "device.google.atv.audio_proxy@5.1-service",

  defaults: [
    "audio_proxy_service_defaults"
  ],

  init_rc: [
    "<EMAIL>",
  ],

  vintf_fragments: [ "manifest_audio_proxy_5_0.xml" ],

  shared_libs: [
    "android.hardware.audio@5.0",
    "android.hardware.audio.common@5.0",
  ],

  cflags: [
    "-DMAJOR_VERSION=5",
    "-DMINOR_VERSION=0",
  ],
}

cc_binary {
  name: "device.google.atv.audio_proxy@6.0-service",

  defaults: [
    "audio_proxy_service_defaults"
  ],

  init_rc: [
    "<EMAIL>",
  ],

  vintf_fragments: [ "manifest_audio_proxy_6_0.xml" ],

  shared_libs: [
    "android.hardware.audio@6.0",
    "android.hardware.audio.common@6.0",
  ],

  cflags: [
    "-DMAJOR_VERSION=6",
    "-DMINOR_VERSION=0",
  ],
}

cc_binary {
  name: "device.google.atv.audio_proxy@7.0-service",

  defaults: [
    "audio_proxy_service_defaults"
  ],

  init_rc: [
    "<EMAIL>",
  ],

  vintf_fragments: [ "manifest_audio_proxy_7_0.xml" ],

  shared_libs: [
    "android.hardware.audio@7.0",
    "android.hardware.audio.common@7.0",
  ],

  cflags: [
    "-DMAJOR_VERSION=7",
    "-DMINOR_VERSION=0",
  ],
}

cc_binary {
  name: "device.google.atv.audio_proxy@7.1-service",

  defaults: [
    "audio_proxy_service_defaults"
  ],

  init_rc: [
    "<EMAIL>",
  ],

  vintf_fragments: [ "manifest_audio_proxy_7_1.xml" ],

  shared_libs: [
    "android.hardware.audio@7.1",
    "android.hardware.audio@7.0",
    "android.hardware.audio.common@7.0",
  ],

  cflags: [
    "-DMAJOR_VERSION=7",
    "-DMINOR_VERSION=1",
  ],
}

cc_test {
  name: "audio_proxy_service_util_test",
  host_supported: true,

  srcs: [
    "RingBufferUtilTest.cpp",
    "ServiceConfigTest.cpp",
  ],
  static_libs: [
    "audio_proxy_service_util",
    "libbase",
    "libgtest",
  ],

  cflags: [
    // Suppress the warning to make ServiceConfigTest easier.
    "-Wno-writable-strings",
  ],
}
