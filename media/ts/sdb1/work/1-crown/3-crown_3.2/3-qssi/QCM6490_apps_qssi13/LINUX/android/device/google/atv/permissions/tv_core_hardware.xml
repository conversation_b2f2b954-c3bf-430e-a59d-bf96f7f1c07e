<?xml version="1.0" encoding="utf-8"?>
<!-- Copyright (C) 2014 The Android Open Source Project

     Licensed under the Apache License, Version 2.0 (the "License");
     you may not use this file except in compliance with the License.
     You may obtain a copy of the License at

         http://www.apache.org/licenses/LICENSE-2.0

     Unless required by applicable law or agreed to in writing, software
     distributed under the License is distributed on an "AS IS" BASIS,
     WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
     See the License for the specific language governing permissions and
     limitations under the License.
-->

<permissions>

    <!-- These are the hardware components that all television devices must
         include.  Devices with optional hardware must also include extra hardware
         files.
    -->
    <feature name="android.hardware.audio.output" />
    <feature name="android.hardware.location" />
    <feature name="android.hardware.location.network" />
    <feature name="android.hardware.screen.landscape" />
    <feature name="android.hardware.type.television" />

    <feature name="android.software.backup" />
    <feature name="android.software.leanback" />
    <feature name="android.software.leanback_only" />
    <feature name="android.software.live_tv" />
    <feature name="android.software.picture_in_picture" notLowRam="true" />
    <feature name="android.software.activities_on_secondary_displays" notLowRam="true" />
    <feature name="android.software.voice_recognizers" />
    <feature name="android.software.input_methods" />
    <feature name="android.software.autofill" />

    <feature name="android.software.cts" />
    <feature name="android.hardware.security.model.compatible" />

</permissions>
