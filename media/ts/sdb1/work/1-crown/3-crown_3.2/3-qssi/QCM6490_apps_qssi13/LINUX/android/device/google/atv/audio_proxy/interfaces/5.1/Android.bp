// This file is autogenerated by hidl-gen -Landroidbp.

package {
    // See: http://go/android-license-faq
    // A large-scale-change added 'default_applicable_licenses' to import
    // all of the 'license_kinds' from "device_google_atv_license"
    // to get the below license kinds:
    //   legacy_notice
    default_applicable_licenses: ["device_google_atv_license"],
}

hidl_interface {
    name: "device.google.atv.audio_proxy@5.1",
    root: "device.google.atv.audio_proxy",
    system_ext_specific: true,
    srcs: [
        "IAudioProxyDevicesManager.hal",
        "IAudioProxyStreamOut.hal",
        "IBusDevice.hal",
        "IStreamEventListener.hal",
    ],
    interfaces: [
        "android.hardware.audio.common@5.0",
        "android.hardware.audio@5.0",
        "android.hidl.base@1.0",
        "android.hidl.safe_union@1.0",
        "device.google.atv.audio_proxy@5.0",
    ],
    gen_java: false,
}
