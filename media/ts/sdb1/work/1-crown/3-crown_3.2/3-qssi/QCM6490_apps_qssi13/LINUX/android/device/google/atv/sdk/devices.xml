<?xml version="1.0" encoding="UTF-8"?>

<d:devices xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
           xmlns:d="http://schemas.android.com/sdk/devices/1">

    <d:device>
        <d:name>Android TV (1080p)</d:name>
        <d:id>tv_1080p</d:id>
        <d:manufacturer>Google</d:manufacturer>
        <d:hardware>
            <d:screen>
                <d:screen-size>xlarge</d:screen-size>
                <d:diagonal-length>55.0</d:diagonal-length>
                <d:pixel-density>xhdpi</d:pixel-density>
                <d:screen-ratio>long</d:screen-ratio>
                <d:dimensions>
                    <d:x-dimension>1920</d:x-dimension>
                    <d:y-dimension>1080</d:y-dimension>
                </d:dimensions>
                <d:xdpi>40.05</d:xdpi>
                <d:ydpi>40.05</d:ydpi>
                <d:touch>
                    <d:multitouch>none</d:multitouch>
                    <d:mechanism>notouch</d:mechanism>
                    <d:screen-type>notouch</d:screen-type>
                </d:touch>
            </d:screen>
            <d:networking>
                Bluetooth
                Wifi
                NFC
            </d:networking>
            <d:sensors>
                LightSensor
                GPS
            </d:sensors>
            <d:mic>true</d:mic>
            <d:keyboard>qwerty</d:keyboard>
            <d:nav>dpad</d:nav>
            <d:ram unit="GiB">2</d:ram>
            <d:buttons>hard</d:buttons>
            <d:internal-storage unit="KiB">7811891</d:internal-storage>
            <d:removable-storage unit="MiB">0</d:removable-storage>
            <d:cpu>Generic CPU</d:cpu>
            <d:gpu>Generic GPU</d:gpu>
            <d:abi>
                armeabi-v7a
                x86
            </d:abi>
            <d:dock/>
            <d:power-type>plugged-in</d:power-type>
            <d:skin>tv_1080p</d:skin>
        </d:hardware>
        <d:software>
            <d:api-level>20-</d:api-level>
            <d:live-wallpaper-support>true</d:live-wallpaper-support>
            <d:bluetooth-profiles/>
            <d:gl-version>2.0</d:gl-version>
            <d:gl-extensions/>
            <d:status-bar>false</d:status-bar>
        </d:software>
        <d:state name="Landscape" default="true">
            <d:description>The device in landscape orientation</d:description>
            <d:screen-orientation>land</d:screen-orientation>
            <d:keyboard-state>keyshidden</d:keyboard-state>
            <d:nav-state>navexposed</d:nav-state>
        </d:state>
        <d:state name="Landscape with keyboard">
            <d:description>The device in landscape orientation with a keyboard open</d:description>
            <d:screen-orientation>land</d:screen-orientation>
            <d:keyboard-state>keysexposed</d:keyboard-state>
            <d:nav-state>navexposed</d:nav-state>
        </d:state>
        <d:tag-id>android-tv</d:tag-id>
    </d:device>

    <d:device>
        <d:name>Android TV (720p)</d:name>
        <d:id>tv_720p</d:id>
        <d:manufacturer>Google</d:manufacturer>
        <d:hardware>
            <d:screen>
                <d:screen-size>xlarge</d:screen-size>
                <d:diagonal-length>55.0</d:diagonal-length>
                <d:pixel-density>tvdpi</d:pixel-density>
                <d:screen-ratio>long</d:screen-ratio>
                <d:dimensions>
                    <d:x-dimension>1280</d:x-dimension>
                    <d:y-dimension>720</d:y-dimension>
                </d:dimensions>
                <d:xdpi>26.70</d:xdpi>
                <d:ydpi>26.70</d:ydpi>
                <d:touch>
                    <d:multitouch>none</d:multitouch>
                    <d:mechanism>notouch</d:mechanism>
                    <d:screen-type>notouch</d:screen-type>
                </d:touch>
            </d:screen>
            <d:networking>
                Bluetooth
                Wifi
                NFC
            </d:networking>
            <d:sensors>
                LightSensor
                GPS
            </d:sensors>
            <d:mic>true</d:mic>
            <d:keyboard>qwerty</d:keyboard>
            <d:nav>dpad</d:nav>
            <d:ram unit="GiB">2</d:ram>
            <d:buttons>hard</d:buttons>
            <d:internal-storage unit="KiB">7811891</d:internal-storage>
            <d:removable-storage unit="MiB">0</d:removable-storage>
            <d:cpu>Generic CPU</d:cpu>
            <d:gpu>Generic GPU</d:gpu>
            <d:abi>
                armeabi-v7a
                x86
            </d:abi>
            <d:dock/>
            <d:power-type>plugged-in</d:power-type>
            <d:skin>tv_720p</d:skin>
        </d:hardware>
        <d:software>
            <d:api-level>20-</d:api-level>
            <d:live-wallpaper-support>true</d:live-wallpaper-support>
            <d:bluetooth-profiles/>
            <d:gl-version>2.0</d:gl-version>
            <d:gl-extensions/>
            <d:status-bar>false</d:status-bar>
        </d:software>
        <d:state name="Landscape" default="true">
            <d:description>The device in landscape orientation</d:description>
            <d:screen-orientation>land</d:screen-orientation>
            <d:keyboard-state>keyshidden</d:keyboard-state>
            <d:nav-state>navexposed</d:nav-state>
        </d:state>
        <d:state name="Landscape with keyboard">
            <d:description>The device in landscape orientation with a keyboard open</d:description>
            <d:screen-orientation>land</d:screen-orientation>
            <d:keyboard-state>keysexposed</d:keyboard-state>
            <d:nav-state>navexposed</d:nav-state>
        </d:state>
        <d:tag-id>android-tv</d:tag-id>
    </d:device>

</d:devices>
