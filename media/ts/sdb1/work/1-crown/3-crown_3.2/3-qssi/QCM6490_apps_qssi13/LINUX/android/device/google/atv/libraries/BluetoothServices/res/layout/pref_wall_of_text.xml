<?xml version="1.0" encoding="utf-8"?>
<!--
  ~ Copyright (C) 2021 The Android Open Source Project
  ~
  ~ Licensed under the Apache License, Version 2.0 (the "License");
  ~ you may not use this file except in compliance with the License.
  ~ You may obtain a copy of the License at
  ~
  ~      http://www.apache.org/licenses/LICENSE-2.0
  ~
  ~ Unless required by applicable law or agreed to in writing, software
  ~ distributed under the License is distributed on an "AS IS" BASIS,
  ~ WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
  ~ See the License for the specific language governing permissions and
  ~ limitations under the License.
  -->

<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:id="@+id/container"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:background="?android:attr/selectableItemBackground"
    android:clickable="false"
    android:focusable="false"
    android:descendantFocusability="blocksDescendants"
    android:orientation="horizontal"
    android:paddingStart="@dimen/lb_preference_item_padding_start"
    android:paddingEnd="@dimen/lb_preference_item_padding_end" >

  <LinearLayout android:layout_width="0dp"
      android:layout_height="wrap_content"
      android:layout_weight="1"
      android:orientation="vertical">
    <Space android:layout_width="0dp" android:layout_height="@dimen/lb_preference_item_text_space_top" />
    <TextView
        android:id="@android:id/title"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:fontFamily="sans-serif"
        android:textColor="@color/lb_preference_item_secondary_text_color"
        android:textSize="@dimen/lb_preference_item_secondary_text_size" />
    <Space android:layout_width="0dp" android:layout_height="@dimen/lb_preference_item_text_space_bottom" />
  </LinearLayout>

</LinearLayout>