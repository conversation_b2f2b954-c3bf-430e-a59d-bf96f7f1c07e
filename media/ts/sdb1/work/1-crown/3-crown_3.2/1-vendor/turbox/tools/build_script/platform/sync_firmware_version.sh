#!/bin/bash
#################################################################################
# Author: Auto-generated script for firmware version synchronization
# Copyright (c) 2024
# Description: Synchronize HardWareVersion from config_pf.rc to system.prop
#################################################################################

# 获取脚本所在目录
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
META_DIR="$(cd "$SCRIPT_DIR/../../../.." && pwd)"

# 配置文件路径
CONFIG_PF_RC="$SCRIPT_DIR/config_pf.rc"
SYSTEM_PROP_PATH="$META_DIR/3-qssi/QCM6490_apps_qssi13/LINUX/android/device/qcom/qssi/thundercomm/system.prop"

# 日志函数
log_info() {
    echo "[$(date '+%Y-%m-%d %H:%M:%S')] INFO: $1"
}

log_error() {
    echo "[$(date '+%Y-%m-%d %H:%M:%S')] ERROR: $1" >&2
}

# 检查文件是否存在
check_files() {
    if [ ! -f "$CONFIG_PF_RC" ]; then
        log_error "Config file not found: $CONFIG_PF_RC"
        return 1
    fi
    
    if [ ! -f "$SYSTEM_PROP_PATH" ]; then
        log_error "System.prop file not found: $SYSTEM_PROP_PATH"
        return 1
    fi
    
    return 0
}

# 从config_pf.rc中提取HardWareVersion
extract_hardware_version() {
    local hardware_version
    
    # 加载配置文件
    source "$CONFIG_PF_RC"
    
    # 调用init_platform_env函数来设置变量
    if declare -f init_platform_env > /dev/null; then
        init_platform_env
    fi
    
    # 获取HardWareVersion
    hardware_version="$HardWareVersion"
    
    if [ -z "$hardware_version" ]; then
        log_error "HardWareVersion not found in $CONFIG_PF_RC"
        return 1
    fi
    
    echo "$hardware_version"
    return 0
}

# 更新system.prop中的firmware version
update_system_prop() {
    local new_version="$1"
    local temp_file
    
    if [ -z "$new_version" ]; then
        log_error "New version is empty"
        return 1
    fi
    
    # 创建临时文件
    temp_file=$(mktemp)
    
    # 备份原文件
    cp "$SYSTEM_PROP_PATH" "${SYSTEM_PROP_PATH}.backup.$(date +%Y%m%d_%H%M%S)"
    
    # 更新firmware version
    if grep -q "^ro.build.version.firmware=" "$SYSTEM_PROP_PATH"; then
        # 如果存在，则替换
        sed "s/^ro.build.version.firmware=.*/ro.build.version.firmware=$new_version/" "$SYSTEM_PROP_PATH" > "$temp_file"
    else
        # 如果不存在，则添加
        cp "$SYSTEM_PROP_PATH" "$temp_file"
        echo "ro.build.version.firmware=$new_version" >> "$temp_file"
    fi
    
    # 替换原文件
    mv "$temp_file" "$SYSTEM_PROP_PATH"
    
    log_info "Updated ro.build.version.firmware to: $new_version"
    return 0
}

# 主函数
main() {
    local hardware_version
    
    log_info "Starting firmware version synchronization..."
    
    # 检查文件
    if ! check_files; then
        exit 1
    fi
    
    # 提取硬件版本
    hardware_version=$(extract_hardware_version)
    if [ $? -ne 0 ] || [ -z "$hardware_version" ]; then
        log_error "Failed to extract hardware version"
        exit 1
    fi
    
    log_info "Found HardWareVersion: $hardware_version"
    
    # 更新system.prop
    if update_system_prop "$hardware_version"; then
        log_info "Firmware version synchronization completed successfully"
    else
        log_error "Failed to update system.prop"
        exit 1
    fi
}

# 执行主函数
main "$@"
