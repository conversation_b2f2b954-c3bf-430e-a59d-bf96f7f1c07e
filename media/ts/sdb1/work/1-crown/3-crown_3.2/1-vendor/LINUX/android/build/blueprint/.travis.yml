language: go

go:
    - "1.10"
    - "1.11"
    - "1.12"
    - "1.13"

cache:
    directories:
        - $HOME/ninjabin

install:
    - ./.travis.install-ninja.sh
    - export PATH=$PATH:~/ninjabin

before_script:
    - source .travis.fix-fork.sh

script:
    - export GOROOT=$(go env GOROOT)
    - ./.travis.gofmt.sh
    - go test ./...
    - go test -race -short ./...
    - ./tests/test.sh
    - ./tests/test_tree_tests.sh
    - ./tests/test_tree_tests.sh -t
